<ion-header class="inside-page">
  <ion-toolbar>
    <ion-buttons slot="start">
      <!--<ion-back-button (click)="onClose()"></ion-back-button>-->

      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>在線點名</ion-title>
    <ion-buttons slot="end">
      <ion-button [hidden]="false" (click)="onSave()">
        保存
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<div class="act-info">
  <div class="act-info-item"><span class="label">活動名稱：</span>{{ data.actName }}</div>
  <div class="act-info-item"><span class="label">時間：</span>{{ day }} {{ yearData.actStartTime }}-{{ yearData.actEndTime }}</div>
  <div class="act-info-item"><span class="label">地點：</span>{{ yearData.actOtherPlace }}</div>
  <div class="act-tips">* 點擊學生信息可直接將其設為出席情況</div>
  <div class="act-tips">* 如需選擇其它出席情況，可點擊右方按鈕</div>
  <div class="batch-set">
    <ion-button [color]="allSetClass" (click)="onAllAttend()">{{ allSetStr }}</ion-button>
  </div>
</div>
<ion-content >
  <!--<div class="stu-list">-->
  <!--  -->
  <!--</div>-->
  <ion-list>
    <ion-item lines="full" button *ngFor="let stu of item.students;let i = index" >
      <div class="stu-item">
        <div class="index">{{ i + 1 }}</div>
        <div class="info" (click)="onchangeStatusToAttend(stu, i)">
          <div class="info-1">
            <span>{{ stu.stuUname }}</span>
            <i class="iconfont icon-nv" *ngIf="stu.stuGender === 'F'"></i>
            <i class="iconfont icon-nan" *ngIf="stu.stuGender === 'M'"></i>
            <span>{{ stu.stuClassName }}</span>
            <span>({{ stu.stuClassNo }})</span>
          </div>
          <div class="info-2">
            <span>緊急聯絡電話號碼：</span>
            <span>{{ stu.stuPhoneNum }}</span>
          </div>
          <div class="info-3">
            <span>歸程方式：</span>
            <span>{{ stu.backInfo }}</span>
          </div>
        </div>
        <div class="status">
          <ion-button [color]="getStatusColor(stu)" (click)="onChangeStatus(stu, i)">{{ getStatus(stu) }}</ion-button>
        </div>
      </div>
    </ion-item>
  </ion-list>
</ion-content>
