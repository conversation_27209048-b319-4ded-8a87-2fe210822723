import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {Routes, RouterModule} from '@angular/router';

import {IonicModule} from '@ionic/angular';

import {TranslateModule} from '@ngx-translate/core';
import {InfoLabelModule} from '@app/components/info-label/info-label.module';
import {StudentStarActivitySearchPage} from './student-star-activity-search.page';

const routes: Routes = [
    {
        path: '',
        component: StudentStarActivitySearchPage
    }
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        RouterModule.forChild(routes),
        TranslateModule,
        InfoLabelModule
    ],
    // declarations: [StudentStarActivitySearchPage]
})
export class StudentStarActivitySearchPageModule {
}
