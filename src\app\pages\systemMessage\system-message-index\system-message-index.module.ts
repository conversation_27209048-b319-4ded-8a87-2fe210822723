import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { SystemMessageIndexPage } from './system-message-index.page';

const routes: Routes = [
  {
    path: '',
    component: SystemMessageIndexPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [SystemMessageIndexPage]
})
export class SystemMessageIndexPageModule {}
