import {Component, OnInit, ChangeDetectorRef, Input, Output, EventEmitter} from '@angular/core';
import {ActionSheetController, ModalController, NavController, Platform} from '@ionic/angular';
import {ActivatedRoute} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import {StarService} from '@services/api/star.service';
import {StorageService} from '@services/storage/storage.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {TranslateService} from '@ngx-translate/core';
import {StudentStarActivitySearchPage} from '@pages/tab-student/student-star-activity-search/student-star-activity-search.page';
import {UtilsService} from '@app/utils/utils.service';
import {Events} from '@app/services/events/event.service';

@Component({
    selector: 'app-student-star-input',
    templateUrl: './student-star-input.page.html',
    styleUrls: ['./student-star-input.page.scss'],
})
export class StudentStarInputPage implements OnInit {
    @Input() domId = 'tudent-star-input-modal'
    @Input() project_id: any = ''
    @Input() stu_id: any = ''
    @Input() post_data: any = {}
    @Input() can_edit: any = false
    @Output() public reload = new EventEmitter();

    public data: any = {}
    public submitData: any = {}
    public user: any = {}
    public hasStarCount: any = 0
    public textList: any = []
    public addTextList: any = []
    public selectedText: any = {}
    public selectedActivities: any = []

    constructor(
        private platform: Platform,
        public utils: UtilsService,
        private routeInfo: ActivatedRoute,
        public nav: NavController,
        private loadingService: LoadingService,
        public starService: StarService,
        public actionSheetController: ActionSheetController,
        private modalCtrl: ModalController,
        private storageService: StorageService,
        private nativeStorage: NativeStorage,
        public translate: TranslateService,
        protected cdr: ChangeDetectorRef,
        protected events: Events,
    ) {
    }

    ngOnInit() {
        this.platform.ready().then(async () => {
            await this.loadData()
            this.user = await this.nativeStorage.getItem('userInfo')
        })
    }

    async loadData() {
        await this.loadingService.start()
        try {
            const res: any = await this.starService.getStarProject({project_id: this.project_id, stu_id: this.stu_id})
            this.data = res
            this.submitData = this.post_data
            this.selectedActivities = this.submitData.eca_content
            this.selectedActivities = this.submitData.eca_content
            this.hasStarCount = this.submitData.num
            await this.initTextList()
        } catch (e) {
            console.error(e)
        }

        await this.loadingService.end()
    }

    async initTextList() {
        try {
            const res: any = await this.starService.fetchTextList({
                year_id: this.data.year_id,
                project_id: this.project_id
            })
            console.log(res)
            this.textList = res

            this.selectedText = {}
            this.textList.forEach(textItem => {
                this.selectedText['tl_' + textItem.id] = false
            })

            this.addTextList = []
            if (this.submitData.text_array.length > 0) {
                this.submitData.text_array.forEach(textObj => {
                    this.selectedText['tl_' + textObj.text_id] = true
                    if (isNaN(textObj.text_id)) {
                        this.addTextList.push({
                            text_id: textObj.text_id,
                            detail: textObj.detail
                        })
                    }
                })
            }
        } catch (e) {
            console.error(e)
        }
    }

    addTextItem() {
        const randomStr = this.randomString(5)
        this.addTextList.push({
            text_id: randomStr,
            detail: '',
        })
        this.selectedText['tl_' + randomStr] = false
    }

    removeTextItem(index, text_id) {
        if (this.addTextList.length > 0) {
            this.addTextList.splice(index, 1)
        }
        this.selectedText['tl_' + text_id] = false
    }

    textListChanged($event) {
        /*if ($event.detail.checked) {
            for (const [key, value] of Object.entries(this.selectedText)) {
                const tmpKey = key.split('_')[1]
                if (isNaN(Number(tmpKey))) {
                    this.selectedText[key] = false
                }
            }
        }*/
    }

    addTextChanged($event) {
        // if ($event.detail.checked) {
        //     for (const [key, value] of Object.entries(this.selectedText)) {
        //         const tmpKey = key.split('_')[1]
        //         if (!isNaN(Number(tmpKey))) {
        //             this.selectedText[key] = false
        //         }
        //     }
        // }
    }

    randomString(len) {
        len = len || 32;
        const $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz';
        /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
        const maxPos = $chars.length;
        let str = '';
        for (let i = 0; i < len; i++) {
            str += $chars.charAt(Math.floor(Math.random() * maxPos));
        }
        return str;
    }

    async searchActivity() {
        const returnData = await this.showSearchActivity()
        console.log(returnData, 'returnData')
        if (returnData.length === 0) {
            return
        }
        // 不重複加入
        returnData.forEach(item => {
            const tmpIndex = this.selectedActivities.findIndex(tmp => tmp.active_id === item.active_id)
            if (tmpIndex === -1) {
                this.selectedActivities.push(item)
            }
        })
    }

    removeActivity(index) {
        if (this.selectedActivities.length > 0) {
            this.selectedActivities.splice(index, 1)
        }
    }

    async showSearchActivity() {
        const modal = await this.modalCtrl.create({
            component: StudentStarActivitySearchPage,
            componentProps: {
                domId: 'search-activity-modal',
                year_id: this.data.year_id,
                stu_id: this.stu_id,
                semester: this.data.semester,
            },
            id: 'search-activity-modal'
        })
        await modal.present()
        const res: any = await modal.onDidDismiss()
        console.log(res, 'dismiss')
        if (res.data.save) {
            return res.data.data
        }
        return {}
    }

    getStarClass(starIndex) {
        if (starIndex >= this.hasStarCount) {
            return 'list-star iconfont icon-ios-star-outline'
        } else {
            return 'list-star iconfont icon-ios-star'
        }
    }

    changeStarCount(starCount) {
        if (this.can_edit) {
            if (this.hasStarCount === starCount) {
                this.hasStarCount = this.hasStarCount - 1
            } else {
                this.hasStarCount = starCount
            }
            this.submitData.num = this.hasStarCount
        }
    }

    onClose(save = false) {
        this.modalCtrl.dismiss({
            dismissed: true,
            save
        });
    }

    async onSave() {
        // 根據類型處理相關數據
        let isEmpty = true
        let msg = '請選擇內容'
        if (this.submitData.num === '' || this.submitData.num === 0) {
            isEmpty = false
        }
        switch (this.data.type_value) {
            case 'NAME':
            case 'AWARD':
            case 'DESCRIBE':
                const text_array = []

                for (const [key, value] of Object.entries(this.selectedText)) {
                    if (value) {
                        const tmpKey = key.split('_')[1]
                        if (isNaN(Number(tmpKey))) {
                            const tempIndex = this.addTextList.findIndex(temp => temp.text_id === tmpKey)
                            if (tempIndex > -1) {
                                if (isEmpty) {
                                    if (this.addTextList[tempIndex].detail === '') {
                                        msg = '請填寫內容'
                                    } else {
                                        isEmpty = false
                                    }
                                }
                                text_array.push(this.addTextList[tempIndex])
                            }
                        } else {
                            text_array.push({
                                text_id: Number(tmpKey),
                                detail: '',
                            })
                            isEmpty = false
                        }
                    }
                }
                this.submitData.text_array = text_array
                break
            case 'ECA':
                this.submitData.eca_content = this.selectedActivities
                if (isEmpty) {
                    if (this.selectedActivities.length > 0) {
                        isEmpty = false
                    }
                }
                break
            default:
                isEmpty = false
        }
        if (this.submitData.num === '') {
            this.submitData.num = 0
        }

        if (isEmpty) {
            await this.utils.showMsg(msg)
            return
        }

        this.modalCtrl.dismiss({
            dismissed: true,
            save: true,
            data: this.submitData
        });
    }
}

