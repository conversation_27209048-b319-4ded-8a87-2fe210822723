ion-content {
  --background: var(--content-page-background);
  ion-list {
    background: var(--content-page-background);
    color: #606266;
    ion-item-group {
      margin-bottom: 5px;
      ion-item {
        word-break: keep-all;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
        ion-label {
          font-size: 14px;
        }
        ion-text {
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: calc(100% - 5px);
          width: 100%;
          text-align: right;
        }
        &.item-accordion {
          //padding-top: 0;
          //padding-bottom: 0;
          -webkit-transition: 0.09s all linear;
          transition: 0.09s all linear;
          //height: 50px;
          ion-text {
            padding-left: 1em;
            white-space: pre-wrap;
            line-height: 20px;
            text-align: left;
            color: #A1A1A1;
          }
          &.hidden {
            //height: 0px;
          }
        }
        i, ion-icon {
          height: 20px;
          width: 20px;
          color: #c0c0c0;
        }
      }
    }
  }
}
