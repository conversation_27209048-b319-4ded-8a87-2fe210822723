import { Injectable } from '@angular/core';
import { SQLite, SQLiteObject } from '@ionic-native/sqlite/ngx';
@Injectable({
  providedIn: 'root'
})
export class SqlService {

  public db: SQLiteObject
  public isInit = false
  constructor(private sqlite: SQLite) { }


  init() {
    // this.clear()
    return new Promise((resolve, reject) => {
      this.sqlite.create({
        name: 'eca.db',
        location: 'default',
        key: '2020.eca.norrayhk.com',
        createFromLocation: 1
      }).then((db: SQLiteObject) => {
        this.db = db
        this.isInit = true
        // 查詢記錄
        db.transaction(tx => {
          tx.executeSql('CREATE TABLE IF NOT EXISTS tbl_roll_call' +
            ' (id INTEGER PRIMARY KEY AUTOINCREMENT, activity_id CHAR(50), actCode CHAR(50), ' +
            'actName CHAR(50), actEname CHAR(50), actTeacher CHAR(50), ' +
            'cateCode CHAR(50), cateName CHAR(50), cateEnName CHAR(50),' +
            'activity_date TEXT,' +
            'activityInfo TEXT, lottery TEXT)');
          tx.executeSql('CREATE TABLE IF NOT EXISTS tbl_user_list' +
            ' (id INTEGER PRIMARY KEY AUTOINCREMENT, username CHAR(50), password CHAR(50))');
          // tx.executeSql('INSERT INTO tbl_past_records (code, item_name, search_time) VALUES (?,?,?)', ['111', 'aaaaa', new Date()]);
          // tx.executeSql('INSERT INTO tbl_past_records (code, item_name, search_time) VALUES (?,?,?)', ['222', 'bbbb', new Date()]);
          // tx.executeSql('INSERT INTO tbl_past_records (code, item_name, search_time) VALUES (?,?,?)', ['333', 'ccccc', new Date()]);
          // tx.executeSql('SELECT * FROM tbl_past_records', [],
          //   (x, rs) => {
          //     for (let i = 0; i < rs.rows.length; i++) {
          //       const item = rs.rows.item(i)
          //       console.log(item.id, item.code, item.item_name, item.search_time)
          //     }
          //   }, (x, error) => {
          //   console.log('SELECT error: ' + error.message);
          // });
        })
        resolve(true)
      }).catch(err => {
        console.error(err)
        reject(err)
      })
    })
  }

  clear() {
    this.sqlite.deleteDatabase({
      name: 'eca.db',
      location: 'default',
      key: '2020.eca.norrayhk.com',
      createFromLocation: 1
    })
  }
}
