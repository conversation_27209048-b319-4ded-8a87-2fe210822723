import { Component, OnInit } from '@angular/core';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {Router} from '@angular/router';
import {NavController} from '@ionic/angular';
import {StoreService} from '@services/store/store.service';
import {StorageService} from '@services/storage/storage.service';

@Component({
  selector: 'app-user-index',
  templateUrl: './user-index.page.html',
  styleUrls: ['./user-index.page.scss'],
})
export class UserIndexPage implements OnInit {

  private userInfo: any = {}
  constructor(
    private nativeStorage: NativeStorage,
    private router: Router,
    private navCtrl: NavController,
    private storeService: StoreService,
    private storageService: StorageService,
  ) { }

  ngOnInit() {
    this.init()
  }

  async init() {
    this.userInfo = await this.nativeStorage.getItem('userInfo')
    console.log(this.userInfo)
  }

  get is_engineer() {
    return this.userInfo ? this.userInfo.is_engineer === '1' : false
  }
  get nickname() {
    return this.userInfo && this.userInfo.nickname || '-'
  }
  get username() {
    return this.userInfo && this.userInfo.username || '-'
  }
  get email() {
    return this.userInfo && this.userInfo.email || '-'
  }
  toChangePwd() {
    this.navCtrl.setDirection('forward');
    this.router.navigate(['change-pwd'])
  }
  logout() {
    this.storeService.Logout()
  }

  get version() {
    return this.storageService
    && this.storageService.serverSetting
    && this.storageService.serverSetting.version ? this.storageService.serverSetting.version : '-'
  }
}
