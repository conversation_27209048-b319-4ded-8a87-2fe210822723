import {Injectable} from '@angular/core'

import {RequestService} from '../request/request.service'

@Injectable({
  providedIn: 'root'
})
export class LoginService {

  constructor(public request: RequestService) {
  }

  login(username: string, password: string) {
    return this.request.request({
      url: '/v2/app/user/login',
      method: 'post',
      data: {
        username,
        password
      }
    })
  }

  loginForToken(username: string, password: string) {
    return this.request.requestWithToken({
      url: '/login',
      method: 'post',
      data: {
        username,
        password
      }
    })
  }

}
