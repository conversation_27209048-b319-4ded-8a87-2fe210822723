import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {ActionSheetController, AlertController, ModalController} from '@ionic/angular';
import {ActivityService} from '@services/api/activity.service';
import {LoadingService} from '@app/utils/loading.service';
import {UtilsService} from '@app/utils/utils.service';
import {isNumber} from 'util';
import {NativeStorage} from '@ionic-native/native-storage/ngx';

@Component({
  selector: 'app-activity-sign-up-input',
  templateUrl: './activity-sign-up-input.page.html',
  styleUrls: ['./activity-sign-up-input.page.scss'],
})
export class ActivitySignUpInputPage implements OnInit {

  @Input() domId = 'activity-sign-up-input-modal'
  @Input() activity_id: any = ''
  @Input() data: any = {}
  @Output() public reload = new EventEmitter();
  public actPjCList: any = []
  public actSupportList: any = []
  public actSupport = ''
  public actBackList: any = []
  public actBack = ''
  public min = ''
  public max = ''
  public phone = ''
  constructor(
    private modalCtrl: ModalController,
    public actionSheetController: ActionSheetController,
    private activityService: ActivityService,
    private loadingService: LoadingService,
    private alertController: AlertController,
    private utils: UtilsService,
    private nativeStorage: NativeStorage,
  ) {
  }

  ngOnInit() {
    console.log(this.data)
    this.init()
  }

  init() {
    if (this.data) {
      if (this.data.actPjCList) {
        const arr = []
        for (const key in this.data.actPjCList) {
          if (this.data.actPjCList.hasOwnProperty(key)) {
            arr.push({
              id: key,
              name: this.data.actPjCList[key],
              check: false
            })
          }
        }
        this.actPjCList = arr
      }
      if (this.data.actSupportList) {
        // this.actSupportList = this.data.actSupportList.map(p => ({ name: p, check: false }))
        const arr = []
        for (const key in this.data.actSupportList) {
          if (this.data.actSupportList.hasOwnProperty(key)) {
            arr.push({
              id: key,
              name: this.data.actSupportList[key],
            })
          }
        }
        this.actSupportList = arr
      }
      if (this.data.actBackList) {
        // const arr = []
        // for (const key in this.data.actBackList) {
        //   if (this.data.actBackList.hasOwnProperty(key)) {
        //     arr.push({
        //       id: key,
        //       name: this.data.actBackList[key],
        //     })
        //   }
        // }
        // this.actBackList = arr

        const arr = []
        for (const key in this.data.actBackList) {
          if (this.data.actBackList.hasOwnProperty(key)) {
            arr.push({
              id: key,
              name: this.data.actBackList[key],
            })
          }
        }
        this.actBackList = arr
      }

      if (this.data.activity) {
        const actPjP = this.data.activity.actPjP
        if (actPjP !== null && actPjP !== '') {
          this.min = actPjP
        }
        const actPjPL = this.data.activity.actPjPL
        if (actPjPL !== null && actPjPL !== '') {
          this.max = actPjPL
        }
      }
    }

    this.nativeStorage.getItem('userInfo').then( user => {
      this.phone = user.phone
    }).catch(err => {
      console.error(err)
    })
  }
  get showProject() {
    if (this.actPjCList && this.actPjCList.length > 0) {
      if (this.actPjCList.length === 1) {
        const id = this.actPjCList[0].id + ''
        return id !== '0'
      } else {
        return true
      }
    }
    return false
  }

  onClose(save = false) {
    this.modalCtrl.dismiss({
      dismissed: true,
      save
    });
  }
  async onSave() {

    let min = 0
    let max = 99999
    if (this.min) {
      if (Number(this.min) > 0) {
        min = Number(this.min)
      }
    }
    if (this.max) {
      if (Number(this.max) > 0) {
        max = Number(this.max)
      }
    }
    let prjs = []
    if (this.showProject) {
      prjs = this.actPjCList.filter(i => i.check).map(i => i.id)
      const pl = prjs.length
      if (pl < min) {
        await this.utils.showMsg('限制項目數量最少為' + min)
        return
      }
      if (pl > max) {
        await this.utils.showMsg('限制項目數量最多為' + max)
        return
      }
    }
    const actPj = prjs.join(',')

    const actSupport = this.actSupport
    if (!actSupport || actSupport === '') {
      await this.utils.showMsg('請選擇是否申請資助')
      return
    }
    const actBack = this.actBack
    if (!actBack || actBack === '') {
      await this.utils.showMsg('請選擇歸程方式')
      return
    }
    const stuPhoneNum = this.phone
    if (!stuPhoneNum || stuPhoneNum === '') {
      await this.utils.showMsg('請輸入聯絡電話')
      return
    }

    const data = {
      actPj,
      actSupport,
      actBack,
      stuPhoneNum,
    }

    this.modalCtrl.dismiss({
      dismissed: true,
      save: true,
      data
    });
  }

}
