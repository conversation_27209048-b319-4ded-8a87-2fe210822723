import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { TabTeacherPage } from './tab-teacher.page';
import { TabTeacherPageRoutingModule } from './tab-teacher.router.module'

const routes: Routes = [
  {
    path: '',
    component: TabTeacherPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    // RouterModule.forChild(routes)
    TabTeacherPageRoutingModule
  ],
  declarations: [TabTeacherPage]
})
export class TabTeacherPageModule {}
