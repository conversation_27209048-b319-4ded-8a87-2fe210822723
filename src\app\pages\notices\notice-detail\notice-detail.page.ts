import { Component, OnInit } from '@angular/core';
import {ModalController, NavController, Platform} from '@ionic/angular';
import {ActivatedRoute} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import {UtilsService} from '@app/utils/utils.service';
import {NoticesService} from '@services/api/notices.service';
import {dateFormat} from '@app/utils';
import { NoticeFilePage } from '@pages/notices/notice-file/notice-file.page';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {NoticeStudentListPage} from '@pages/notices/notice-student-list/notice-student-list.page';
import {Events} from '@app/services/events/event.service';

@Component({
  selector: 'app-notice-detail',
  templateUrl: './notice-detail.page.html',
  styleUrls: ['./notice-detail.page.scss'],
})
export class NoticeDetailPage implements OnInit {

  public id: any = ''
  public data: any = {}
  public user: any = {}
  public studentList: any = []
  constructor(
    private platform: Platform,
    private routeInfo: ActivatedRoute,
    public nav: NavController,
    private loadingService: LoadingService,
    private modalCtrl: ModalController,
    private utils: UtilsService,
    private notices: NoticesService,
    private nativeStorage: NativeStorage,
    private events: Events,
    ) { }

  ngOnInit() {
    this.platform.ready().then(async () => {
      this.user = await this.nativeStorage.getItem('userInfo')
      // this.statusBar.backgroundColorByHexString('#003658')
      // this.id = this.routeInfo.snapshot.queryParams['id']
      this.id = this.routeInfo.snapshot.params.id
      this.loadData()
    })
  }

  async loadData() {
    await this.loadingService.start()
    try {
      const res: any = await this.notices.getNotice(this.id)
      console.log(res)
      res.time = this.getDate(res.create_time)

      const arr = []
      for (const key in res.student) {
        if (res.student.hasOwnProperty(key)) {
          const classes = {
            className: key,
            students: []
          }
          classes.students = res.student[key].map(s => s)
          arr.push(classes)
        }
      }
      this.studentList = arr


      this.data = res
      if (this.showDot) {
        this.read()
      }
    } catch (e) {
      console.error(e)
    }

    await this.loadingService.end()
  }
  async read() {
    if (!this.user) { return }
    const notice_id = this.id
    const user_id = this.user.user_id
    try {
      await this.notices.readNotice({ notice_id, user_id })

      this.events.publish('notice:changeReadStatus', {notice_id, unread: '0' });
    } catch (e) {
      console.error(e)
    }
  }
  get showDot() {
    if (this.user && typeof this.user.user_type === 'string') {
      const user_type = this.user.user_type.toUpperCase()
      return user_type === 'STUDENT'
    }
    return false
  }

  getDate(v) {
    const str = v.replace(new RegExp(/-/gm), '/')
    const d = new Date(str)
    return dateFormat(d, 'yyyy-MM-dd HH:mm')
  }
  onFileView() {
    this.showFileList()

  }

  onCheckStudent() {
    this.showStudents()
  }

  async showFileList() {
    const files = this.data.attachment
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: NoticeFilePage,
      componentProps: {
        domId: 'notice-file-view-modal',
        files,
      },
      id: 'notice-file-view-modal'
    })
    await modal.present()
    const res = await modal.onDidDismiss()
    if (res && res.data && res.data.save) {
      return { save: true, student: res.data.student }
    }
    return {}
  }



  async showStudents() {
    // const data = this.rollCallData
    // const activity_id = this.id
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: NoticeStudentListPage,
      componentProps: {
        domId: 'notice-stu-list-modal',
        students: this.studentList,
        edit: false,
      },
      id: 'notice-stu-list-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()

  }

}
