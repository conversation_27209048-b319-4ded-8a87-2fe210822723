import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import { StoreService } from '@services/store/store.service';
import {Router} from '@angular/router';
import {NavController, PopoverController} from '@ionic/angular';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {TranslateService} from '@ngx-translate/core';
import { GooglePlus } from '@ionic-native/google-plus/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {GoogleService} from '@services/api/google.service';
import {LoadingService} from '@app/utils/loading.service';
import {UserListService} from '@services/sql/userList.service';

@Component({
  selector: 'app-user-list-popover',
  templateUrl: './user-list-popover.component.html',
  styleUrls: ['./user-list-popover.component.scss'],
})
export class UserListPopoverComponent implements OnInit {
  @Output() public select = new EventEmitter();
  @Output() public close = new EventEmitter();
  public user: any = {}
  public userList: any = []
  constructor(
    private store: StoreService,
    private router: Router,
    private navCtrl: NavController,
    public popoverController: PopoverController,
    private nativeStorage: NativeStorage,
    public translate: TranslateService,
    private googlePlus: GooglePlus,
    private googleService: GoogleService,
    private utils: UtilsService,
    private loadingService: LoadingService,
    private userListService: UserListService,
  ) { }

  async ngOnInit() {
    this.user = await this.nativeStorage.getItem('userInfo')
    this.init()
  }


  ionViewWillLeave() {
    this.close.emit()
  }


  get isEng() {
    return this.translate.currentLang === 'en'
  }

  get userName() {
    if (this.user && this.user.name_cn) {
      return this.user.name_cn
    }
    return '-'
  }
  get isStudent() {
    if (this.user) {
      return this.user.user_type_m === 'S'
    }
    return true
  }
  onClose() {
    this.popoverController.dismiss()
  }


  init() {
    this.userListService.all().then(res => {
      this.userList = res
    })
  }
  onSelect(item) {
    this.select.emit(item)
  }
  async onDelete(item) {
    const del = await this.utils.confirm(
      '刪除賬戶',
      '確認刪除賬戶' + item.username + '？',
      '刪除')
    if (del) {
      this.userListService.delete(item.id)
    }

  }
}
