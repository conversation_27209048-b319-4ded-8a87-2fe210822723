import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {ActivitySelectComponent} from './activity-select.component'
import {IonicModule} from '@ionic/angular'
import {TranslateModule} from '@ngx-translate/core';

@NgModule({
  declarations: [ActivitySelectComponent],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
  ],
  // exports: [ActivitySelectComponent]
})
export class ActivitySelectModule { }
