import { Component, OnInit } from '@angular/core';
import {Events} from '@app/services/events/event.service';

@Component({
  selector: 'app-tab-teacher',
  templateUrl: './tab-teacher.page.html',
  styleUrls: ['./tab-teacher.page.scss'],
})
export class TabTeacherPage implements OnInit {

  constructor(
    private events: Events,
  ) { }

  ngOnInit() {
  }

  onClickNotice() {
    this.events.publish('notice:reload');
  }
}
