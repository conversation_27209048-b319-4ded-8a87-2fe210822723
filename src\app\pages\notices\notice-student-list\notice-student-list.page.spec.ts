import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { NoticeStudentListPage } from './notice-student-list.page';

describe('NoticeStudentListPage', () => {
  let component: NoticeStudentListPage;
  let fixture: ComponentFixture<NoticeStudentListPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ NoticeStudentListPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(NoticeStudentListPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
