<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>添加學生</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="onSelectAll()">
        {{ selectStr }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list>
    <ion-item detail button lines="full" *ngFor="let item of list" (click)="selectStudent(item)">
      <ion-button [color]="getSelectAllClass(item)" size="mini" slot="start" (click)="itemSelectAll(item, $event)">全選</ion-button>
      <div class="class-name">{{ item.className }}</div>
      <ion-note slot="end">{{ getStuNum(item) }}</ion-note>
    </ion-item>
  </ion-list>
</ion-content>
