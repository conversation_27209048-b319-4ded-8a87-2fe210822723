import {ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {IonContent, IonInfiniteScroll, IonList, ModalController, NavController} from '@ionic/angular';
import {StarService} from '@services/api/star.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {SelectStudentService} from '@services/utils/select-student.service';
import {Router} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import {TranslateService} from '@ngx-translate/core';
import * as charts from 'echarts';
import {StudentStarInputPage} from '@pages/tab-student/student-star-input/student-star-input.page';
import {StudentStarDetailPage} from '@pages/tab-student/student-star-detail/student-star-detail.page';
import {dateFormat} from '@app/utils';
import {Events} from '@app/services/events/event.service';

@Component({
    selector: 'app-student-star',
    templateUrl: './student-star.page.html',
    styleUrls: ['./student-star.page.scss'],
})
export class StudentStarPage implements OnInit {

    @ViewChild('contentList', {}) contentList: IonContent;
    @ViewChild('contentSummary', {}) contentSummary: IonContent;
    @ViewChild('echartsContent', {}) container: ElementRef;

    public tab = 'LIST'
    public listDataFilter: any = {
        stu_id: '',
        semester: 0,
        year_id: 0,
        scid: '',
        is_audit: '',
        stu_input: 1,
        page_index: 1,
        page_size: 100
    }
    public total_count_data = 0
    public listData: any = []
    public years: any = []
    public timeSetting: any = {}
    public categories: any = []
    public submitJson: any = {}
    public canEdit: any = false

    public cate_star: any = []

    public user: any = {}
    public stuInfo: any = {}

    constructor(
        private nativeStorage: NativeStorage,
        public utils: UtilsService,
        public selectStudentService: SelectStudentService,
        private modalCtrl: ModalController,
        private navCtrl: NavController,
        private router: Router,
        private loadingService: LoadingService,
        private events: Events,
        public starService: StarService,
        public translate: TranslateService,
        protected cdr: ChangeDetectorRef,
    ) {
    }

    async initChart(showError = false) {
        const params = {
            stu_id: this.user.stu_id,
            year_id: this.listDataFilter.year_id,
            semester: this.listDataFilter.semester,
            is_student_select: 1
        }

        const summaryData: any = await this.starService.fetchStars(params)

        let totalIntegral = 0
        if (!isNaN(summaryData.total_star)) {
            totalIntegral = summaryData.total_star
        }
        let maxIntegral = 0
        let maxproportion = 0
        const axisLineColors = []
        const color_arr = [
            '#DAE4FF',
            '#A9BFFF',
            '#678EFF',
            '#3D6EFF',
            '#295efc',
            '#1c54f8',
        ]
        summaryData.award_info.forEach(award => {
            if (award.integral > maxIntegral) {
                maxIntegral = award.integral
            }
            if (award.proportion > maxproportion) {
                maxproportion = award.proportion
            }
            if (axisLineColors.length === color_arr.length) {
                axisLineColors.push([award.proportion, color_arr[color_arr.length - 1]])
            } else {
                axisLineColors.push([award.proportion, color_arr[axisLineColors.length]])
            }
        })

        if (maxproportion < 1) {
            if (axisLineColors.length === color_arr.length) {
                axisLineColors.push([1, color_arr[color_arr.length - 1]])
            } else {
                axisLineColors.push([1, color_arr[axisLineColors.length]])
            }
        }

        this.cate_star = []
        let tmpArr = []
        summaryData.cate_star.forEach((cate, cateIndex) => {
            const obj = Object.assign({}, cate)
            if ((cateIndex + 1) % 2 > 0) {
                tmpArr = [obj]
            } else {
                tmpArr.push(obj)
                this.cate_star.push(tmpArr)
                tmpArr = []
            }
        })
        if (tmpArr.length > 0) {
            this.cate_star.push(tmpArr)
        }
        const ec = charts as any;
        const container = document.getElementById('chart');

        if (summaryData.award_info.length === 0) {
            container.style.visibility = 'hidden'
            if (showError) {
                await this.loadingService.end()
                await this.utils.showMsg('未有相關數據')
                return
            } else {
                return
            }
        } else {
            container.style.visibility = 'unset'
        }
        const chart = ec.init(container);

        chart.setOption({
            series: [
                {
                    type: 'gauge',
                    startAngle: 180,
                    endAngle: 0,
                    min: 0,
                    max: maxIntegral,
                    splitNumber: 1,
                    itemStyle: {
                        color: '#f12101',
                        shadowColor: '#f1210173',
                        shadowBlur: 10,
                        shadowOffsetX: 2,
                        shadowOffsetY: 2
                    },
                    progress: {
                        show: true,
                        roundCap: true,
                        width: 8
                    },
                    pointer: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            width: 6,
                            color: axisLineColors
                        }
                    },
                    axisTick: {
                        splitNumber: 1,
                        lineStyle: {
                            width: 2,
                            color: '#999'
                        }
                    },
                    splitLine: {
                        length: 12,
                        lineStyle: {
                            width: 3,
                            color: '#999'
                        }
                    },
                    axisLabel: {
                        distance: 30,
                        color: '#999',
                        fontSize: 20
                    },
                    title: {
                        show: false
                    },
                    detail: {
                        backgroundColor: '#fff',
                        borderColor: '#999',
                        borderWidth: 0,
                        width: '100%',
                        lineHeight: 30,
                        height: 30,
                        offsetCenter: [0, '35%'],
                        valueAnimation: true,
                        formatter(data) {
                            const tempArr = []
                            tempArr.push('{tips|摘星總數}')
                            tempArr.push('{total|' + totalIntegral + '}')
                            tempArr.push('{title|' + (summaryData.this_award !== null && summaryData.this_award !== '' ? summaryData.this_award : '未達標') + '}')
                            if (summaryData.next_award !== null && summaryData.next_award !== '') {
                                tempArr.push('{next|' + '下一個獎項：' + (summaryData.next_award !== null ? summaryData.next_award : '') + '（' + (summaryData.next_award_star !== null ? summaryData.next_award_star : 0) + '星）' + '}')
                            }
                            return tempArr.join('\n')
                        },
                        rich: {
                            tips: {
                                fontSize: 10,
                                color: '#303133FF'
                            },
                            total: {
                                fontSize: 48,
                                fontWeight: 'bolder',
                                color: '#3D6EFFFF'
                            },
                            title: {
                                fontSize: 14,
                                color: '#303133FF'
                            },
                            next: {
                                fontSize: 10,
                                color: '#909399FF'
                            }
                        }
                    },
                    data: [
                        {
                            value: totalIntegral
                        }
                    ]
                }
            ]
        })
    }

    ngOnInit() {
        this.events.subscribe('login', () => {
            this.init()
        })
        this.init()
    }

    async init() {
        this.user = await this.nativeStorage.getItem('userInfo')
        await this.initYears()
        this.listData = []
        this.listDataFilter = {
            stu_id: '',
            semester: this.user.default_semester,
            year_id: this.user.year_id,
            scid: '',
            is_audit: '',
            stu_input: 1,
            page_index: 1,
            page_size: 100
        }
        this.initYearTimeSetting()
        this.initCategory()
        this.initChart()
    }

    async initYears() {
        try {
            const res: any = await this.starService.fetchYearsDesc({})
            this.years = res.datas
        } catch (e) {
            console.error(e)
        }
    }

    async changeYear(research = false) {
        await this.initYearTimeSetting()
        await this.initCategory(true)
        this.initChart()
    }

    async changeSemester(research = false) {
        this.canEdit = this.timeSetting['semester_' + this.listDataFilter.semester]
        this.initChart()
        await this.loadListData()
    }

    async initYearTimeSetting() {
        try {
            this.listDataFilter.scid = ''
            const res: any = await this.starService.fetchYearsTimeSetting({year_id: this.listDataFilter.year_id, is_all: 0})

            const d = new Date()
            const nowDate = dateFormat(d, 'yyyy-MM-dd H:i:s')
            console.log(nowDate)

            this.timeSetting = {
                semester_1: nowDate < res.up_time,
                semester_2: nowDate >= res.up_time && nowDate < res.down_time,
            }
            this.canEdit = this.timeSetting['semester_' + this.listDataFilter.semester]
        } catch (e) {
            console.error(e)
        }
    }

    async initCategory(research = false) {
        try {
            this.listDataFilter.scid = ''
            const res: any = await this.starService.fetchStarCates({year_id: this.listDataFilter.year_id})
            console.log(res)
            this.categories = res

            if (research) {
                this.loadListData()
            }
        } catch (e) {
            console.error(e)
        }
    }

    onSearchKeyUp(event) {
        if ('Enter' === event.key) {
            this.loadListData()
        }
    }

    async loadListData(event?, isNew = true, isRefresh = false) {
        await this.loadingService.start()
        let empty = false
        try {
            const params = {}

            for (const [key, value] of Object.entries(this.listDataFilter)) {
                if (value !== '') {
                    params[key] = value
                }
            }

            const res: any = await this.starService.fetchStars(params)
            this.listData = res.project !== undefined ? res.project : []
            this.stuInfo = res.student_info
        } catch (e) {
            if (e && e.code) {
                if (e.code === 3002) {
                    empty = true
                    // await this.utils.showMsg(e.desc)
                    if (isNew) {
                        this.listData = []
                    }
                }
            }
            console.error(e)
        }
        this.contentList.scrollToTop()
        // 處理數據
        this.listData.forEach(item => {
            const obj = {
                year_id: this.listDataFilter.year_id,
                project_id: item.id,
                num: '',
                text_array: item.text_data,
                eca_content: item.active_data
            }

            if (item.parent_content.length > 0) {
                item.parent_content.forEach(pc => {
                    obj.text_array.push({
                        text_id: this.randomString(5),
                        detail: pc,
                    })
                })
            }

            if (item.is_audit === 'AUDITED') {
                if (item.audit_num === null || isNaN(item.audit_num)) {
                    obj.num = ''
                } else {
                    obj.num = item.audit_num
                }
            } else {
                if (item.audit_num === null || isNaN(item.audit_num)) {
                    if (item.stu_alone_num !== null && !isNaN(item.stu_alone_num)) {
                        obj.num = item.stu_alone_num
                    }
                } else {
                    obj.num = item.audit_num
                }
            }
            this.submitJson['pid_' + item.id] = obj
        })

        await this.loadingService.end()
    }

    getStarClass(starIndex, starCount) {
        if (isNaN(starCount) || starIndex >= starCount) {
            return 'list-star iconfont icon-ios-star-outline'
        } else {
            return 'list-star iconfont icon-ios-star'
        }
    }

    async segmentChanged($event) {
        this.tab = $event.detail.value
        this.cdr.detectChanges();
        if (this.tab === 'SUMMARY') {
            await this.loadingService.start()
            await this.initChart(true)
            await this.loadingService.end()
        } else {
            // await this.loadListData()
        }
    }

    getStatusClass(tv) {
        const s = tv + ''
        switch (s) {
            case 'DESCRIBE':
                return 'tv-0'
            case 'NAME':
                return 'tv-0'
            case 'AWARD':
                return 'tv-1'
            case 'ECA':
                return 'tv-4'
            default:
                return 'status-default'
        }
    }

    getSummaryItemStatusClass(groupIndex, seq) {
        const tmpNumber = 2 * groupIndex + seq
        return 'status_bg_' + (tmpNumber % 8)
    }

    getStatus(status) {
        const s = status + ''
        switch (s) {
            case 'AUDITED':
                return '已審批'
            default:
                return '待審批'
        }
    }

    async toInput(item, index) {
        const inputData = await this.inputInfo(item.id, item.is_audit)
        if (!inputData || !inputData.hasOwnProperty('project_id')) {
            return
        }
        console.log(inputData)
        this.submitJson['pid_' + item.id] = inputData
    }

    async inputInfo(project_id, is_audit) {
        const can_edit = this.timeSetting['semester_' + this.listDataFilter.semester]
        /*if (can_edit) {
            if (is_audit === 'AUDITED') {
                can_edit = false
            }
        }*/
        const modal = await this.modalCtrl.create({
            component: StudentStarInputPage,
            componentProps: {
                domId: 'student-star-input-modal',
                project_id,
                stu_id: this.user.stu_id,
                post_data: JSON.parse(JSON.stringify(this.submitJson['pid_' + project_id])),
                can_edit
            },
            id: 'student-star-input-modal'
        })
        await modal.present()
        const res: any = await modal.onDidDismiss()
        if (res.data.save) {
            return res.data.data
        }
        return {}
    }

    changeListStatus(id, status) {
        this.events.publish('activity:changeStatus', {id, status});
    }

    async submit() {
        // 確認
        const confirm = await this.utils.confirm(
            '提交',
            `確認提交摘星數據?`)
        if (!confirm) {
            return
        }
        try {
            const stu_id = this.user.stu_id
            const is_student = 1
            const year_id = this.listDataFilter.year_id
            const semester = this.listDataFilter.semester
            const is_audit = 0
            const data_json = []

            this.listData.forEach(item => {
                // 處理 text_array
                const tmp_text_array = []
                this.submitJson['pid_' + item.id].text_array.forEach(textObj => {
                    if (isNaN(textObj.text_id)) {
                        tmp_text_array.push({
                            text: textObj.detail
                        })
                    } else {
                        tmp_text_array.push({
                            text_id: textObj.text_id
                        })
                    }
                })
                this.submitJson['pid_' + item.id].text_array = tmp_text_array

                // 處理 eca
                const tmp_eca_content = []
                this.submitJson['pid_' + item.id].eca_content.forEach(ecaObj => {
                    tmp_eca_content.push(ecaObj.active_id)
                })
                this.submitJson['pid_' + item.id].eca_content = tmp_eca_content
                data_json.push(this.submitJson['pid_' + item.id])
            })
            this.loadingService.start()
            await this.starService.updateStar({stu_id, is_student, year_id, semester, is_audit, data_json})
            await this.loadListData()
            this.utils.showMsg('提交成功')
        } catch (e) {
            await this.loadingService.end()
            console.error(e)
            if (e && e.code) {
                switch (e.code) {
                    case 4001:
                    case 4002:
                    case 4003:
                        await this.utils.showMsg(e.desc)
                        return
                    default:
                        if (e.desc) {
                            await this.utils.showMsg(e.desc)
                            return
                        }
                        break
                }
            }
            await this.utils.showMsg('提交失敗')
        }
    }

    async toDetail() {
        const returnData = await this.showDetail()
        if (!returnData || !returnData.hasOwnProperty('year_id')) {
            return
        }
        console.log(returnData)

        if (returnData.year_id !== this.listDataFilter.year_id || returnData.semester !== this.listDataFilter.semester) {
            this.listDataFilter.year_id = returnData.year_id
            this.listDataFilter.semester = returnData.semester
            this.loadListData()
        }
    }

    async showDetail() {
        const modal = await this.modalCtrl.create({
            component: StudentStarDetailPage,
            componentProps: {
                domId: 'star-input-detail-modal',
                year_id: this.listDataFilter.year_id,
                stu_id: this.listDataFilter.stu_id,
                semester: this.listDataFilter.semester,
            },
            id: 'star-input-detail-modal'
        })
        await modal.present()
        const res: any = await modal.onDidDismiss()
        console.log(res, 'dismiss')
        if (res.data.save) {
            return res.data.data
        }
        return {
            year_id: '',
            semester: ''
        }
    }

    toNumber(value) {
        return Number(value)
    }

    randomString(len) {
        len = len || 32;
        const $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz';
        /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
        const maxPos = $chars.length;
        let str = '';
        for (let i = 0; i < len; i++) {
            str += $chars.charAt(Math.floor(Math.random() * maxPos));
        }
        return str;
    }
}
