import { Injectable } from '@angular/core';
import {RequestService} from '@services/request/request.service';

@Injectable({
  providedIn: 'root'
})
export class CustomersService {

  constructor(public request: RequestService) {
  }

  /**
   * 獲取所有客戶資料
   * @param customer_id 客戶id
   * @param search_name 模糊搜索客戶名稱,聯繫人
   * @param join_date_from 加入日期範圍 從
   * @param join_date_to 加入日期範圍 至
   * @param is_active 1=活躍,0=不活躍
   * @param page_index 請求頁碼,從1開始
   * @param page_size 每頁數目
   */
  fetchCustomers({ customer_id, search_name, join_date_from, join_date_to, is_active, page_index, page_size }: any) {
    return this.request.request({
      url: '/customers',
      method: 'get',
      params: {
        customer_id, search_name, join_date_from, join_date_to, is_active, page_index, page_size
      }
    })
  }
}
