import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { NoticeAddPage } from './notice-add.page';

describe('NoticeAddPage', () => {
  let component: NoticeAddPage;
  let fixture: ComponentFixture<NoticeAddPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ NoticeAddPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(NoticeAddPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
