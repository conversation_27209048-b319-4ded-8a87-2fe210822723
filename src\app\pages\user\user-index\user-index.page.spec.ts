import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { UserIndexPage } from './user-index.page';

describe('UserIndexPage', () => {
  let component: UserIndexPage;
  let fixture: ComponentFixture<UserIndexPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ UserIndexPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UserIndexPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
