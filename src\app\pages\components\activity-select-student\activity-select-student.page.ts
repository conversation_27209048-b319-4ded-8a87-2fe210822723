import {Component, Input, OnInit} from '@angular/core';
import {ModalController} from '@ionic/angular';

@Component({
  selector: 'app-activity-select-student',
  templateUrl: './activity-select-student.page.html',
  styleUrls: ['./activity-select-student.page.scss'],
})
export class ActivitySelectStudentPage implements OnInit {

  @Input() list: any = []
  constructor(
    private modalCtrl: ModalController,
    ) { }

  ngOnInit() {
    console.log(this.list)
  }

  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true
    });
  }

  onSelectAll() {
    const checked = this.list.every(i => i.checked)
    this.list.forEach(i => i.checked = !checked)
  }

  get selectStr() {
    return this.list.every(i => i.checked) ? '取消全選' : '全選'
  }
  getClassNo(item) {
    if (item) {
      if (item.hasOwnProperty('stuInfosyNo')) {
        return item.stuInfosyNo
      } else if (item.hasOwnProperty('stuClassNo')) {
        return item.stuClassNo
      }
    }
    return ''
  }
}
