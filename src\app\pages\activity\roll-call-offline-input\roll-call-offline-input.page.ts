import {Component, Input, OnInit} from '@angular/core';
import {ActionSheetController, AlertController, ModalController} from '@ionic/angular';
import {ActivityService} from '@services/api/activity.service';
import {LoadingService} from '@app/utils/loading.service';
import {UtilsService} from '@app/utils/utils.service';
import {RollCallService} from '@services/sql/rollCall.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {Network} from '@ionic-native/network/ngx';

@Component({
  selector: 'app-roll-call-offline-input',
  templateUrl: './roll-call-offline-input.page.html',
  styleUrls: ['./roll-call-offline-input.page.scss'],
})
export class RollCallOfflineInputPage implements OnInit {

  @Input() domId = 'edit-day-modal'
  @Input() activity_id: any = ''
  @Input() data: any = {}
  @Input() year: any = ''
  @Input() day: any = ''
  @Input() itemIndex: any = ''
  public item: any = {}
  public yearData: any = {}
  constructor(
    private modalCtrl: ModalController,
    public actionSheetController: ActionSheetController,
    private activityService: ActivityService,
    private rollCallService: RollCallService,
    private loadingService: LoadingService,
    private alertController: AlertController,
    private utils: UtilsService,
    private nativeStorage: NativeStorage,
    private network: Network
  ) {
  }

  ngOnInit() {
    console.log(this.data, this.itemIndex)
    if (this.data && this.itemIndex != null) {
      try {
        this.yearData = this.data.activity_date[this.itemIndex]
        const year = this.data.activity_date[this.itemIndex].actDateArray[this.year]
        const item = year.find(d => d.date === this.day)
        if (item) {
          this.item = JSON.parse(JSON.stringify(item))
        } else {
          this.item = {}
        }
      } catch (e) {
        console.error(e)
      }
    }
  }
  onClose(save = false) {
    this.modalCtrl.dismiss({
      dismissed: true,
      save
    });
  }
  getStatus(stu) {
    const status = stu.status
    // if (status === null || status === undefined) {
    //   return '未設定'
    // }
    switch (status) {
      case 'ATTEND':
        return '出席'
      case 'ABSENT':
      case 'ANOTHER ACTIVITY':
      case 'SICK LEAVE':
      case 'DETENTION':
      case 'PERSONAL LEAVE':
        return '缺席'
      // case 'ANOTHER ACTIVITY':
      //   return '缺席 : 課外活動'
      // case 'SICK LEAVE':
      //   return '缺席 : 病假'
      // case 'DETENTION':
      //   return '缺席 : 被老師罰留堂'
      // case 'PERSONAL LEAVE':
      //   return '缺席 : 事假'
      case 'LATE':
        return '遲到'
      case 'LEAVE EARLY':
        return '早退'
      default:
        return '未設定'
    }

  }
  getStatusColor(stu) {
    const status = stu.status
    // if (status === null || status === undefined) {
    //   return 'white'
    // }

    switch (status) {
      case 'ATTEND':
        return 'green'
      case 'ABSENT':
      case 'ANOTHER ACTIVITY':
      case 'SICK LEAVE':
      case 'DETENTION':
      case 'PERSONAL LEAVE':

      case 'LATE':
      case 'LEAVE EARLY':
        return 'yellow'
      default:
        return 'white'
    }

  }

  onchangeStatusToAttend(stu, i) {
    stu.status = 'ATTEND'
  }
  async onChangeStatus(stu, i) {
    await this.setStatus(stu)
    // const status = await this.setStatus(stu)
    // stu.status = status
  }

  getStatusIcon(status, option) {
    if (status) {
      return status === option ? 'checkmark' : '-'
    }
    return ''
  }

  async setStatus(stu) {
    const oldStatus = stu.status
    let status = ''
    const actionSheet = await this.actionSheetController.create({
      header: '選擇名單類型',
      buttons: [{
        icon: this.getStatusIcon(oldStatus, 'ATTEND'),
        text: '出席',
        handler: () => {
          status = 'ATTEND'
          stu.status = status
        }
      // }, {
      //   text: '缺席',
      //   handler: () => {
      //     status = 'ABSENT'
      //     stu.status = status
      //   }
      }, {
        icon: this.getStatusIcon(oldStatus, 'ANOTHER ACTIVITY'),
        text: '缺席 : 課外活動',
        handler: () => {
          status = 'ANOTHER ACTIVITY'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, 'SICK LEAVE'),
        text: '缺席 : 病假',
        handler: () => {
          status = 'SICK LEAVE'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, 'DETENTION'),
        text: '缺席 : 被老師罰留堂',
        handler: () => {
          status = 'DETENTION'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, 'PERSONAL LEAVE'),
        text: '缺席 : 事假',
        handler: () => {
          status = 'PERSONAL LEAVE'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, 'LATE'),
        text: '遲到',
        handler: () => {
          status = 'LATE'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, 'LEAVE EARLY'),
        text: '早退',
        handler: () => {
          status = 'LEAVE EARLY'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, '-'),
        text: '重置',
        // icon: 'close',
        // role: 'cancel',
        handler: () => {
          status = ''
          stu.status = status
        }
      }]
    });
    await actionSheet.present();
    await actionSheet.onDidDismiss();

    // stu.status = status
    return status
  }

  async onSave() {
    let save = false
    let send_notice = '1'
    try {
      // const user = await this.nativeStorage.getItem('userInfo')
      // if (user && user.user_id) {
        save = await this.saveConfirm()
        if (save) {
          const send = await this.sendConfirm()
          send_notice = send ? '1' : '0'
        }
      // }
    } catch (e) {
      console.error(e)
    }

    // if (!save) { return }

    await this.loadingService.start()
    try {

      const item = this.data.activity_date[this.itemIndex].actDateArray[this.year].find(i => i.date === this.day)
      if (!item) {
        this.utils.showMsg('保存失敗，請聯繫系統管理員')
        return false
      }
      item.students = this.item.students




      this.rollCallService.updateRollCall({
        id: this.data.id,
        activity_date: this.data.activity_date,
      })
      if (save) {
        if (this.network.type === this.network.Connection.NONE) {
          this.utils.showMsg('無法上載，請檢查網絡！', '網絡錯誤')
        } else {
          const activity_id = this.activity_id
          const actKey = this.data.activity_date[this.itemIndex].actKey
          const students = []
          for (const stu of this.item.students) {
            students.push({
              student_id: stu.student_id,
              status: stu.status || null,
            })
          }
          const roll_call_array = [
            {
              actKey,
              dateArray: [
                {
                  date: this.item.date,
                  students
                }
              ]
            }
          ]
          try {
            const res: any = await this.activityService.saveRollCall({activity_id, roll_call_array, send_notice})
            if (res && res.code) {
              if (res.code === 3004) {
                await this.loadingService.end()
                this.utils.showMsg('活動數據不匹配，請重新下載點名紙！', '上載失敗')
                return
              }
            }
            console.log(res)
          } catch (e) {
            console.log(e)
            this.utils.showMsg('無法上載，請重試！', '上載失敗')
          }
        }
      }

      this.utils.showToast({ msg: '保存成功！' })
      this.onClose(true)
      // console.log(res)
    } catch (e) {
      switch (e.code) {
        case 3003:
          break
      }
      console.error(e)
    }

    await this.loadingService.end()

  }


  async saveConfirm() {
    let save = false
    const alert = await this.alertController.create({
      header: '提示',
      message: '是否確認上載點名紙？',
      backdropDismiss: false,
      // subHeader: '',
      buttons: [
        {
          text: '僅保存',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            save = false
          }
        }, {
          text: '保存並上載',
          handler: () => {
            save = true
          }
        }
      ]
    });
    await alert.present();
    await alert.onDidDismiss()
    return save
  }

  async sendConfirm() {
    let send = true
    const alert = await this.alertController.create({
      header: '提示',
      message: '上載後是否發送訊息到家長手機？',
      backdropDismiss: false,
      // subHeader: '',
      buttons: [
        {
          text: '否',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (data) => {
            send = false
          }
        }, {
          text: '是',
          handler: (data) => {
            send = true
          }
        }
      ]
    });
    await alert.present();
    await alert.onDidDismiss()
    return send
  }

  onAllAttend() {
    console.log('全部出席')
    const isAll = this.item.students.every(stu => stu.status === 'ATTEND')
    this.item.students.forEach(stu => {
      stu.status = isAll ? null : 'ATTEND'
    })
  }
  get allSetStr() {
    const isAll = this.item.students.every(stu => stu.status === 'ATTEND')
    return isAll ? '取消全部出席' : '全部出席'
  }
  get allSetClass() {
    const isAll = this.item.students.every(stu => stu.status === 'ATTEND')
    return isAll ? 'white' : 'green'
  }

}
