import {Component, OnInit, ViewChild} from '@angular/core';
import {IonContent, IonInfiniteScroll, IonList, NavController} from '@ionic/angular';
import {ActivityService} from '@services/api/activity.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {SelectStudentService} from '@services/utils/select-student.service';
import {Router} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import {dateFormat} from '@app/utils';
import {Events} from '@app/services/events/event.service';

@Component({
  selector: 'app-student-personal',
  templateUrl: './student-personal.page.html',
  styleUrls: ['./student-personal.page.scss'],
})
export class StudentPersonalPage implements OnInit {

  @ViewChild('infiniteScroll', {}) infiniteScroll: IonInfiniteScroll;
  @ViewChild('content', {}) content: IonContent;
  @ViewChild('list', {}) list: IonList;
  public listData: any = []
  public user: any = {}
  public filter: any = {
    actTerm: '',
    page_index: 0,
    page_size: 20,
  }
  public total_count = 0

  constructor(
    public activityService: ActivityService,
    private nativeStorage: NativeStorage,
    public utils: UtilsService,
    public selectStudentService: SelectStudentService,
    private navCtrl: NavController,
    private router: Router,
    private loadingService: LoadingService,
    private events: Events,
  ) { }

  ngOnInit() {
    this.events.subscribe('login', () => {
      this.init()
    })
    this.init()
  }
  async init() {
    this.user = await this.nativeStorage.getItem('userInfo')
    this.listData = []
    this.total_count = 0
    this.filter = {
      actTerm: '',
      page_index: 0,
      page_size: 20,
    }
    this.loadData()
  }


  async loadData(event?, isNew = true, isRefresh = false) {
    if (!isNew && !isRefresh) {
      if ((this.filter.page_index * this.filter.page_size) >= this.total_count) {

        if (event && event.target) {
          event.target.complete();
        }
        return
      }
    }
    await this.loadingService.start()
    let empty = false
    try {
      const { actTerm, page_index } = this.filter
      let page = page_index
      if (isNew) {
        page = 1
        this.filter.page_index = 1
        this.toggleInfiniteScroll(false)
      }

      let user
      try {
        user = await this.nativeStorage.getItem('userInfo')
      } catch (e) {
        await this.loadingService.end()
        this.utils.showMsg('登錄失效，請重新登錄')
        if (event && event.target) {
          event.target.complete();
        }
        return
      }


      const actYear = user.year_id
      const userNo = user.user_no

      const res: any = await this.activityService.fetchStudentActivity({
        userNo,
        actYear,
        actTerm,
        page
      })
      console.log(res)
      if (isNew) {
        this.listData = res.datas
      } else {
        this.listData.push(...res.datas)
      }
      this.total_count = res.count

    } catch (e) {
      if (e && e.code) {
        if (e.code === 3002) {
          empty = true
          // await this.utils.showMsg(e.desc)
          if (isNew) {
            this.listData = []
          }
        }
      }
      console.error(e)
    }
    if (event && event.target) {
      event.target.complete();
      if (!isRefresh) { // !isRefresh 下拉刷新不需要禁用
        event.target.disabled = empty || (this.filter.page_index * this.filter.page_size) >= this.total_count
      }
    } else {
      this.toggleInfiniteScroll(empty || (this.filter.page_index * this.filter.page_size) >= this.total_count)
    }
    if (isNew) {
      this.content.scrollToTop()
    }
    this.filter.page_index += 1
    await this.loadingService.end()
  }
  toggleInfiniteScroll(disabled) {
    console.log(disabled)
    this.infiniteScroll.disabled = disabled
  }

  getStatusClass(actStatus) {
    switch (actStatus) {
      case '未完成':
        return 'warning'
      case '完成':
        return 'success'
    }
    return ''
  }

  toDetail(item) {
    this.navCtrl.setDirection('forward');
    this.router.navigate(['activity-detail-student', item.id], { queryParams: { view: 'view' }})
  }
}
