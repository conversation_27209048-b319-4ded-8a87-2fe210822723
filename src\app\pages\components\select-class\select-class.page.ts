import {Component, Input, OnInit} from '@angular/core';
import {ModalController} from '@ionic/angular';
import { MasterService } from '@services/api/master.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {LoadingService} from '@app/utils/loading.service';
import {SelectStudentPage} from '@pages/components/select-student/select-student.page';
// import {SelectStudentService} from '@services/utils/select-student.service';

@Component({
  selector: 'app-select-class',
  templateUrl: './select-class.page.html',
  styleUrls: ['./select-class.page.scss'],
})
export class SelectClassPage implements OnInit {
  @Input() domId = 'select-class-page'
  public class = ''
  public student = ''
  public classes: any = []
  constructor(
    private modalCtrl: ModalController,
    private nativeStorage: NativeStorage,
    private master: MasterService,
    private loadingService: LoadingService,
    // public selectStudentService: SelectStudentService,
  ) { }

  ngOnInit() {
    this.initClasses()
  }

  async initClasses() {
    let yearId = 0
    try {
      const user = await this.nativeStorage.getItem('userInfo')
      yearId = user.year_id
    } catch (e) {
      console.error(e)
      return
    }
    if (!yearId) {
      return
    }
    await this.loadingService.start()
    try {
      const res: any = await this.master.fetchClasses({ yearId })
      console.log(res)
      this.classes = this.formatClasses(res.class)
    } catch (e) {
      console.error(e)
    }

    await this.loadingService.end()
  }
  formatClasses(data) {
    const arr = []
    let index = -1
    let grade = 0
    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      if (grade !== item.classGrade) {
        index++
      }
      grade = item.classGrade
      if (!Array.isArray(arr[index])) {
        arr[index] = []
      }
      arr[index].push(item)
    }
    return arr
  }

  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true
    });
  }
  onSave() {
    this.modalCtrl.dismiss({
      save: true,
      class: this.class,
      student: this.student,
    });
  }

  async onCheckClass(c) {
    try {
      const res: any = await this.showStudent(c.id)
      console.log(res)
      if (res.save) {
        this.modalCtrl.dismiss(
          {
          save: true,
          class: c,
          student: res.student,
        }, undefined, this.domId);
      }
    } catch (e) {
      console.error(e)
    }
  }



  async showStudent(classId) {
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: SelectStudentPage,
      componentProps: {
        domId: 'student-select-modal',
        classId,
      },
      id: 'student-select-modal'
    })
    await modal.present()
    const res = await modal.onDidDismiss()
    if (res.data.save) {
      return { save: true, student: res.data.student }
    }
    return {}
  }
}
