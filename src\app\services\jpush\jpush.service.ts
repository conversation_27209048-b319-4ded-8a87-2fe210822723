import { Injectable } from '@angular/core';
import {AlertController, Platform, NavController} from '@ionic/angular';
import { Router } from '@angular/router';
// import { JPush } from '@jiguang-ionic/jpush/ngx';
import {TranslateService} from '@ngx-translate/core';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {isNumber} from 'util';
import {PushService} from '@services/api/push.service';
import {UtilsService} from '@app/utils/utils.service';
import { Device } from '@ionic-native/device/ngx'; // 設備
import md5 from 'md5'
import {sleep} from '@app/utils';
import {Events} from '@app/services/events/event.service';
import {FirebaseX} from '@ionic-native/firebase-x/ngx';
import { LocalNotifications } from '@awesome-cordova-plugins/local-notifications/ngx';
// const JPush = {}
@Injectable({
  providedIn: 'root'
})
export class JpushService {

  sequence = 0;
  public registrationId = '';
  constructor(private platform: Platform,
              // private jPush: UtilsService,
              private router: Router,
              private alertController: AlertController,
              public translate: TranslateService,
              private nativeStorage: NativeStorage,
              private events: Events,
              private nav: NavController,
              private pushService: PushService,
              private utils: UtilsService,
              private device: Device,
              private firebaseX: FirebaseX,
              private localNotifications: LocalNotifications,
  ) {
  }

  getId() {
    return new Promise((resolve, reject) => {
      if (!this.platform.is('mobile')) {
        reject();
        return
      }
      // if (this.registrationId) {
      //   resolve(this.registrationId)
      //   return
      // }
      // this.jPush.init()
      // this.jPush.getRegistrationID().then(res => {
      //   resolve(res + '')
      // }).catch(err => {
      //   reject(err)
      // })
      this.firebaseX.getToken()
          .then(token => {
            console.log(`[firebaseX] -- The token is ${token}`)
            // alert(`[firebaseX] -- The token is ${token}`)
            resolve(token)
          }) // save the token server-side and use it to push notifications to this device
          .catch(error => {
            console.log('[firebaseX] -- Error getting token' + error.toString())
            // alert('[firebaseX] -- Error getting 12token' + error.toString())
            reject(error)
          });

      // let id = 'eca_app_' + new Date().getTime()
      // if (this.device && this.device.uuid) {
      //   id = md5(this.device.uuid)
      // }
      // resolve(id)
    })
  }

  async initJpush() {

    if (!this.platform.is('mobile')) {
      return;
    }

    try {
      const fr = window.localStorage.getItem('firstRun')
      // console.log('fr', fr)
      if (fr === null) {
        // if (!await this.clear()) {
        //   this.utils.showToast({ msg: '清除登記失敗1' })
        //   if (await this.clear()) {
        //     this.utils.showToast({ msg: '清除登記成功2' })
        //   } else {
        //     this.utils.showToast({ msg: '清除登記失敗2' })
        //   }
        // } else {
        //   this.utils.showToast({ msg: '清除登記成功1' })
        // }
        let isClear = false
        const retryNum = 10
        for (let i = 0; i < retryNum; i++) {
          if (isClear) {
            break
          }
          // this.utils.showToast({ msg: '清除登記' + i })
          if (await this.clear()) {
            // this.utils.showToast({ msg: '清除登記成功' + i })
            isClear = true
          }
          if (i === retryNum - 1) {
            this.utils.showToast({ msg: '推送程式初始化失敗，請退出重試' })
          }
        }
      }
    } catch (e) {
      console.error(e)
    }

    this.jPushAddEventListener()
    // this.jPush.init();
    // this.jPush.setDebugMode(false);
    // this.jPushAddEventListener();

    // this.setTags(this.platform.platforms());
    // await this.deleteAlias()

    // this.jPush.getAlias({ sequence: 1}).then(res => {
    //   debugger
    // })
  }
  clearBadge() {
    // this.localNotifications.update({ badge: 0 })
    this.firebaseX.setBadgeNumber(0)
  }
  async clear() {
    try {
      await sleep(1000)
      let id = await this.getId()
      if (id) {
        await sleep(1000)
        id = await this.getId()
        await this.pushService.clearPush(id + '')
        // this.utils.showMsg('第一次運行，清除推送設備')
        window.localStorage.setItem('firstRun', '1')
        try {
          // this.jPush.init()
          // await this.jPush.deleteAlias({ sequence: 1 })
        } catch (e) {
          console.error(e)
        }
        return true
      }
    } catch (e) {
      console.error(e)
      return false
    }
    return false
  }
  async openPermissionTips() {
    try {
      const alert = await this.alertController.create({
        header: this.translate.instant('MESSAGE.PUSH_TITLE'),
        message: this.translate.instant('MESSAGE.PUSH_CONTENT'),
        buttons: [
          {
            text: this.translate.instant('BUTTON.CONFIRM'),
            role: 'cancel',
            handler: (blah) => {
              // console.log('Confirm Cancel: blah');
            }
          },
          // {
          //   text: '是',
          //   handler: () => {
          //     // console.log('Confirm Okay');
          //     this.handlePass()
          //   }
          // }
        ]
      });

      await alert.present();
    } catch (e) {

    }
  }

  public jPushAddEventListener() {

    try {
      // 判断系统设置中是否允许当前应用推送
      // this.jPush.getUserNotificationSettings().then(result => {
      //   if (result === 0) {
      //     console.log('系统设置中已关闭应用推送');
      //     this.platform.ready()
      //       .then(() => {
      //         setTimeout(() => {
      //           this.openPermissionTips()
      //         }, 1000)
      //       })
      //   } else if (result > 0) {
      //     console.log('系统设置中打开了应用推送');
      //   }
      // });

      this.firebaseX.hasPermission().then((hasPermission) => {
        if (!hasPermission) {
            this.firebaseX.grantPermission().then((hasPermission2) => {
                console.log('2Permission was ' + (hasPermission ? 'granted' : 'denied'));
            }).catch(err => {
                console.log('系统设置中已关闭应用推送');
                this.platform.ready()
                    .then(() => {
                        setTimeout(() => {
                            this.openPermissionTips()
                        }, 1000)
                    })
            })
        }
        console.log('Permission is ' + (hasPermission ? 'granted' : 'denied'));
      });
    } catch (error) {
      console.log(error);
    }

    // document.addEventListener('jpush.receiveRegistrationId', (event: any) => {
    //   console.log('黄' + JSON.stringify(event));
    //   if (event && event.registrationId) {
    //     this.registrationId = event.registrationId
    //   }
    // }, false)
    this.getId().then(token => {
      this.registrationId = token + ''
    })

    this.firebaseX.onTokenRefresh()
        .subscribe((token: string) => {
          console.log(`[firebaseX] -- Got a new token ${token}`)
          this.registrationId = token
        });
    this.firebaseX.onMessageReceived()
        .subscribe(async data => {
          console.log(`[firebaseX] -- User opened a notification `, data)
          this.clearBadge()
          let user: any = {}
          try {
            user = await this.nativeStorage.getItem('userInfo')
            if (!user || !user.user_id) {
              return
            }
          } catch (e) {
            return
          }
          if (data && data.hasOwnProperty('tap')) {
            // 點擊推送消息 foreground | background

            // 活動報名：activity
            // 我的活動：my-activity
            // 通告：notice

            // id: "710"
            // role: "student"
            // type: "notice"
            switch (data.type) {
              case 'notice':
                this.events.publish('notice:reload');
                if (data.role === 'student') {
                  // this.nav.navigateRoot('/tab-student/notice')
                  this.nav.setDirection('forward')
                  // this.router.navigate(['/tab-student/notice'])
                  this.router.navigate(['notice-detail', data.id])
                } else {
                  // this.nav.navigateRoot('/tab-teacher/notice')
                  this.nav.setDirection('forward')
                  // this.router.navigate(['/tab-teacher/notice'])
                  this.router.navigate(['notice-detail', data.id])
                }
                break
              case 'activity':
                if (data.role === 'student') {
                  // this.nav.navigateRoot('/tab-student/activity')
                  this.nav.setDirection('forward')
                  this.router.navigate(['activity-detail-student', data.id])
                } else {
                  // this.nav.navigateRoot('/tab-teacher/activity')
                  this.nav.setDirection('forward')
                  this.router.navigate(['activity-detail', data.id])
                }
                break
              case 'my-activity':
                if (data.role === 'student') {
                  // this.nav.navigateRoot('/tab-student/my-activity')
                  this.nav.setDirection('forward')
                  this.router.navigate(['activity-detail-student', data.id])
                } else {
                  this.router.navigate(['activity-detail', data.id])
                }
                break
            }
          }
        });


    // 点击通知进入应用程序时会触发的事件
    // document.addEventListener('jpush.openNotification', async (event: any) => {
    //   // const content = this.platform.is('ios') ? event.aps.alert : event.alert;
    //   let user: any = {}
    //   try {
    //     user = await this.nativeStorage.getItem('userInfo')
    //     if (!user || !user.user_id) {
    //       return
    //     }
    //   } catch (e) {
    //     return
    //   }
    //
    //   if (event && event.extras && event.extras['cn.jpush.android.EXTRA']) {
    //     const data = event.extras['cn.jpush.android.EXTRA']
    //     //  活動報名：activity
    //     // 我的活動：my-activity
    //     // 通告：notice
    //
    //     // id: "710"
    //     // role: "student"
    //     // type: "notice"
    //     switch (data.type) {
    //       case 'notice':
    //         this.events.publish('notice:reload');
    //         if (data.role === 'student') {
    //           // this.nav.navigateRoot('/tab-student/notice')
    //           this.nav.setDirection('forward')
    //           // this.router.navigate(['/tab-student/notice'])
    //           this.router.navigate(['notice-detail', data.id])
    //         } else {
    //           // this.nav.navigateRoot('/tab-teacher/notice')
    //           this.nav.setDirection('forward')
    //           // this.router.navigate(['/tab-teacher/notice'])
    //           this.router.navigate(['notice-detail', data.id])
    //         }
    //         break
    //       case 'activity':
    //         if (data.role === 'student') {
    //           // this.nav.navigateRoot('/tab-student/activity')
    //           this.nav.setDirection('forward')
    //           this.router.navigate(['activity-detail-student', data.id])
    //         } else {
    //           // this.nav.navigateRoot('/tab-teacher/activity')
    //           this.nav.setDirection('forward')
    //           this.router.navigate(['activity-detail', data.id])
    //         }
    //         break
    //       case 'my-activity':
    //         if (data.role === 'student') {
    //           // this.nav.navigateRoot('/tab-student/my-activity')
    //           this.nav.setDirection('forward')
    //           this.router.navigate(['activity-detail-student', data.id])
    //         } else {
    //           this.router.navigate(['activity-detail', data.id])
    //         }
    //         break
    //     }
    //
    //   }
    //
    //   // this.nativeStorage.getItem('userInfo').then(user => {
    //   //   if (user && user.user_id) {
    //   //
    //   //     if (user.user_type === 'student') {
    //   //       this.router.navigate(['/tab-student/notice'])
    //   //     } else {
    //   //       this.router.navigate(['/tab-teacher/notice'])
    //   //     }
    //   //   } else {
    //   //
    //   //     this.router.navigate(['login'])
    //   //   }
    //   // }).catch(err => {
    //   //   console.log(err)
    //   // })
    //   console.log('黄 点击通知事件' + JSON.stringify(event));
    // }, false);
  }

  setTags(items: string[]) {
    // this.jpush
    //   .setTags({ sequence: this.sequence++, tags: ["Tag1", "Tag2"] })
    //   .then(this.tagResultHandler)
    //   .catch(this.errorHandler);
    // this.jPush.setTags({ sequence: this.sequence++, tags: items }).then(this.tagResultHandler)
    //   .catch(this.errorHandler); ;
  }

  errorHandler(err) {
    let sequence: number = err.sequence;
    if (sequence === undefined) {
      sequence = 0;
    }
    let code = err.code;
    if (code === undefined) {
      code = 0;
    }
    console.error('Error!' + '\nSequence: ' + sequence + '\nCode: ' + code);
  };


  tagResultHandler(result) {
    const sequence: number = result.sequence;
    const tags: Array<string> = result.tags == null ? [] : result.tags;
    console.log(tags);
    console.log('Success!' + '\nSequence: ' + sequence + '\nTags: ' + tags.toString());
  };


  // 设置别名,一个用户只有一个别名
  public async setAlias(userId) {
    try {
      if (!this.platform.is('mobile')) {
        return;
      }
      console.log('設置推送Alias為：' + userId)
      // ios设置setAlias有bug,值必须为string类型,不能是number类型
      if (userId === null || userId === undefined || userId === '') {
        console.log('清除Alias')
        try {
          // this.jPush.deleteAlias({sequence: 2})
          console.log('清除成功')
        } catch (e) {
          console.log('清除失敗', e)
        }
      } else {
        try {
          // const res = await this.jPush.setAlias({
          //   sequence: 1,
          //   alias: userId + ''
          // });
          // console.log('設置Alias:', res)
        } catch (e) {
          this.errorHandler(e)
        }
      }
    } catch (error) {
      console.log(error);
    }

  }

  async deleteAlias() {
    try {
      // await this.jPush.deleteAlias({ sequence: 0 })
    } catch (e) {
      console.error(e)
    }
  }


  handleNotice(data) {

  }

  setBadge(num: number) {
    // this.jPush.setApplicationIconBadgeNumber(num)
  }
  resetBadge() {
    // this.jPush.resetBadge()
    // this.jPush.setApplicationIconBadgeNumber(0)
  }
}
