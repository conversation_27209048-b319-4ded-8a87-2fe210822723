import { Injectable } from '@angular/core';
import {SqlService} from '@services/sql/sql.service'
import { dateFormat} from '@app/utils';
import {Platform} from '@ionic/angular';
import {ActivityService} from '@services/api/activity.service';

@Injectable({
  providedIn: 'root'
})
export class RollCallService {

  constructor(
    private sql: SqlService,
    private platform: Platform,
    private activityService: ActivityService,
    ) {
    this.platform.ready()
      .then(async () => {
        this.sql.init()
      })
  }
  get isInit() {
    return this.sql.isInit
  }

  getCount() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT count(id) as count FROM tbl_roll_call', [],
          (x, rs) => {
            console.log(rs)
            const res = rs.rows.item(0)
            resolve(res.count)
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  all() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_roll_call order by id desc', [],
          (x, rs) => {
            const arr = []
            for (let i = 0; i < rs.rows.length; i++) {
              const item = rs.rows.item(i)
              console.log('sql row:', item)

              arr.push({
                id: item.id, // 本地ID

                activity_id: item.activity_id,
                actCode: item.actCode,
                actName: item.actName,
                actEname: item.actEname,
                actTeacher: item.actTeacher,
                cateCode: item.cateCode,
                cateName: item.cateName,
                cateEnName: item.cateEnName,
                activity_date: JSON.parse(item.activity_date),
                activityInfo: JSON.parse(item.activityInfo),
                lottery: JSON.parse(item.lottery),
              })
            }
            resolve(arr)
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  get(id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_roll_call WHERE id=(?)', [id],
          (x, rs) => {
            const item = rs.rows.item(0)
            console.log('sql row:', item)

            const data = {
                id: item.id, // 本地ID

                activity_id: item.activity_id,
                actCode: item.actCode,
                actName: item.actName,
                actEname: item.actEname,
                actTeacher: item.actTeacher,
                cateCode: item.cateCode,
                cateName: item.cateName,
                cateEnName: item.cateEnName,
                activity_date: JSON.parse(item.activity_date),
                activityInfo: JSON.parse(item.activityInfo),
                lottery: JSON.parse(item.lottery),
              }
            resolve(data)
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  getByActId(activity_id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_roll_call WHERE activity_id=(?)', [activity_id],
          (x, rs) => {
          if (rs.rows.length > 0) {
            const item = rs.rows.item(0)
            console.log('sql row:', item)

            const data = {
              id: item.id, // 本地ID

              activity_id: item.activity_id,
              actCode: item.actCode,
              actName: item.actName,
              actEname: item.actEname,
              actTeacher: item.actTeacher,
              cateCode: item.cateCode,
              cateName: item.cateName,
              cateEnName: item.cateEnName,
              activity_date: JSON.parse(item.activity_date),
              activityInfo: JSON.parse(item.activityInfo),
              lottery: JSON.parse(item.lottery),
            }
            resolve(data)
          } else {
            resolve(null)
          }
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  add({ activity_id, actCode, actName, actEname, actTeacher, cateCode, cateName, cateEnName, activity_date,
        activityInfo,
        lottery }) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'INSERT INTO tbl_roll_call (activity_id, actCode, actName, actEname, actTeacher, cateCode, cateName, cateEnName, activity_date, ' +
          'activityInfo, lottery ) VALUES (?,?,?,?,?,?,?,?,?,?,?)',

          [activity_id, actCode, actName, actEname, actTeacher, cateCode, cateName, cateEnName, JSON.stringify(activity_date),
            JSON.stringify(activityInfo), JSON.stringify(lottery)],
          (x, rs) => {
            this.all().then(arr => {
              resolve(arr)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  delete(id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM tbl_roll_call WHERE id=(?)',

          [id],
          (x, rs) => {
            this.all().then(arr => {
              resolve(arr)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  deleteByActId(activity_id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM tbl_roll_call WHERE activity_id=(?)',

          [activity_id],
          (x, rs) => {
            this.all().then(arr => {
              resolve(arr)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  deleteByCode(actCode) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM tbl_roll_call WHERE actCode=(?)',
          [actCode],
          (x, rs) => {
            this.all().then(arr => {
               resolve(arr)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  update({ id, activity_id, actCode, actName, actEname, actTeacher, cateCode, cateName, cateEnName, activity_date,
         activityInfo,
         lottery }) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'UPDATE tbl_roll_call SET activity_id=(?),actCode=(?),actName=(?),actEname=(?),actTeacher=(?),cateCode=(?),cateName=(?),cateEnName=(?),activity_date=(?),activityInfo=(?),lottery=(?) WHERE id=(?)',

          [activity_id, actCode, actName, actEname, actTeacher, cateCode, cateName, cateEnName, JSON.stringify(activity_date), JSON.stringify(activityInfo), JSON.stringify(lottery), id],
          (x, rs) => {
            this.all().then(arr => {
              resolve(arr)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  updateRollCall({ id, activity_date }) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'UPDATE tbl_roll_call SET activity_date=(?) WHERE id=(?)',

          [JSON.stringify(activity_date), id],
          (x, rs) => {
            this.all().then(arr => {
              resolve(arr)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  async updateActivity(activity_id) {
    try {
      const actInfo: any = await this.activityService.getActivityInfo({id: activity_id})
      const rollCallInfo: any = await this.activityService.getRollCall({ activity_id })
      const lottery: any = await this.activityService.getActivityLottery(activity_id)
      const local: any = await this.getByActId(activity_id)

      debugger
      // @ts-ignore
      for (let i = 0; i < local.activity_date.length; i++) {
        const localItem = local.activity_date[i]
        if (localItem) {
          const cItem = rollCallInfo.activity_date.find( item => item.actKey === localItem.actKey)
          if (cItem) {
            const years = Object.keys(localItem.actDateArray)
            for (let j = 0; j < years.length; j++) {
              const year = years[j]
              // localItem.actDateArray[]
              if (cItem.actDateArray.hasOwnProperty(year)) {
                const yearList = localItem.actDateArray[year] // 本地選擇年

                for (let k = 0; k < yearList.length; k++) {
                  const dayInfo = yearList[k] // 本地日
                  const cDay = cItem.actDateArray[year].find(day => day.date === dayInfo.date) // 新日
                  if (cDay) {
                    for (let l = 0; l < dayInfo.students.length; l++) {
                      const stu = dayInfo.students[l] // 本地學生
                      const cStu = cDay.students.find(s => s.student_id === stu.student_id) // 新學生
                      if (cStu) {
                        cStu.status = stu.status
                      } else {
                        // 新有，舊無
                      }
                    }

                  } else {
                    // cItem.actDateArray.push(dayInfo)
                  }
                }

              } else {
                // 年
                // cItem.actDateArray[year] = localItem.actDateArray[year]
              }
            }

          } else {
            // 活動地點
            // rollCallInfo.activity_date.push(cItem)
          }
        }
      }

      await this.update({
        id: local.id,
        activity_id,
        actCode: rollCallInfo.actCode,
        actName: rollCallInfo.actName,
        actEname: rollCallInfo.actEname,
        actTeacher: rollCallInfo.actTeacher,
        cateCode: rollCallInfo.cateCode,
        cateName: rollCallInfo.cateName,
        cateEnName: rollCallInfo.cateEnName,
        activity_date: rollCallInfo.activity_date,
 
        activityInfo: actInfo,
        lottery,
      })

      // 更新狀態
      await this.activityService.updateReloadStatus([activity_id])

      return true
    } catch (e) {
      console.error(e)
      return false
    }
  }
}
