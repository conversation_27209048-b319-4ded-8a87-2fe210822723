import { Injectable } from '@angular/core';
import {RequestService} from '@services/request/request.service';

@Injectable({
  providedIn: 'root'
})
export class MasterService {

  constructor(public request: RequestService) {
  }

  /**
   * 獲取班級
   */
  fetchClasses({ yearId }: {
    yearId: number, // [integer] 年度ID
  }) {
    return this.request.request({
      url: `/master/class_index`,
      method: 'GET',
      params: {
        yearId
      }
    })
  }


  /**
   * 倒序獲取班級
   */
  fetchClassesDesc({ yearId }: {
    yearId: number, // [integer] 年度ID
  }) {
    return this.request.request({
      url: `/master/class_index_desc`,
      method: 'GET',
      params: {
        yearId
      }
    })
  }

  /**
   * 獲取班內學生
   */
  fetchClassStudent({ id }: {
    id: number, // [integer] 班級的ID
  }) {
    return this.request.request({
      url: `/master/class_student`,
      method: 'GET',
      params: {
        id
      }
    })
  }

  /**
   * ajax_getstudent_all
   */
  getStudentAll({
        code,
        yearId,
        gender
      }: {
    yearId: number, // [integer] yearId
    code?: string, // [string] code
    gender?: string, // [string] gender
  }) {
    return this.request.request({
      url: `/extra/ajax_getstudent_all2`,
      method: 'GET',
      params: {
        code,
        yearId,
        gender
      }

    })
  }


  /**
   * 獲取所有年份
   */
  fetchYears() {
    return this.request.request({
      url: `/ebooking/year_index`,
      method: 'GET',
    })
  }



}
