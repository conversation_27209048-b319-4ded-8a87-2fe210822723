import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RollCallOfflineInputPage } from './roll-call-offline-input.page';

describe('RollCallOfflineInputPage', () => {
  let component: RollCallOfflineInputPage;
  let fixture: ComponentFixture<RollCallOfflineInputPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RollCallOfflineInputPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RollCallOfflineInputPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
