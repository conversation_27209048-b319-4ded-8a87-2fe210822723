import { Injectable } from '@angular/core';
import {RequestService} from '@services/request/request.service';

@Injectable({
  providedIn: 'root'
})
export class SystemMessageService {

  constructor(public request: RequestService) {
  }

  /**
   * 消息列表
   * @param read 已讀 未讀=0,已讀=1,不傳返回所有
   * @param page_index 請求頁碼,從1開始
   * @param page_size 每頁數目
   */
  fetchSystemMessages({ read, page_index, page_size }: any) {
    return this.request.request({
      url: '/system-messages',
      method: 'get',
      params: {
        read,
        page_index,
        page_size
      }
    })
  }

  /**
   * 設置消息已讀
   * @param message_id message_id
   */
  readSystemMessages(message_id: any) {
    return this.request.request({
      url: '/system-messages/actions/read',
      method: 'post',
      data: {
        message_id,
      }
    })
  }
  /**
   * 設置所有消息已讀
   */
  readAllSystemMessages() {
    return this.request.request({
      url: '/system-messages/actions/read-all',
      method: 'post',
    })
  }
}
