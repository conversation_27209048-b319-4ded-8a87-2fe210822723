<ion-header>
  <ion-toolbar>
    <ion-segment [(ngModel)]="tab" (ionChange)="segmentChanged($event)">
      <ion-segment-button value="activity">
        <ion-label>活動</ion-label>
      </ion-segment-button>
      <ion-segment-button value="student">
        <ion-label>學生</ion-label>
      </ion-segment-button>
    </ion-segment>
  </ion-toolbar>
</ion-header>



<!-- 篩選 -->
<!-- 活動 -->
<ion-header *ngIf="tab === 'activity'; else elseTab" class="activity-filter">
  <ion-searchbar
      [(ngModel)]="activityFilter.actCodeOrName"
      [debounce]="300"
      name="keyword"
      showCancelButton="never"
      placeholder="輸入活動編號/活動名稱"
      type="search"
      inputmode="search"
      [ngModelOptions]="{standalone: true}"
      (keyup)="onSearchKeyUp($event)"
      (ionBlur)="loadDataActivity()"
  ></ion-searchbar>
  <!-- (ionChange)="loadDataActivity()" -->
  <ion-row class="row" style="margin-bottom: 3px;">
    <ion-label class="input-label">學期</ion-label>
    <ion-select
        [(ngModel)]="activityFilter.actTerm"
        placeholder="學期"
        okText="確認"
        cancelText="取消"
        interface="popover"
        (ngModelChange)="loadDataActivity()">
      <ion-select-option value="">全部</ion-select-option>
      <ion-select-option value="0">全學期</ion-select-option>
      <ion-select-option value="1">上學期</ion-select-option>
      <ion-select-option value="2">下學期</ion-select-option>
      <ion-select-option value="3">暑假期</ion-select-option>
    </ion-select>
    <ion-label class="input-label">狀態</ion-label>
    <ion-select
        [(ngModel)]="activityFilter.actFinish"
        placeholder="狀態"
        okText="確認"
        cancelText="取消"
        interface="popover"
        (ngModelChange)="loadDataActivity()">
      <ion-select-option value="">全部</ion-select-option>
      <ion-select-option value="0">審核中</ion-select-option>
      <ion-select-option value="2">不通過</ion-select-option>
      <ion-select-option value="3">已發出通告</ion-select-option>
      <ion-select-option value="4_1">篩選中</ion-select-option>
      <ion-select-option value="4_2">開始篩選</ion-select-option>
      <ion-select-option value="5_3">完成篩選</ion-select-option>
      <ion-select-option value="5_2">完成</ion-select-option>
    </ion-select>


  </ion-row>
  <ion-row class="row" style="margin-bottom: 3px;">
    <ion-label class="input-label">類別</ion-label>
    <ion-select
        [(ngModel)]="activityFilter.actCategory"
        placeholder="類別"
        okText="確認"
        cancelText="取消"
        interface="popover"
        (ngModelChange)="loadDataActivity()">
      <ion-select-option value="">全部</ion-select-option>
      <ion-select-option
          *ngFor="let item of categorys"
          [value]="item.id"
      >{{ item.cateName }}</ion-select-option>
    </ion-select>
  </ion-row>
  <ion-row class="row" style="margin-bottom: 3px;">
    <ion-label class="input-label">報名截止日期</ion-label>
    <!--<ion-datetime-->
    <!--    [(ngModel)]="activityFilter.actExpire"-->
    <!--    displayFormat="YYYY/MM/DD"-->
    <!--    min="1990"-->
    <!--    max="2050"-->
    <!--    (ionCancel)="onCancelActExpire()"-->
    <!--&gt;</ion-datetime>-->
    <ion-datetime-button datetime="datetime">
      <span slot="date-target" style="display: inline-block;width: 98px;background: #ffffff;height: 25px;line-height: 25px;border-radius: 7px;">{{ actExpireStr }}</span>
    </ion-datetime-button>

    <ion-modal [keepContentsMounted]="true">
      <ng-template>
        <ion-datetime
          #actDatetime
          [(ngModel)]="activityFilter.actExpire"
          [showDefaultButtons]="true"
          presentation="date"
          id="datetime"
          min="1990"
          max="2050"
          doneText="確認"
          cancelText="清除"
          (ionCancel)="onCancelActExpire(actDatetime)"
        ></ion-datetime>
      </ng-template>
    </ion-modal>
    <ion-button color="search" (click)="loadDataActivity()" >搜尋</ion-button>
  </ion-row>
</ion-header>
<!-- 學生 -->
<ng-template #elseTab>
<ion-header class="student-filter">
  <ion-row class="row" style="margin-bottom: 3px;">
    <ion-label class="input-label">學期</ion-label>
    <ion-select
        [(ngModel)]="studentFilter.actTerm"
        placeholder="學期"
        okText="確認"
        cancelText="取消"
        interface="popover"
        (ngModelChange)="loadDataStudent()">
      <ion-select-option value="">全部</ion-select-option>
      <ion-select-option value="0">全學期</ion-select-option>
      <ion-select-option value="1">上學期</ion-select-option>
      <ion-select-option value="2">下學期</ion-select-option>
      <ion-select-option value="3">暑假期</ion-select-option>
    </ion-select>
    <ion-label class="input-label">學生</ion-label>
    <ion-input [value]="stuInfo" readonly></ion-input>
    <ion-button color="search" (click)="onStudentSelect()" class="stu-select-btn">選擇學生</ion-button>
  </ion-row>
</ion-header>
</ng-template>

<!-- 活動內容 -->
<ion-content #contentActivity *ngIf="tab === 'activity'">

  <ion-refresher slot="fixed" [pullMax]="500" (ionRefresh)="loadDataActivity($event, true, true)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-list #listActivity *ngIf="listDataActivity.length > 0; else emptyTips">
    <ion-item
        *ngFor="let item of listDataActivity"
        detail
        button
        lines="none"
        class="list-item"
        [classList]="'list-item ' + getStatusClass(item.finishStatus, item.actType)"
        (click)="toDetailStudent(item)"
    >
      <div class="item-info">
        <div class="project-name">{{ item.actName }}</div>
        <div class="info">編號：{{ item.actCode }}</div>
        <div class="info">負責老師：{{ item.actTeacher }}</div>
        <div class="info">狀態：{{ getStatus(item.finishStatus, item.actType) }}</div>
        <div class="info">{{ item.actCategory }}</div>
      </div>
    </ion-item>
  </ion-list>

  <ng-template #emptyTips>
    <!--<div class="page-empty-tips"></div>-->
    <div class="empty-tips">
      暫無數據
    </div>
  </ng-template>

  <ion-infinite-scroll #infiniteScrollActivity threshold="10px" (ionInfinite)="loadDataActivity($event, false)">
    <ion-infinite-scroll-content
        loadingSpinner="bubbles"
        loadingText="正在加載...">
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
<!-- 學生內容 -->
<ion-content #contentStudent *ngIf="tab === 'student'">

  <ion-refresher slot="fixed" [pullMax]="500" (ionRefresh)="loadDataStudent($event, true, true)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-list #listStudent *ngIf="listDataStudent.length > 0; else emptyTips">
    <ion-item
        *ngFor="let item of listDataStudent"
        detail
        button
        lines="none"
        class="list-item"
        [classList]="'list-item ' + getStatusClass(item.finishStatus, item.actType)"
        (click)="toDetailStudent(item)"
    >
      <div class="item-info">
        <div class="project-name">{{ item.actName }}</div>
        <div class="info">編號：{{ item.actCode }}</div>
        <div class="info">負責老師：{{ item.actTeacher }}</div>
        <div class="info">狀態：{{ getStatus(item.finishStatus, item.actType) }}</div>
        <div class="info">歸程方式：{{ item.backInfo }}</div>
        <div *ngIf="item.actWeekly" class="info">逢星期：{{ item.actWeekly }}</div>
        <!--<div class="info">{{ item.actCategory }}</div>-->
      </div>
    </ion-item>
  </ion-list>

  <ng-template #emptyTips>
    <div class="empty-tips">
      暫無數據
    </div>
  </ng-template>
</ion-content>
