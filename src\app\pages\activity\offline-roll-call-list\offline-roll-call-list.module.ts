import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { OfflineRollCallListPage } from './offline-roll-call-list.page';

const routes: Routes = [
  {
    path: '',
    component: OfflineRollCallListPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [OfflineRollCallListPage]
})
export class OfflineRollCallListPageModule {}
