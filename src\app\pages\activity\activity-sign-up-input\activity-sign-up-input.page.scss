ion-content {
  --background: #FFFFFF;

  .project-box, .sup-box, .back-box, .input-box {
    background: #fafafa;
    border-radius: 0.2rem;
    font-size: 0.8rem;


    width: 90%;
    width: calc(100% - 1rem);
    margin: 0.3rem auto;
    padding: 0.7rem 0.5rem;

    .item {
      display: flex;
      margin-bottom: 0.2rem;
    }



    .label {
      margin-bottom: 0.5rem;
    }
    .tips {
      font-size: 0.6rem;
      color: #6e6e6e;
      margin-bottom: 0.5rem;
    }

    .list {
      ion-checkbox, ion-radio {
        margin-right: 0.5rem;
        min-width: 20px;
        min-height: 20px;
      }
    }
    ion-input {
      background: #ffffff;
      border: 1px solid #EDEDED;
      border-radius: 5px;
      width: 6rem;
      height: 1.25rem;
      line-height: 1.05rem;
    }

  }

}

.radio-label {
  line-height: 150%;
  white-space: unset;
}
.checkbox-label {
  line-height: 150%;
  white-space: unset;
}
