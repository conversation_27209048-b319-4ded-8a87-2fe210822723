import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SystemMessageIndexPage } from './system-message-index.page';

describe('SystemMessageIndexPage', () => {
  let component: SystemMessageIndexPage;
  let fixture: ComponentFixture<SystemMessageIndexPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SystemMessageIndexPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SystemMessageIndexPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
