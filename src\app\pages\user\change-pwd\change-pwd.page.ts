import { Component, OnInit } from '@angular/core';
import { UserService } from '../../../services/api/user.service'
import md5 from 'md5'
import {UtilsService} from '../../../utils/utils.service';
import {StoreService} from '../../../services/store/store.service';

@Component({
  selector: 'app-change-pwd',
  templateUrl: './change-pwd.page.html',
  styleUrls: ['./change-pwd.page.scss'],
})
export class ChangePwdPage implements OnInit {

  public form = {
    oldPwd: '',
    newPwd: '',
    confirmPwd: '',
  }
  constructor(
    private userService: UserService,
    public utils: UtilsService,
    private storeService: StoreService,
  ) { }

  ngOnInit() {
  }

  async onSave() {
    try {
      const new_password = md5(this.form.newPwd)
      const confirmPwd = md5(this.form.confirmPwd)
      if (new_password !== confirmPwd) {
        this.utils.showToast({msg: '新密碼兩次輸入不一致！'})
        return
      }
      const password = md5(this.form.oldPwd)
      await this.userService.resetPassword({
        password,
        new_password
      })
      this.utils.showToast({msg: '密碼修改成功，請重新登錄！' })
      setTimeout(() => {
        this.storeService.Logout()
      }, 500)
    } catch (e) {
      console.error(e)
    }

  }

  get canSubmit() {
    return this.form.oldPwd.length > 0 && this.form.newPwd.length > 0 && this.form.confirmPwd.length > 0
  }

}
