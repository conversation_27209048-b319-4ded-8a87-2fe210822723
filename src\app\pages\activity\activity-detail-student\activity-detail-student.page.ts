import { Component, EventEmitter, OnInit, ChangeDetectorRef } from '@angular/core';
import {ActionSheetController, ModalController, NavController, Platform} from '@ionic/angular';
import {ActivatedRoute} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import {ActivityService} from '@services/api/activity.service';
import {StorageService} from '@services/storage/storage.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {TranslateService} from '@ngx-translate/core';
import {dateFormat} from '@app/utils';
import {switchAll} from 'rxjs/operators';
import { ActivitySignUpInputPage} from '@pages/activity/activity-sign-up-input/activity-sign-up-input.page';
// import { PreviewAnyFile  } from '@ionic-native/preview-any-file/ngx';
import {OpenFileService} from '@services/request/open-file.service';
import {Events} from '@app/services/events/event.service';

@Component({
  selector: 'app-activity-detail-student',
  templateUrl: './activity-detail-student.page.html',
  styleUrls: ['./activity-detail-student.page.scss'],
})
export class ActivityDetailStudentPage implements OnInit {

  public id: any = ''
  public data: any = {}
  public tab = 'activity'
  public lottery: any = {}
  public stuListType = 'actStu'

  public rollCallData: any = {}
  public stuLottery: any = {}
  public user: any = {}
  public pageType = 'view'
  public status = ''
  constructor(
    private platform: Platform,
    private routeInfo: ActivatedRoute,
    public nav: NavController,
    private loadingService: LoadingService,
    private activityService: ActivityService,
    public actionSheetController: ActionSheetController,
    private modalCtrl: ModalController,
    private storageService: StorageService,
    private nativeStorage: NativeStorage,
    private utils: UtilsService,
    public translate: TranslateService,
    // public previewAnyFile: PreviewAnyFile,
    public openFileService: OpenFileService,
    protected cdr: ChangeDetectorRef,
    protected events: Events,
    ) { }

  ngOnInit() {
    this.platform.ready().then(async () => {
      // this.statusBar.backgroundColorByHexString('#003658')
      // this.id = this.routeInfo.snapshot.queryParams['id']
      this.id = this.routeInfo.snapshot.params.id
      if (this.routeInfo.snapshot.queryParams && this.routeInfo.snapshot.queryParams.view) {
        this.pageType = this.routeInfo.snapshot.queryParams.view
      }
      this.loadData()
      this.user = await this.nativeStorage.getItem('userInfo')
    })
  }
  // public ngAfterViewInit(): void {
  //   // Bug: https://github.com/ionic-team/ionic/issues/19289
  //   setTimeout(() => this.cdr.markForCheck());
  // }

  get isView() {
    return this.pageType === 'view'
  }

  async loadData() {
    await this.loadingService.start()
    try {
      const res: any = await this.activityService.getActivityInfo({ id: this.id })
      console.log(res)

      const w = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

      if (res && res.activityWT) {
        res.activityWT.forEach(item => {
          const date = []
          item.dateArr = []
          // tslint:disable-next-line:forin
          for (const year in item.actDateArray) {

              const y = { year, months: [] }
              y.year = year

              let month = -1
              let monthIndex = -1
              for (const dateStr of item.actDateArray[year]) {
                const d = new Date(dateStr)
                if (month !== d.getMonth()) {
                  monthIndex++
                }
                month = d.getMonth()

                if (!Array.isArray(y.months[monthIndex])) {
                  y.months[monthIndex] = []
                }
                const dateCn = this.getDayStr(d, 'cn')
                const dateEn = this.getDayStr(d, 'en')
                const isToday = dateFormat(new Date()) === dateFormat(d)
                const week = w[d.getDay()]

                y.months[monthIndex].push({
                  date: dateStr,
                  dateCn,
                  dateEn,
                  week,
                  isToday
                })
              }

              item.dateArr.push(y)

            }

        })
      }


      this.data = res


      await this.getStatus()
      await this.initRollCall(this.id)
      await this.initLottery()
      await this.initStudentLottery()

    } catch (e) {
      console.error(e)
    }

    await this.loadingService.end()
  }

  async getStatus() {
  //  /v2/app/student/activities?_format=json&userNo=a174515&actYear=5&activity_id=2430&page=1

    try {
      const userNo = this.user.user_no
      const actYear = this.user.year_id
      const activity_id = this.id
      const res: any = await this.activityService.fetchStudentActivities({ userNo, actYear, activity_id })
      console.log(res)
      if (res && res.datas && res.datas.length === 1) {
        const act = res.datas[0]
        this.status = act.status + ''
      } else {
        this.status = '3' // 沒有找到記錄
      }
    } catch (e) {
      console.error(e)
    }
  }


  // 點名信息
  async initRollCall(activity_id) {
    try {
      const rollCallData: any = await this.activityService.getRollCall({ activity_id })

      this.rollCallData = rollCallData
    } catch (e) {
      console.error(e)
    }
  }
  // 歸程信息
  async initStudentLottery() {
    try {
      const userNo = this.user.user_no
      const actId = this.id
      const stuLottery: any = await this.activityService.getStudentLottery({ userNo, actId })

      this.stuLottery = stuLottery
      // this.stuLottery = {back: '1111', sup: null}
    } catch (e) {
      console.error(e)
    }
  }
  // 學生名單
  async initLottery() {
    try {
      const lottery: any = await this.activityService.getActivityLottery(this.id)
      // 報名名單
      const actStuList = []
      // tslint:disable-next-line:forin
      for (const grade in lottery.actStu) {
          const g = grade.split('_')
          if (g.length === 2) {
            const c = {
              class: g[0],
              list: []
            }
            c.list = lottery.actStu[grade].map(i => Object.assign(i))
            actStuList.push(c)
          }
      }
      lottery.actStuList = this.getStuList(lottery.actStu)
      lottery.stuLotteryList = this.getStuList(lottery.stuLottery)
      lottery.stuNotLotteryList = this.getStuList(lottery.stuNotLotteryWithClass)
      lottery.stuNotAgreeList = this.getStuList(lottery.stuNotAgree)
      lottery.stuNotReplyList = this.getStuList(lottery.stuNotReply)


      this.lottery = lottery
    } catch (e) {
      console.error(e)
    }
  }
  getStuList(data) {
    const actStuList = []
    // tslint:disable-next-line:forin
    for (const grade in data) {
        const g = grade.split('_')
        if (g.length === 2) {
          const c = {
            class: g[0],
            list: []
          }
          c.list = data[grade].map(i => Object.assign(i))
          actStuList.push(c)
        }
    }
    return actStuList
  }
  getDayStr(day, type = 'cn') {
    if (type === 'cn') {
      return dateFormat(day, 'MM月dd日')// + ' ' + wStr
    } else {
      return dateFormat(day, 'MM月dd日')// + ' ' + wStr
    }
  }


  get showDown() {
    if (this.user && typeof this.user.user_type === 'string') {
      const user_type = this.user.user_type.toUpperCase()
      return user_type === 'ADMIN' || user_type === 'TEACHER' || user_type === 'STAFF' || user_type === 'OUTTEACHER'
    }
    return false
  }

  get showAction() {
    // if (this.status) {
    //   return this.status === '0' || this.status === '2'
    // }
    return this.canAgree || this.canNotAgree
  }
  get canAgree() {
    if (this.status) {
      return this.status === '0'
    }
    return false
  }
  get canNotAgree() {
    const status = this.status
    if (status) {
      if (status === '0') {
        return true
      } else if (status === '4' || status === '5') {
        const activity = this.data && this.data.activity
        const type = activity && activity.actType || ''
        let expire: any = ''
        if (type === '7') {
          const expireStr = activity.actExpire2.replace(new RegExp(/-/gm), '/')
          expire = new Date(expireStr)
        } else if (type === '1' || type === '6') {
          const expireStr = activity.actExpire.replace(new RegExp(/-/gm), '/')
          expire = new Date(expireStr)
        }
        if (expire) {
          return new Date() < expire
        } else {
          return false
        }
      }
    }
    return false
  }
  get showProject() {
    if (this.data && this.data.actPjCId && this.data.actPjCId.length > 0) {
      if (this.data.actPjCId.length === 1) {
        const id = this.data.actPjCId[0] + ''
        return id !== '0'
      } else {
        return true
      }
    }
    return false
  }

  get title() {
    if (this.data && this.data.activity) {
      return this.data.activity.actName
    }
    return this.translate.instant('ACTIVITY.DETAIL.TITLE')
  }
  get actCode() {
    if (!this.data || !this.data.activity) { return '-' }
    return this.data.activity.actCode
  }
  get actTeacher() {
    if (!this.data || !this.data.activity) { return '-' }
    return this.data.activity.actTeacher
  }
  get actType() {
    if (!this.data || !this.data.activity) { return '' }
    switch (this.data.activity.actType) {
      case '1':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T1')
      case '3':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T3')
      case '5':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T5')
      case '6':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T6')
      case '7':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T7')
      case '2':
      case '4':
      default:
        return ''
    }
  }




  /* ------------------------------------------------ */
  async onChangeStudentList() {
    const actionSheet = await this.actionSheetController.create({
      header: this.translate.instant('ACTIVITY.DETAIL.SELECT_LIST_TYPE'),
      buttons: [{
        text: this.translate.instant('ACTIVITY.LIST_TYPE.APP_STU'),
        // role: 'destructive',
        // icon: 'trash',
        handler: () => {
          this.stuListType = 'actStu'
          console.log('報名名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.LOTTERY'),
        // icon: 'share',
        handler: () => {
          this.stuListType = 'stuLottery'
          console.log('取錄的學生名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.NOT_LOTTERY'),
        // icon: 'arrow-dropright-circle',
        handler: () => {
          this.stuListType = 'stuNotLottery'
          console.log('未取錄的學生名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.NOT_AGREE'),
        // icon: 'heart',
        handler: () => {
          this.stuListType = 'stuNotAgree'
          console.log('不同意參加的學生名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.NOT_REPLY'),
        // icon: 'close',
        // role: 'cancel',
        handler: () => {
          this.stuListType = 'stuNotReply'
          console.log('未回復的學生名單');
        }
      }]
    });
    await actionSheet.present();
  }

  get canSelect() {
    if (this.data && this.data.activity) {
      return !(this.data.activity.fsactId === '0' && this.data.activity.fushu === '1')
    }
    return false
  }


  get stuList() {
    switch (this.stuListType) {
      case 'actStu':
        return this.lottery.actStuList
      case 'stuLottery':
        return this.lottery.stuLotteryList
      case 'stuNotLottery':
        return this.lottery.stuNotLotteryList
      case 'stuNotAgree':
        return this.lottery.stuNotAgreeList
      case 'stuNotReply':
        return this.lottery.stuNotReplyList
      default:
        return []
    }
  }

  get stuListTypeStr() {
    switch (this.stuListType) {
      case 'actStu':
        return this.translate.instant('ACTIVITY.LIST_TYPE.APP_STU')
      case 'stuLottery':
        return this.translate.instant('ACTIVITY.LIST_TYPE.LOTTERY')
      case 'stuNotLottery':
        return this.translate.instant('ACTIVITY.LIST_TYPE.NOT_LOTTERY')
      case 'stuNotAgree':
        return this.translate.instant('ACTIVITY.LIST_TYPE.NOT_AGREE')
      case 'stuNotReply':
        return this.translate.instant('ACTIVITY.LIST_TYPE.NOT_REPLY')
      default:
        return this.translate.instant('ACTIVITY.LIST_TYPE.STU')
    }
  }



  segmentChanged($event) {
    this.tab = $event.detail.value
    this.cdr.detectChanges();
  }



  openFile(file) {
    // http://test.esaccount.com
    // /ckttest/activity
    // /web/bundles/extramain/upload/pdf
    // /216b82c8d3684ba348c54.pdf

    const serverInfo = this.storageService.serverSetting
    // this.serverInfo.http + '://' + this.serverInfo.ip + ':' + this.serverInfo.port + '/' +
    // this.serverInfo.remoteProjectName + '/' + this.serverInfo.uri + '/' + this.schoolInfo.schLogo
    let url = ''
    // http://************/EM-Web-PLKCKT/web/bundles/extramain/upload/pdf/e6a06b1648eac4b295d6813d6c010470.pdf
    // url = serverInfo.http + '://' + serverInfo.ip + ':' + serverInfo.port
    // url += '/' + serverInfo.remoteProjectName
    // url = 'http://jmsys.norrayhk.com:88/EM-Web-PLKCKT'
    // url += '/' + serverInfo.uri
    // url += '/' + 'bundles/extramain/upload/pdf/'
    // url += file

    url = serverInfo.web_http + '://' + serverInfo.web_ip + ':' + serverInfo.web_port + '/'
    url += serverInfo.web_remoteProjectName + '/' + serverInfo.web_uri + '/' + 'bundles/extramain/upload/pdf/'
    url += file
    console.log(url)
    if (url) {
      // this.photoViewer.show(url, name, this.options);
      // this.previewAnyFile.preview(url)
      //   .then((res: any) => console.log(res))
      //   .catch((error: any) => console.error(error));
      this.openFileService.showFile(url)
    } else {
      this.utils.showMsg('文件有損壞，無法打開')
    }

    console.log(url)
  }

  async onAgree() {
    if (!this.canAgree) {
      return
    }
    // 確認
    const confirm = await this.utils.confirm(
      this.data.activity.actFirstRemark,
      `【${this.data.activity.actFirstRemark}】參加【${this.data.activity.actName}】?`)
    if (!confirm) {
      return
    }
    try {


      const userId = this.user.user_id
      const userNo = this.user.user_no
      const actId = this.id
      const agree = 'Y'

      // 檢測
      const status: any = await this.activityService.checkClashStuAgree({ userId, actId })

      // 輸入
      const inputData = await this.inputInfo()
      if (!inputData || !inputData.hasOwnProperty('actPj')) {
        return
      }
      this.loadingService.start()

      // actPj,
      //   actSupport,
      //   actBack,
      //   stuPhoneNum,
      const actPj = inputData.actPj
      const actSupport = inputData.actSupport
      const actBack = inputData.actBack
      const stuPhoneNum = inputData.stuPhoneNum

      // 保存
      await this.activityService.saveStudentAgree({
        userNo,
        actId,
        agree,
        actPj
      })
      await this.activityService.saveStudentNotice({
        userNo,
        actId,
        actPj,
        actSupport,
        actBack,
        stuPhoneNum
      })
      this.changeListStatus(actId, '4') // 修改列表狀態
      await this.loadingService.end()
      await this.loadData()
      this.utils.showMsg('提交成功')
    } catch (e) {

      await this.loadingService.end()
      console.error(e)
      if (e && e.code) {
        switch (e.code) {
          case 4001:
          case 4002:
          case 4003:
            await this.utils.showMsg(e.desc)
            return
          default:
            if (e.desc) {
              await this.utils.showMsg(e.desc)
              return
            }
            break
        }
      }
      await this.utils.showMsg('提交失敗')
    }

  }

  async onNotAgree() {
    if (!this.canNotAgree) {
      return
    }
    const confirm = await this.utils.confirm(
      this.data.activity.actSecRemark,
      `【${this.data.activity.actSecRemark}】參加【${this.data.activity.actName}】?`)
    if (!confirm) {
      return
    }


    await this.loadingService.start()
    try {
      const userNo = this.user.user_no
      const actId = this.id
      const agree = 'N'


      // 保存
      await this.activityService.saveStudentAgree({
        userNo,
        actId,
        agree,
        // actPj
      })
      this.changeListStatus(actId, '1') // 修改列表狀態
      // this.utils.showMsg('提交成功')
      await this.loadingService.end()
      await this.loadData()
      this.utils.showMsg('提交成功')
    } catch (e) {
      console.error(e)
    }
    await this.loadingService.end()

  }




async inputInfo() {
    const data = this.data
    const activity_id = this.id
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: ActivitySignUpInputPage,
      componentProps: {
        domId: 'activity-sign-up-input-modal',
        activity_id,
        data,
      },
      id: 'activity-sign-up-input-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    if (res.data.save) {
      return res.data.data
    }
    return {}
  }

  changeListStatus(id, status) {
    this.events.publish('activity:changeStatus', {id, status});
  }
}

