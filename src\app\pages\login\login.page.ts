import {Component, EventEmitter, OnInit, ViewChild} from '@angular/core'
import {ActivatedRoute, Router} from '@angular/router'
import {
  AlertController,
  IonInput,
  LoadingController,
  NavController,
  Platform,
  PopoverController,
  ToastController
} from '@ionic/angular'
import {NativeStorage} from '@ionic-native/native-storage/ngx'
import {Events} from '@app/services/events/event.service';

import {StorageService} from '../../services/storage/storage.service'
import {StoreService} from '../../services/store/store.service'
import {UtilsService} from '../../utils/utils.service'
import {TranslateService} from '@ngx-translate/core'
import { SchoolService } from '@services/api/school.service';
import { JpushService } from '@services/jpush/jpush.service';
import {RollCallService} from '@services/sql/rollCall.service';
import {SqlService} from '@services/sql/sql.service';
import {UserListPopoverComponent} from '@app/components/user-list-popover/user-list-popover.component';
import {UserListService} from '@services/sql/userList.service';
import {LoadingService} from '@app/utils/loading.service';

// import { GooglePlus } from '@ionic-native/google-plus/ngx';
@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage implements OnInit {
  public shouldHeight = document.body.clientHeight - 56 + 'px'
  public subscription: any
  private backButtonPressed = false

  public serverInfo: any
  public store: any
  public canSubmit = false
  public loginForm: any = {
    username: '',
    password: '',
  }
  public schoolInfo: any = {}

  public rollCallCount: any = 0
  public userCount: any = 0

  @ViewChild('pwd', {}) pwdInput: IonInput

  public pageType = 'login'
  public popover = false

  constructor(
    private platform: Platform,
    private router: Router,
    public loadingController: LoadingController,
    private alertController: AlertController,
    private storageService: StorageService,
    private storeService: StoreService,
    private nativeStorage: NativeStorage,
    public toastController: ToastController,
    public nav: NavController,
    public utils: UtilsService,
    public translate: TranslateService,
    public schoolService: SchoolService,
    public rollCallService: RollCallService,
    public sqlService: SqlService,
    public popoverController: PopoverController,
    private routeInfo: ActivatedRoute,
    private userListService: UserListService,
    private events: Events,
    private loading: LoadingService,
    private jpushService: JpushService,
    // private googlePlus: GooglePlus
  ) {
    // 獲取IP設置
    // this.nativeStorage.getItem('serverSettings')
    //   .then(
    //     data => {
    //       debugger
    //       console.log(data)
    //     },
    //     error => {
    //       debugger
    //       console.error(error)
    //       this.toSetting()
    //     }
    //   );
    this.platform.ready()
      .then(async () => {
        await this.loading.start()
        await this.loading.end()
        this.getQueryParams()

        this.initSchoolInfo()
        this.serverInfo = this.storageService.serverSetting
        await this.sqlService.init()
        this.rollCallCount = await this.rollCallService.getCount()
        this.userCount = await this.userListService.getCount()
        // return this.storeService.GetStore()
      })


      // .then(res => {
      //   this.store = res
      // })
  }
  getQueryParams() {
    if (this.routeInfo.snapshot && this.routeInfo.snapshot.queryParams && this.routeInfo.snapshot.queryParams.pageType) {
      this.pageType = this.routeInfo.snapshot.queryParams.pageType
    }
  }

  async ngOnInit() {

    // this.store = await this.storeService.GetStore()
  }
  onBack() {
    this.nav.pop()
  }
  initSchoolInfo() {
    this.schoolService.getSchoolInfo().then((data: any) => {
      console.log(data)
      this.schoolInfo = data
    })
  }

  get logoUrl() {
    // return 'http://test.esaccount.com/ckttest/activity/web/project/images/logo.png'
    if (this.serverInfo && this.schoolInfo && this.schoolInfo.schLogo) {
      return this.serverInfo.http + '://' + this.serverInfo.ip + ':' + this.serverInfo.port + '/' +
        this.serverInfo.remoteProjectName + '/' + this.serverInfo.uri + '/system_image/' + this.schoolInfo.schLogo
    } else {
      return ''
    }
  }

  get schNameCn() {
    if (this.schoolInfo && this.schoolInfo.schUname) {
      return this.schoolInfo.schUname
    } else {
      return ''
    }
  }

  get schNameEn() {
    if (this.schoolInfo && this.schoolInfo.schEname) {
      return this.schoolInfo.schEname
    } else {
      return ''
    }
  }
  get now() {
    return new Date().getTime() + this.schoolInfo.schUname
  }

  get language() {
    // let language = this.storageService.get('language')
    // if (language) {
    //   return language
    // } else {
    //   return 'zh-hk'
    // }
    return 'zh-hk'
  }

  get version() {
    return this.storageService
    && this.storageService.serverSetting
    && this.storageService.serverSetting.version ? this.storageService.serverSetting.version : '-'
  }

  toSetting() {
    // this.router.navigate(['setting'])
  }

  toHome() {
    // this.router.navigate(['tab-student'])
    // this.router.navigate(['tab-student'])
    this.events.publish('login', {}) // ???
    this.nativeStorage.getItem('userInfo').then(user => {
      if (user) {
        if (user.user_type === 'admin' || user.user_type === 'staff') {
          this.nav.navigateRoot('/tab-teacher', { queryParams: { t: new Date().getTime() }})
          return;
        } else if (user.user_type === 'student') {
          this.nav.navigateRoot('/tab-student', { queryParams: { t: new Date().getTime() }})
          return
        }
        this.utils.showMsg('登錄失敗，用戶無登錄權限！')
        return;
      }
      this.utils.showMsg('登錄失敗')
    })
  }

  checkCanSubmit() {
    if (this.loginForm.username.length > 0 && this.loginForm.password.length > 0) {
      this.canSubmit = true
    } else {
      this.canSubmit = false
    }
  }

  async handleSuccess() {
    const toast = await this.toastController.create({
      message: this.translate.instant('LOGIN.LOGIN_SUCCESS'),
      duration: 2000,
      position: 'middle',
    })
    toast.present()
  }

  async handleLogin() {
    if (!this.canSubmit) {
      return
    }


    if (this.loginForm.username !== '' && this.loginForm.password !== '') {
      const username = this.loginForm.username
      const password = this.loginForm.password
      const loading = await this.loadingController.create({
        message: this.translate.instant('LOGIN.LOADING'),
        translucent: true,
        cssClass: 'custom-class custom-loading',
      })
      await loading.present()
      // const token = await this.jpushService.getId()
      // loading.dismiss();
      // this.toHome()
      // return
      this.storeService.Login({username, password}).then(() => {
        const that = this
        // this.storeService.LoginForToken({username, password}).then(() => {
        that.userListService.add({ username, password })
        // this.router.navigate(['setting'])
        loading.dismiss()
        // this.handleSuccess()
        that.saveLastUserName(username)
        // this.router.navigate(['select_system']);
        that.toHome()
        // })
      })
        .catch(async error => {
          console.log(error)
          loading.dismiss()
          let msg = 'Error'
          switch (typeof error) {
            case 'object':
              if (error.desc) {
                msg = error.desc.toString()
              } else {
                msg = JSON.stringify(error)
              }
              break
            case 'bigint':
            case 'boolean':
            case 'number':
            case 'string':
              msg = error.toString()
              break
            default:
              msg = this.translate.instant('LOGIN.LOGIN_FAILED')
              break
          }
          const alertObj = await this.alertController.create({
            header: this.translate.instant('MESSAGE.TIPS'),
            message: msg,
            buttons: [{
              text: this.translate.instant('BUTTON.CONFIRM'),
            }],
          })
          await alertObj.present()
        })
        .finally(() => {
          loading.dismiss()
        })
    }
  }

  saveLastUserName(name) {
    this.nativeStorage.setItem('lastLoginName', name)
  }

  async getLastUserName() {
    try {
      return await this.nativeStorage.getItem('lastLoginName')
    } catch (e) {
      return ''
    }
  }


  handleNextInput() {
    this.pwdInput.setFocus()
  }


  initializeBackButtonCustomHandler(): void {
    // @ts-ignore
    // this.subscription = this.platform.backButton.subscribeWithPriority(9999, () => {
    // })

    // 兩次後退 退出APP
    this.subscription = this.platform.backButton.subscribeWithPriority(9999, () => {
      if (this.pageType === 'switch') {
        this.onBack()
      } else if (this.backButtonPressed) {
        // @ts-ignore
        navigator.app.exitApp()
      } else {
        this.handleExitSuccess()
        // 标记为true
        this.backButtonPressed = true
        // 两秒后标记为false，如果退出的话，就不会执行了
        setTimeout(() => this.backButtonPressed = false, 2000)
      }
    })

  }

  ionViewDidEnter() {
    this.shouldHeight = document.body.clientHeight - 56 + 'px'
  }
  ionViewWillEnter() {
    this.platform.ready()
      .then(() => {
        this.initData()
        this.initializeBackButtonCustomHandler()
      })
  }

  ionViewWillLeave() {
    if (this.subscription) {
      this.subscription.unsubscribe()
    }
  }

  async handleExitSuccess() {
    const toast = await this.toastController.create({
      message: this.translate.instant('MESSAGE.BACK_EXIT_TIPS'),
      duration: 2000,
      // position: 'middle'
      position: 'bottom',
    })
    toast.present()
  }

  initData() {
    this.getQueryParams()
    this.loginForm.password = ''
    this.getLastUserName()
      .then(name => {
        this.loginForm.username = name
      })
      .then(() => this.nativeStorage.getItem('username'))
      .then(username => this.loginForm.username = username)
      .then(() => this.nativeStorage.getItem('password'))
      .then(password => this.loginForm.password = password)
      .then(async () => {
        if (this.pageType === 'login') {
          if (this.loginForm.username && this.loginForm.password) {
            this.checkCanSubmit()
            return this.handleLogin()
          }
        } else if (this.pageType === 'switch') {

          this.userCount = await this.userListService.getCount()
          if (this.userCount > 1) {
            this.onList(undefined)
          }
        }
      })
      .catch(err => {
      })
  }
  handForgetPassword() {
    this.utils.showToast({ msg: this.translate.instant('LOGIN.CONTACT_ADMIN') })
  }
  onRollCall() {
    console.log('活動點名')
    this.nav.setDirection('forward');
    this.router.navigate(['offline-roll-call-list'])
  }

  async onList(e) {
    // const ev = {
    //   target : {
    //     getBoundingClientRect : () => {
    //       return {
    //         top: -50
    //       };
    //     }
    //   }
    // };
    const ev = {
      target : document.querySelector('.login-page .form-item.username')
    };


    const select = new EventEmitter();
    select.subscribe(async (res) => {
      this.popoverController.dismiss()
      console.log(res)
      this.popover = false
      this.loginForm.username = res.username
      this.loginForm.password = res.password
    });

    const close = new EventEmitter();
    close.subscribe(async () => {
      this.popover = false
      console.log('closeclose')
    });


    const popover = await this.popoverController.create({
      component: UserListPopoverComponent,
      // @ts-ignore
      event: ev,
      translucent: true,
      cssClass: 'user-list-popover',
      componentProps: {
        select,
        close
      }
    });
    this.popover = true
    await popover.present();
    await popover.onDidDismiss()
    this.popover = false
  }
  get popoverIcon() {
    return this.popover ? 'arrow-up' : 'arrow-down'
  }

}
