<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1756276" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">search-icon</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">ios-close-circle-out</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">ios-add-circle-outli</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">ios-star</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">ios-star-outline</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80a;</span>
                <div class="name">md-arrow-dropdown</div>
                <div class="code-name">&amp;#xe80a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80b;</span>
                <div class="name">md-arrow-dropup</div>
                <div class="code-name">&amp;#xe80b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d8;</span>
                <div class="name">ios-close-circle-out</div>
                <div class="code-name">&amp;#xe7d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d9;</span>
                <div class="name">ios-close-circle</div>
                <div class="code-name">&amp;#xe7d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe815;</span>
                <div class="name">md-close-circle-outl</div>
                <div class="code-name">&amp;#xe815;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe816;</span>
                <div class="name">md-close-circle</div>
                <div class="code-name">&amp;#xe816;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cc;</span>
                <div class="name">ios-arrow-back</div>
                <div class="code-name">&amp;#xe7cc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cd;</span>
                <div class="name">ios-arrow-down</div>
                <div class="code-name">&amp;#xe7cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d0;</span>
                <div class="name">ios-arrow-up</div>
                <div class="code-name">&amp;#xe7d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe807;</span>
                <div class="name">md-arrow-down</div>
                <div class="code-name">&amp;#xe807;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe808;</span>
                <div class="name">md-arrow-back</div>
                <div class="code-name">&amp;#xe808;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe809;</span>
                <div class="name">md-arrow-forward</div>
                <div class="code-name">&amp;#xe809;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80e;</span>
                <div class="name">md-arrow-up</div>
                <div class="code-name">&amp;#xe80e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ca;</span>
                <div class="name">ios-add</div>
                <div class="code-name">&amp;#xe7ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ce;</span>
                <div class="name">ios-arrow-forward</div>
                <div class="code-name">&amp;#xe7ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d7;</span>
                <div class="name">ios-close</div>
                <div class="code-name">&amp;#xe7d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7db;</span>
                <div class="name">ios-contacts</div>
                <div class="code-name">&amp;#xe7db;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7dd;</span>
                <div class="name">ios-document</div>
                <div class="code-name">&amp;#xe7dd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ed;</span>
                <div class="name">ios-more</div>
                <div class="code-name">&amp;#xe7ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f5;</span>
                <div class="name">ios-remove</div>
                <div class="code-name">&amp;#xe7f5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe805;</span>
                <div class="name">md-add</div>
                <div class="code-name">&amp;#xe805;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe814;</span>
                <div class="name">md-close</div>
                <div class="code-name">&amp;#xe814;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe818;</span>
                <div class="name">md-document</div>
                <div class="code-name">&amp;#xe818;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe819;</span>
                <div class="name">md-contacts</div>
                <div class="code-name">&amp;#xe819;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe824;</span>
                <div class="name">md-more</div>
                <div class="code-name">&amp;#xe824;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">女</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f8;</span>
                <div class="name">男</div>
                <div class="code-name">&amp;#xe6f8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62e;</span>
                <div class="name">PDF</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">通知公告</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">活动</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe89e;</span>
                <div class="name"> 日历</div>
                <div class="code-name">&amp;#xe89e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">chat</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">LC_icon_edit_line_1</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe736;</span>
                <div class="name">chevron-down</div>
                <div class="code-name">&amp;#xe736;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe737;</span>
                <div class="name">chevron-left</div>
                <div class="code-name">&amp;#xe737;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe738;</span>
                <div class="name">chevron-right</div>
                <div class="code-name">&amp;#xe738;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe739;</span>
                <div class="name">chevron-up</div>
                <div class="code-name">&amp;#xe739;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70d;</span>
                <div class="name">calendar</div>
                <div class="code-name">&amp;#xe70d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe621;</span>
                <div class="name">我的</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe793;</span>
                <div class="name">文件</div>
                <div class="code-name">&amp;#xe793;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">excel</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63f;</span>
                <div class="name">png</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe640;</span>
                <div class="name">pdf</div>
                <div class="code-name">&amp;#xe640;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">ppt</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">word</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">jpg</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1647662417355') format('woff2'),
       url('iconfont.woff?t=1647662417355') format('woff'),
       url('iconfont.ttf?t=1647662417355') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-sousuo"></span>
            <div class="name">
              search-icon
            </div>
            <div class="code-name">.icon-sousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-close-circle-out1"></span>
            <div class="name">
              ios-close-circle-out
            </div>
            <div class="code-name">.icon-ios-close-circle-out1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-add-circle-outli"></span>
            <div class="name">
              ios-add-circle-outli
            </div>
            <div class="code-name">.icon-ios-add-circle-outli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-star"></span>
            <div class="name">
              ios-star
            </div>
            <div class="code-name">.icon-ios-star
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-star-outline"></span>
            <div class="name">
              ios-star-outline
            </div>
            <div class="code-name">.icon-ios-star-outline
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-arrow-dropdown"></span>
            <div class="name">
              md-arrow-dropdown
            </div>
            <div class="code-name">.icon-md-arrow-dropdown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-arrow-dropup"></span>
            <div class="name">
              md-arrow-dropup
            </div>
            <div class="code-name">.icon-md-arrow-dropup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-close-circle-out"></span>
            <div class="name">
              ios-close-circle-out
            </div>
            <div class="code-name">.icon-ios-close-circle-out
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-close-circle"></span>
            <div class="name">
              ios-close-circle
            </div>
            <div class="code-name">.icon-ios-close-circle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-close-circle-outl"></span>
            <div class="name">
              md-close-circle-outl
            </div>
            <div class="code-name">.icon-md-close-circle-outl
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-close-circle"></span>
            <div class="name">
              md-close-circle
            </div>
            <div class="code-name">.icon-md-close-circle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-arrow-back"></span>
            <div class="name">
              ios-arrow-back
            </div>
            <div class="code-name">.icon-ios-arrow-back
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-arrow-down"></span>
            <div class="name">
              ios-arrow-down
            </div>
            <div class="code-name">.icon-ios-arrow-down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-arrow-up"></span>
            <div class="name">
              ios-arrow-up
            </div>
            <div class="code-name">.icon-ios-arrow-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-arrow-down"></span>
            <div class="name">
              md-arrow-down
            </div>
            <div class="code-name">.icon-md-arrow-down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-arrow-back"></span>
            <div class="name">
              md-arrow-back
            </div>
            <div class="code-name">.icon-md-arrow-back
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-arrow-forward"></span>
            <div class="name">
              md-arrow-forward
            </div>
            <div class="code-name">.icon-md-arrow-forward
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-arrow-up"></span>
            <div class="name">
              md-arrow-up
            </div>
            <div class="code-name">.icon-md-arrow-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-add"></span>
            <div class="name">
              ios-add
            </div>
            <div class="code-name">.icon-ios-add
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-arrow-forward"></span>
            <div class="name">
              ios-arrow-forward
            </div>
            <div class="code-name">.icon-ios-arrow-forward
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-close"></span>
            <div class="name">
              ios-close
            </div>
            <div class="code-name">.icon-ios-close
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-contacts"></span>
            <div class="name">
              ios-contacts
            </div>
            <div class="code-name">.icon-ios-contacts
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-document"></span>
            <div class="name">
              ios-document
            </div>
            <div class="code-name">.icon-ios-document
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-more"></span>
            <div class="name">
              ios-more
            </div>
            <div class="code-name">.icon-ios-more
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ios-remove"></span>
            <div class="name">
              ios-remove
            </div>
            <div class="code-name">.icon-ios-remove
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-add"></span>
            <div class="name">
              md-add
            </div>
            <div class="code-name">.icon-md-add
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-close"></span>
            <div class="name">
              md-close
            </div>
            <div class="code-name">.icon-md-close
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-document"></span>
            <div class="name">
              md-document
            </div>
            <div class="code-name">.icon-md-document
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-contacts"></span>
            <div class="name">
              md-contacts
            </div>
            <div class="code-name">.icon-md-contacts
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-md-more"></span>
            <div class="name">
              md-more
            </div>
            <div class="code-name">.icon-md-more
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nv"></span>
            <div class="name">
              女
            </div>
            <div class="code-name">.icon-nv
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nan"></span>
            <div class="name">
              男
            </div>
            <div class="code-name">.icon-nan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-PDF"></span>
            <div class="name">
              PDF
            </div>
            <div class="code-name">.icon-PDF
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-notice"></span>
            <div class="name">
              通知公告
            </div>
            <div class="code-name">.icon-notice
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-activity"></span>
            <div class="name">
              活动
            </div>
            <div class="code-name">.icon-activity
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rili"></span>
            <div class="name">
               日历
            </div>
            <div class="code-name">.icon-rili
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-search"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.icon-search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chat"></span>
            <div class="name">
              chat
            </div>
            <div class="code-name">.icon-chat
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-edit"></span>
            <div class="name">
              LC_icon_edit_line_1
            </div>
            <div class="code-name">.icon-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chevrondown"></span>
            <div class="name">
              chevron-down
            </div>
            <div class="code-name">.icon-chevrondown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chevronleft"></span>
            <div class="name">
              chevron-left
            </div>
            <div class="code-name">.icon-chevronleft
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chevronright"></span>
            <div class="name">
              chevron-right
            </div>
            <div class="code-name">.icon-chevronright
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chevronup"></span>
            <div class="name">
              chevron-up
            </div>
            <div class="code-name">.icon-chevronup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-date"></span>
            <div class="name">
              calendar
            </div>
            <div class="code-name">.icon-date
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-me"></span>
            <div class="name">
              我的
            </div>
            <div class="code-name">.icon-me
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file"></span>
            <div class="name">
              文件
            </div>
            <div class="code-name">.icon-file
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-excel"></span>
            <div class="name">
              excel
            </div>
            <div class="code-name">.icon-excel
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-png"></span>
            <div class="name">
              png
            </div>
            <div class="code-name">.icon-png
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pdf"></span>
            <div class="name">
              pdf
            </div>
            <div class="code-name">.icon-pdf
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ppt"></span>
            <div class="name">
              ppt
            </div>
            <div class="code-name">.icon-ppt
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-word"></span>
            <div class="name">
              word
            </div>
            <div class="code-name">.icon-word
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jpg"></span>
            <div class="name">
              jpg
            </div>
            <div class="code-name">.icon-jpg
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sousuo"></use>
                </svg>
                <div class="name">search-icon</div>
                <div class="code-name">#icon-sousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-close-circle-out1"></use>
                </svg>
                <div class="name">ios-close-circle-out</div>
                <div class="code-name">#icon-ios-close-circle-out1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-add-circle-outli"></use>
                </svg>
                <div class="name">ios-add-circle-outli</div>
                <div class="code-name">#icon-ios-add-circle-outli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-star"></use>
                </svg>
                <div class="name">ios-star</div>
                <div class="code-name">#icon-ios-star</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-star-outline"></use>
                </svg>
                <div class="name">ios-star-outline</div>
                <div class="code-name">#icon-ios-star-outline</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-arrow-dropdown"></use>
                </svg>
                <div class="name">md-arrow-dropdown</div>
                <div class="code-name">#icon-md-arrow-dropdown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-arrow-dropup"></use>
                </svg>
                <div class="name">md-arrow-dropup</div>
                <div class="code-name">#icon-md-arrow-dropup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-close-circle-out"></use>
                </svg>
                <div class="name">ios-close-circle-out</div>
                <div class="code-name">#icon-ios-close-circle-out</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-close-circle"></use>
                </svg>
                <div class="name">ios-close-circle</div>
                <div class="code-name">#icon-ios-close-circle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-close-circle-outl"></use>
                </svg>
                <div class="name">md-close-circle-outl</div>
                <div class="code-name">#icon-md-close-circle-outl</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-close-circle"></use>
                </svg>
                <div class="name">md-close-circle</div>
                <div class="code-name">#icon-md-close-circle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-arrow-back"></use>
                </svg>
                <div class="name">ios-arrow-back</div>
                <div class="code-name">#icon-ios-arrow-back</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-arrow-down"></use>
                </svg>
                <div class="name">ios-arrow-down</div>
                <div class="code-name">#icon-ios-arrow-down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-arrow-up"></use>
                </svg>
                <div class="name">ios-arrow-up</div>
                <div class="code-name">#icon-ios-arrow-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-arrow-down"></use>
                </svg>
                <div class="name">md-arrow-down</div>
                <div class="code-name">#icon-md-arrow-down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-arrow-back"></use>
                </svg>
                <div class="name">md-arrow-back</div>
                <div class="code-name">#icon-md-arrow-back</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-arrow-forward"></use>
                </svg>
                <div class="name">md-arrow-forward</div>
                <div class="code-name">#icon-md-arrow-forward</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-arrow-up"></use>
                </svg>
                <div class="name">md-arrow-up</div>
                <div class="code-name">#icon-md-arrow-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-add"></use>
                </svg>
                <div class="name">ios-add</div>
                <div class="code-name">#icon-ios-add</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-arrow-forward"></use>
                </svg>
                <div class="name">ios-arrow-forward</div>
                <div class="code-name">#icon-ios-arrow-forward</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-close"></use>
                </svg>
                <div class="name">ios-close</div>
                <div class="code-name">#icon-ios-close</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-contacts"></use>
                </svg>
                <div class="name">ios-contacts</div>
                <div class="code-name">#icon-ios-contacts</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-document"></use>
                </svg>
                <div class="name">ios-document</div>
                <div class="code-name">#icon-ios-document</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-more"></use>
                </svg>
                <div class="name">ios-more</div>
                <div class="code-name">#icon-ios-more</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ios-remove"></use>
                </svg>
                <div class="name">ios-remove</div>
                <div class="code-name">#icon-ios-remove</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-add"></use>
                </svg>
                <div class="name">md-add</div>
                <div class="code-name">#icon-md-add</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-close"></use>
                </svg>
                <div class="name">md-close</div>
                <div class="code-name">#icon-md-close</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-document"></use>
                </svg>
                <div class="name">md-document</div>
                <div class="code-name">#icon-md-document</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-contacts"></use>
                </svg>
                <div class="name">md-contacts</div>
                <div class="code-name">#icon-md-contacts</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-md-more"></use>
                </svg>
                <div class="name">md-more</div>
                <div class="code-name">#icon-md-more</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nv"></use>
                </svg>
                <div class="name">女</div>
                <div class="code-name">#icon-nv</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nan"></use>
                </svg>
                <div class="name">男</div>
                <div class="code-name">#icon-nan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-PDF"></use>
                </svg>
                <div class="name">PDF</div>
                <div class="code-name">#icon-PDF</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-notice"></use>
                </svg>
                <div class="name">通知公告</div>
                <div class="code-name">#icon-notice</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-activity"></use>
                </svg>
                <div class="name">活动</div>
                <div class="code-name">#icon-activity</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rili"></use>
                </svg>
                <div class="name"> 日历</div>
                <div class="code-name">#icon-rili</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-search"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#icon-search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chat"></use>
                </svg>
                <div class="name">chat</div>
                <div class="code-name">#icon-chat</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-edit"></use>
                </svg>
                <div class="name">LC_icon_edit_line_1</div>
                <div class="code-name">#icon-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chevrondown"></use>
                </svg>
                <div class="name">chevron-down</div>
                <div class="code-name">#icon-chevrondown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chevronleft"></use>
                </svg>
                <div class="name">chevron-left</div>
                <div class="code-name">#icon-chevronleft</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chevronright"></use>
                </svg>
                <div class="name">chevron-right</div>
                <div class="code-name">#icon-chevronright</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chevronup"></use>
                </svg>
                <div class="name">chevron-up</div>
                <div class="code-name">#icon-chevronup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-date"></use>
                </svg>
                <div class="name">calendar</div>
                <div class="code-name">#icon-date</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-me"></use>
                </svg>
                <div class="name">我的</div>
                <div class="code-name">#icon-me</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file"></use>
                </svg>
                <div class="name">文件</div>
                <div class="code-name">#icon-file</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-excel"></use>
                </svg>
                <div class="name">excel</div>
                <div class="code-name">#icon-excel</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-png"></use>
                </svg>
                <div class="name">png</div>
                <div class="code-name">#icon-png</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pdf"></use>
                </svg>
                <div class="name">pdf</div>
                <div class="code-name">#icon-pdf</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ppt"></use>
                </svg>
                <div class="name">ppt</div>
                <div class="code-name">#icon-ppt</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-word"></use>
                </svg>
                <div class="name">word</div>
                <div class="code-name">#icon-word</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jpg"></use>
                </svg>
                <div class="name">jpg</div>
                <div class="code-name">#icon-jpg</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
