import { Component, OnInit } from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {StorageService} from '@services/storage/storage.service';
// import { PhotoViewer } from '@ionic-native/photo-viewer/ngx';
import { PreviewAnyFile  } from '@ionic-native/preview-any-file/ngx';
import {TextViewService} from '@services/utils/text-view.service';

@Component({
  selector: 'app-event-attaches',
  templateUrl: './event-attaches.page.html',
  styleUrls: ['./event-attaches.page.scss'],
})
export class EventAttachesPage implements OnInit {

  public isInit = false
  public data: any = []
  public storageUrl = ''
  private options = {
    share: true, // default is false
    closeButton: false, // default is true
    copyToReference: true, // default is false
    headers: '',  // If this is not provided, an exception will be triggered
    piccasoOptions: { } // If this is not provided, an exception will be triggered
  };
  constructor(
    private route: ActivatedRoute,
    private storageService: StorageService,
    // private photoViewer: PhotoViewer,
    private previewAnyFile: PreviewAnyFile,
    ) { }

    private fileClass = {
      docx: 'word',
      doc: 'word',
      xlsx: 'excel',
      xls: 'excel',
      ppt: 'ppt',
      pdf: 'pdf',
      jpg: 'jpg',
      png: 'png',
      file: 'file',
    }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      if (!this.isInit && params.data) {
        this.isInit = true
        const data = JSON.parse(params.data)
        data.forEach(item => {
          item._show = false
        })
        this.data = data
      }
    });
    this.storageUrl = this.storageService.serverSetting.storageUrl
  }

  getFileIconClass(type) {
    const className = this.fileClass[type]
    if (className) {
      return 'iconfont icon-' + className
    }
    return 'iconfont icon-file'
  }

  openFile(item) {
    let url = ''
    let name = '預覽'
    if (item && item.path) {
      url = this.storageUrl + item.path
    }
    if (item && item.name) {
      name = item.name
    }
    // url = 'http://image.woshipm.com/wp-files/img/58.jpg'
    // url = 'http://guangdong.chinatax.gov.cn/gdsw/etaxxzzq/2019-05/13/39414a73adf1475fb8801adb83895a68/files/d8366cf8b55549949ec52c812d5926df.doc'
    if (url) {
      // this.photoViewer.show(url, name, this.options);
      this.previewAnyFile.preview(url)
        .then((res: any) => console.log(res))
        .catch((error: any) => console.error(error));
    }
  }

}
