<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{ edit ? '已選學生' : '學生列表' }}</ion-title>
    <ion-buttons *ngIf="edit" slot="end">
      <ion-button (click)="onAdd()">
        添加
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <!--<ion-list>-->
  <!--  <ion-item lines="full" detail *ngFor="let item of studentList">-->
  <!--    -->
  <!--  </ion-item>-->
  <!--</ion-list>-->

  <div class="student-list">
    <div class="class-box" *ngFor="let item of studentList; let ci = index">
      <div class="class-name">{{ item.className }}</div>
      <div class="stu-box">
        <div class="stu-item" *ngFor="let stu of item.students; let si = index">
          {{ stu.stuUname}}

          <!--<ion-icon icon="close"></ion-icon>-->

          <ion-icon name="close-circle" *ngIf="edit" (click)="onDeleteStu(item, ci, stu, si)"></ion-icon>
        </div>
        <!--<ion-chip class="stu-item" *ngFor="let stu of item.students">-->
        <!--  <ion-label>{{ stu.stuUname }}</ion-label>-->
        <!--  <ion-icon name="close-circle"></ion-icon>-->
        <!--</ion-chip>-->
      </div>
    </div>
  </div>
  <div class="empty-tips" *ngIf="studentList.length === 0">
    {{ edit ? '請點擊右上角添加學生' : '無學生信息' }}
  </div>
</ion-content>
