import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StudentStarActivitySearchPage } from './student-star-activity-search.page';

describe('StudentStarActivitySearchPage', () => {
  let component: StudentStarActivitySearchPage;
  let fixture: ComponentFixture<StudentStarActivitySearchPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ StudentStarActivitySearchPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StudentStarActivitySearchPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
