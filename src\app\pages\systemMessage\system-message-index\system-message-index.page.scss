ion-content {
  --background: #FFFFFF;
  ion-list {
    background: #FFFFFF;
    ion-item:not(.header) {
      //ion-row {
      //  width: 100%;
      //  .title {
      //    font-size: 14px;
      //    padding-right: 5px;
      //    font-weight: 500;
      //  }
      //  .datetime {
      //    font-size: 10px;
      //    width: 100px;
      //    font-weight: normal;
      //  }
        .content {
          white-space: normal;
          font-size: 12px;
          margin-top: 0;
        }
      //}
      .msg-item {
        width: 100%;
      }

      div.title-row {
        //padding-bottom: 5px;

        width: 100%;
        position: relative;
        display: block;
        div.title{
          display: inline-block;
          width: calc(100% - 120px);
          h2.title{
            font-size: 14px;
            padding-right: 5px;
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-bottom: 0;
          }
        }
        div.datetime {
          width: 120px;
          display: inline-block;
          vertical-align: top;
          h2.datetime{
            font-size: 11px;
            width: 120px;
            font-weight: normal;
            color: #B0B0B0;
          }
        }
      }
    }

  }
}

ion-item.header {
  --min-height: 33px;
  height: 33px;
  border-bottom: 1px solid #EFEFEF;
  border-top: 1px solid #EFEFEF;
  --background: #F9F9F9;

  .title {
    font-size: 12px;
    margin: 0;
    padding-left: 10px;
  }
  .datetime {
    width: 100px;
    font-size: 12px;
    margin-right: 10px;
  }
  .icon-area {
    width: 20px;
    height: 20px;
    min-width: 20px;
    min-height: 20px;
    margin: 0;
  }
}


i.dot {
  width: 10px;
  &:after {
    content: "";
    width: 5px;
    height: 5px;
    min-width: 5px;
    min-height: 5px;
    display: inline-block;
    vertical-align: middle;
    border-radius: 50%;

  }
  &.read:after {
    //background: #4CC726;
  }
  &.warning:after {
    background: #FF9900;
  }
  &.unread:after {
    background: #F56C6C;
    background: #E02020;
  }
}
