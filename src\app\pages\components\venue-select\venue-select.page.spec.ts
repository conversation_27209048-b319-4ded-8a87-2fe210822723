import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { VenueSelectPage } from './venue-select.page';

describe('VenueSelectPage', () => {
  let component: VenueSelectPage;
  let fixture: ComponentFixture<VenueSelectPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ VenueSelectPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VenueSelectPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
