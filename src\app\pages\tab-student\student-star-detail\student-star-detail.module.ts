import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {Routes, RouterModule} from '@angular/router';

import {IonicModule} from '@ionic/angular';

import {TranslateModule} from '@ngx-translate/core';
import {StudentStarDetailPage} from './student-star-detail.page';

const routes: Routes = [
    {
        path: '',
        component: StudentStarDetailPage
    }
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        TranslateModule,
        RouterModule.forChild(routes)
    ],
    // declarations: [StudentStarDetailPage]
})
export class StudentStarDetailPageModule {
}
