import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { RollCallOnlinePage } from './roll-call-online.page';

const routes: Routes = [
  {
    path: '',
    component: RollCallOnlinePage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  // declarations: [RollCallOnlinePage]
})
export class RollCallOnlinePageModule {}
