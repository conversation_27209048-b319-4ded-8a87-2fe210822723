import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {Routes, RouterModule} from '@angular/router';

import {IonicModule} from '@ionic/angular';

import {TranslateModule} from '@ngx-translate/core';
import {StudentStarPage} from './student-star.page';
import {StudentStarDetailPage} from '@pages/tab-student/student-star-detail/student-star-detail.page';

const routes: Routes = [
    {
        path: '',
        component: StudentStarPage
    }
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        TranslateModule,
        RouterModule.forChild(routes)
    ],
    declarations: [StudentStarPage, StudentStarDetailPage]
})
export class StudentStarPageModule {
}
