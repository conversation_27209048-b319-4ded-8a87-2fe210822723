import { Injectable } from '@angular/core';
import {ModalController} from '@ionic/angular';
import {rightEnterAnimation, rightLeaveAnimation} from '@app/animations/rightModal'
import { TextViewPage } from '@pages/events/text-view/text-view.page';

@Injectable({
  providedIn: 'root'
})
export class TextViewService {

  constructor(
    private modalCtrl: ModalController
  ) { }


  async showTextView(title, content, canEdit = false) {
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: TextViewPage,
      componentProps: {
        id: 'text-view-modal',
        title,
        content,
        canEdit
      },
      id: 'text-view-modal'
    })
    await modal.present()
    const res = await modal.onDidDismiss()
    if (res.data.save) {
      return res.data && res.data.content || ''
    }
    return content
  }
}
