<ion-header class="center">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="onChangeShowType()" class="select-date-type">
        {{ showTypeStr }}
      </ion-button>
    </ion-buttons>
    <ion-title>主頁</ion-title>
    <!--<ion-buttons slot="end">-->
    <!--  <ion-button [hidden]="false" (click)="onReadAll()">-->
    <!--    離線點名-->
    <!--  </ion-button>-->
    <!--</ion-buttons>-->
    <!--<ion-buttons slot="end">-->
    <!--  <ion-button [hidden]="false" (click)="onExit()">-->
    <!--    退出-->
    <!--  </ion-button>-->
    <!--</ion-buttons>-->
    <ion-buttons slot="end">
      <ion-button (click)="onMenu($event)">
        <ion-icon slot="icon-only" name="ellipsis-horizontal-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-calendar [(ngModel)]="filter.date_select"
              (change)="onChange($event)"
              (monthChange)="onMonthChange($event)"
              (select)="onSelect($event)"
              [options]="dateOptions"
              [type]="'string'"
              [format]="'YYYY/MM/DD'"
              class="calendar"
>
</ion-calendar>
<ion-content>
  <div class="content-box">
    <div
        *ngFor="let item of showData; let i = index"
        class="date-box">
      <div class="date">
        {{ item.date }}
      </div>
      <div class="row" *ngFor="let row of item.events; let j = index">
        <div class="divide" [hidden]="j === 0 || !row.showDivide"><div class="dot"></div></div>
        <div class="time" [className]="!row.showTime ? 'time hide-time' : 'time'">{{ row.time }}</div>
        <div class="name ion-activatable"  (click)="onClickItem(row)">
          {{ row.name_cn }}
          <ion-ripple-effect type="bounded"></ion-ripple-effect>
        </div>
      </div>

    </div>
  </div>
  <div class="empty-tips" *ngIf="showData.length === 0">
    暫無數據
  </div>
</ion-content>
