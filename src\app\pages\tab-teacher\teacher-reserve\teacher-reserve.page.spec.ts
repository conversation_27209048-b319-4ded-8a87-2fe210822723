import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TeacherReservePage } from './teacher-reserve.page';

describe('TeacherReservePage', () => {
  let component: TeacherReservePage;
  let fixture: ComponentFixture<TeacherReservePage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TeacherReservePage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TeacherReservePage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
