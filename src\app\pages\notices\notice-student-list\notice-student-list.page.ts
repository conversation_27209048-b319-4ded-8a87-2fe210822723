import { Component, Input, OnInit } from '@angular/core';
import {ActionSheetController, AlertController, ModalController, Platform} from '@ionic/angular';
import {UtilsService} from '@app/utils/utils.service';
import {NoticesService} from '@services/api/notices.service';
import {LoadingService} from '@app/utils/loading.service';
import {ActivitySelectPage} from '@pages/activity/activity-select/activity-select.page';
import {MasterService} from '@services/api/master.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {ActivitySelectClassPage} from '@pages/components/activity-select-class/activity-select-class.page';
import {debounce} from 'rxjs/operators';
import {branch} from '@angular-devkit/schematics/src/tree/static';
import {ActivityService} from '@services/api/activity.service';

@Component({
  selector: 'app-notice-student-list',
  templateUrl: './notice-student-list.page.html',
  styleUrls: ['./notice-student-list.page.scss'],
})
export class NoticeStudentListPage implements OnInit {

  @Input() students: any = []
  @Input() edit = true
  public studentList: any = []
  constructor(
    private platform: Platform,
    private modalCtrl: ModalController,
    private utils: UtilsService,
    private notices: NoticesService,
    private master: MasterService,
    private activity: ActivityService,


    public actionSheetController: ActionSheetController,
    private loadingService: LoadingService,
    private alertController: AlertController,
    private nativeStorage: NativeStorage,
    ) { }

  ngOnInit() {
    this.studentList = []
    if (this.students && Array.isArray(this.students)) {
      this.studentList = this.students
    } else {
      this.studentList = []
    }
    if (this.studentList.length === 0) {
      this.onAdd()
    }
    // const t = new Date().getTime()
    this.platform.backButton.subscribe(() => {
      this.onClose()
      // console.log(t)
      // this.platform.backButton.unsubscribe()
      return false
    });
  }
  ngOnDestroy() {
    this.platform.backButton.unsubscribe()
  }

  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true,
      students: this.studentList
    });
  }


  async onAdd() {

    let user
    try {
      user = await this.nativeStorage.getItem('userInfo')
      if (!user || !user.user_id || !user.year_id) {
        throw new Error('err')
      }
    } catch (e) {
      this.utils.showMsg('登錄失效，請重新登錄')
      return
    }
    const yearId = user.year_id
    const type = await this.selectType()
    if (type === 'CLASS') {

      await this.loadingService.start()
      const res = await this.master.getStudentAll({ yearId })
      const studentList = this.formatStuList(res)

      await this.loadingService.end()
      const classInfo = await this.selectClass(studentList)

      this.addStudents(studentList)
      this.sortStudentList()

    } else if (type === 'ACTIVITY') {
      // 活動選擇
      const activity = await this.selectActivity()
      if (activity && activity.id) {
        // const res = await this.master.getStudentAll({ yearId })
        await this.loadingService.start()
        const res: any = await this.activity.getActivityLottery(activity.id)
        await this.loadingService.end()
        console.log(res)
        const studentList = this.formatStuList(res.stuParticipate)
        const classInfo = await this.selectClass(studentList)
        this.addStudents(studentList)
        this.sortStudentList()
      }

    }
  }
  formatStuList(data) {
    const arr = []
    for (const item in data) {
      /* 本次選擇需要的數據*/
      const g = item.split('_')
      const className = g[0]
      /* 緩存數據(已經選擇的) */
      const oldClass =  this.studentList.find(i => i.className === className)


      arr.push({
        className,
        classKey: item,
        students: data[item].map(i => {
          /* 已選擇的添加選中狀態 */
          let checked = false
          if (oldClass) {
            const oldStu = oldClass.students.find(s => s.id === i.id)
            if (oldStu) {
              checked = oldStu.checked
            }
          }
          return Object.assign({ checked }, i)
        })
      })
    }
    return arr
  }

  clearStudent() {
    this.studentList = []
  }
  addStudents(data) {
    data.forEach(item => {
      // 提取選擇的學生
      const selectedStu = item.students.filter(i => i.checked)
      if (selectedStu.length === 0) {
        // 無選擇學生的跳過
        return
      }

      // 從緩存查找改班級
      let oldClass =  this.studentList.find(i => i.className === item.className)
      if (!oldClass) {
        // 緩存無，則添加空學生班級
        this.studentList.push({
          className: item.className,
          classKey: item.classKey,
          students: [],
        })
        oldClass =  this.studentList.find(i => i.className === item.className)
      }
      // 合併學生
      selectedStu.forEach(stu => {
        const oldStu = oldClass.students.find(os => os.id === stu.id)
        if (oldStu) {
          // 原先已選擇
        } else {
          // 添加學生到當前班級
          oldClass.students.push(stu)
        }
      })

      oldClass.students = selectedStu
    })

    this.sortStudentList()
  }

  /* 班級學生排序 */
  sortStudentList() {
    this.studentList.forEach(item => {
      item.students.sort((a, b) => {
        return a.stuNo.localeCompare(b.stuNo, 'zh');
      })
    })
    this.studentList.sort((a, b) => {
      return a.className.localeCompare(b.className, 'zh');
    })
  }


  async selectType() {
    let type = ''
    const actionSheet = await this.actionSheetController.create({
      header: '選擇學生方式',
      buttons: [{
        text: '從班級學生',
        handler: () => {
          type = 'CLASS'
        }
      }, {
        text: '從參加活動的學生',
        handler: () => {
          type = 'ACTIVITY'
        }
      }]
    });
    await actionSheet.present();
    await actionSheet.onDidDismiss();
    return type
  }


  /* 選擇活動 */
  async selectActivity() {
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: ActivitySelectPage,
      componentProps: {
        domId: 'activity-select-modal',
      },
      id: 'activity-select-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    if (res.data.save) {
      return res.data.activity
    }
    return {}
  }
  /* 選擇班級 */
  async selectClass(studentList) {
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: ActivitySelectClassPage,
      componentProps: {
        domId: 'activity-select-class-modal',
        list: studentList
      },
      id: 'activity-select-class-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    if (res && res.data && res.data.save) {
      return res.data.students
    }
    return {}
  }

  onDeleteStu(item, ci, stu, si) {
    item.students.splice(si, 1)
    if (item.students.length === 0) {
      this.studentList.splice(ci, 1)
    }
  }

}
