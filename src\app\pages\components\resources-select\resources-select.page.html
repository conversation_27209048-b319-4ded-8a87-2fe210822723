<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>預約資源</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="onSave()">
        保存
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list *ngIf="list.length > 0; else emptyTips">
    <ion-item *ngFor="let item of list">
      <ion-checkbox slot="start" [(ngModel)]="item.check"></ion-checkbox>
      <ion-label>{{ item.name}}({{ item.num }})</ion-label>
      <ion-input slot="end" type="number"
                 [(ngModel)]="item.selectQty"
                 [max]="item.num"
                 [min]="0"
                 [placeholder]="0"
                 class="qty-input"
                 (change)="onChangeNum($event, item)"
      ></ion-input>
    <!--

                 [placeholder]="item.num"
                 -->
    </ion-item>
  </ion-list>
  <ng-template #emptyTips>
    <!--<div class="page-empty-tips"></div>-->
    <div class="empty-tips">
      暫無數據
    </div>
  </ng-template>
</ion-content>
