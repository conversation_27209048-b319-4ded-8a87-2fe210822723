<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button ></ion-back-button>
    </ion-buttons>
    <ion-title>新增訊息</ion-title>
    <ion-buttons slot="end">
      <ion-button [disabled]="!canSubmit" (click)="onSend()">
        發送
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-row class="form-item target">
    <div class="label">接收對象</div>
    <div class="value"><ion-input [value]="selectedNum" readonly></ion-input></div>
    <div class="btn"><ion-button size="mini" color="search" (click)="onSelectStudent()">選擇學生</ion-button></div>
  </ion-row>
  <ion-row class="form-item title">
    <div class="label">訊息標題</div>
    <div class="value"><ion-input [(ngModel)]="title" (ngModelChange)="onChangeTitle()"></ion-input></div>
  </ion-row>
  <ion-row class="form-item content">
    <div class="value">
      <ion-textarea [(ngModel)]="content" [rows]="8" type="textarea" placeholder="訊息詳情" (ngModelChange)="onChangeContent()"></ion-textarea>
    </div>
  </ion-row>
  <ion-row class="form-item files">
    <input #inputFile type="file" accept="image/*" hidden (change)="onChangeFile($event)">
    <!--<div class="file-preview" *ngFor="let item of attachment_array; let i = index">-->
    <!--  <ion-img [src]="getPath(item)"></ion-img>-->
    <!--  <ion-icon icon="remove" (click)="onRemoveFile(item, i)"></ion-icon>-->
    <!--</div>-->
    <div class="file-preview" *ngFor="let item of files; let i = index">
      <ion-img [src]="item.base64"></ion-img>
      <ion-icon icon="remove" (click)="onRemoveFile(item, i)"></ion-icon>
    </div>

    <div class="select-file ion-activatable" (click)="onAddFile()">
      <ion-icon icon="add"></ion-icon>
      <ion-ripple-effect type="bounded"></ion-ripple-effect>
    </div>
  </ion-row>

</ion-content>
