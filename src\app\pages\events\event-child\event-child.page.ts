import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, NavigationExtras} from '@angular/router';
import {NavController} from '@ionic/angular';
import {TextViewService} from '@services/utils/text-view.service';

@Component({
  selector: 'app-event-child',
  templateUrl: './event-child.page.html',
  styleUrls: ['./event-child.page.scss'],
})
export class EventChildPage implements OnInit {
  constructor(
    public nav: NavController,
    private route: ActivatedRoute,
    private textViewService: TextViewService,
  ) {}

  public isInit = false
  public data: any = []

  public statusList = {
    Y: '已完成',
    N: '未完成',
  }
  public statusClassList = {
    Y: 'success',
    N: 'error',
  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      if (!this.isInit && params.data) {
        this.isInit = true
        const data = JSON.parse(params.data)
        data.forEach(item => {
          item._show = false
        })
        this.data = data
      }
    });
  }

  getStatus(v) {
    const text = this.statusList[v]
    return text ? text : '-'
  }
  getStatusClass(v) {
    const text = this.statusClassList[v]
    return text ? text : ''
  }

  zero(v) {
    return (v < 10 ? '0' : '') + v
  }
  formatDate(v) {
    let text = ''
    if (v) {
      const str = v.replace(new RegExp(/-/gm), '/')
      const d = new Date(str)
      if (Object.prototype.toString.call(d) !== '[object Date]' || isNaN(d.getTime())) {
        text = ''
      } else {
        const year = d.getFullYear()
        const month = this.zero(d.getMonth() + 1)
        const day = this.zero(d.getDate())
        text = `${year}-${month}-${day}`
      }
    }
    return text
  }

  onViewName(name) {
    this.showText('內容', name)
  }
  onViewRemark(remark) {
    this.showText('備註', remark)
  }
  async showText(title, content) {
    await this.textViewService.showTextView(title, content)
    // const navigationExtras: NavigationExtras = {
    //   queryParams: {
    //     title,
    //     content,
    //   },
    //   skipLocationChange: true
    // }
    // // @ts-ignore
    // this.nav.navigateForward(['text-view'], navigationExtras);
  }

}
