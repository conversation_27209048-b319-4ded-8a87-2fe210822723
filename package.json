{"name": "eca-student-app", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "14.1.3", "@angular/common": "14.1.3", "@angular/compiler": "14.1.3", "@angular/core": "14.1.3", "@angular/fire": "^7.4.1", "@angular/forms": "14.1.3", "@angular/platform-browser": "14.1.3", "@angular/platform-browser-dynamic": "14.1.3", "@angular/router": "14.1.3", "@awesome-cordova-plugins/android-notch": "^5.45.0", "@awesome-cordova-plugins/core": "^6.8.0", "@awesome-cordova-plugins/local-notifications": "^6.8.0", "@ionic-native/android-permissions": "^5.36.0", "@ionic-native/app-version": "^5.36.0", "@ionic-native/core": "^5.36.0", "@ionic-native/device": "^5.36.0", "@ionic-native/file": "^5.36.0", "@ionic-native/file-opener": "^5.36.0", "@ionic-native/firebase-x": "^5.36.0", "@ionic-native/google-plus": "^5.36.0", "@ionic-native/http": "^5.36.0", "@ionic-native/is-debug": "^5.36.0", "@ionic-native/local-notifications": "^5.36.0", "@ionic-native/native-storage": "^5.36.0", "@ionic-native/network": "^5.36.0", "@ionic-native/photo-viewer": "^5.36.0", "@ionic-native/preview-any-file": "^5.36.0", "@ionic-native/qr-scanner": "^5.36.0", "@ionic-native/secure-storage": "^5.36.0", "@ionic-native/splash-screen": "^5.36.0", "@ionic-native/sqlite": "^5.36.0", "@ionic-native/status-bar": "^5.36.0", "@ionic-native/vibration": "^5.36.0", "@ionic/angular": "^6.2.2", "@ionic/cordova-builders": "^7.0.0", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@types/echarts": "^4.9.16", "await-to-js": "^3.0.0", "axios": "^0.27.2", "chroma-js": "^2.4.2", "cordova-browser": "^6.0.0", "cordova-plugin-advanced-http": "^3.3.1", "cordova-plugin-android-permissions": "^1.1.3", "cordova-plugin-file": "^7.0.0", "cordova-plugin-file-opener2": "^3.0.5", "cordova-plugin-firebasex": "^19.0.0", "cordova-plugin-googleplus": "8.5.2", "cordova-plugin-is-debug": "^1.0.0", "cordova-plugin-nativestorage": "^2.3.2", "cordova-plugin-network-information": "^3.0.0", "cordova-plugin-preview-any-file": "^0.2.9", "cordova-plugin-secure-storage": "^3.0.2", "cordova-plugin-vibration": "^3.1.1", "cordova-plugin-whitelist": "^1.3.5", "cordova-sqlite-ext": "^7.0.0", "core-js": "^3.24.1", "echarts": "^5.3.3", "firebase": "^9.10.0", "hammerjs": "^2.0.8", "ion2-calendar": "^3.5.0", "ionic4-star-rating": "^1.1.1", "js-cookie": "^3.0.1", "md5": "^2.3.0", "moment": "^2.29.4", "node-polyfill-webpack-plugin": "^2.0.1", "plugin": "0.3.3", "rxjs": "^7.5.6", "sharp": "^0.30.7", "stream-browserify": "^3.0.0", "tslib": "^2.4.0", "typings": "^2.1.1", "zone.js": "~0.11.8"}, "devDependencies": {"@angular-devkit/architect": "~0.1401.2", "@angular-devkit/build-angular": "14.1.3", "@angular-devkit/core": "14.1.3", "@angular-devkit/schematics": "14.1.3", "@angular/cli": "14.1.3", "@angular/compiler": "14.1.3", "@angular/compiler-cli": "14.1.3", "@angular/language-service": "14.1.3", "@ionic/angular-toolkit": "~7.0.0", "@types/jasmine": "~4.0.3", "@types/jasminewd2": "^2.0.10", "@types/node": "~18.7.2", "codelyzer": "^6.0.2", "cordova-android": "^14.0.1", "cordova-plugin-android-notch": "^1.0.3", "cordova-plugin-androidx": "^3.0.0", "cordova-plugin-androidx-adapter": "^1.1.3", "cordova-plugin-app-version": "^0.1.14", "cordova-plugin-badge": "^0.8.9", "cordova-plugin-device": "^2.1.0", "cordova-plugin-ionic-keyboard": "^2.2.0", "cordova-plugin-ionic-webview": "^5.0.1", "cordova-plugin-local-notification": "^0.9.0-beta.2", "cordova-plugin-statusbar": "^3.0.0", "cordova-plugin-wkwebview-engine": "^1.2.2", "jasmine-core": "~4.3.0", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.1", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.0.0", "protractor": "^5.4.4", "ts-node": "~10.9.1", "tslint": "~5.20.1", "typescript": "~4.7.4"}, "description": "An Ionic project", "cordova": {"plugins": {"cordova-plugin-statusbar": {}, "cordova-plugin-device": {}, "cordova-plugin-ionic-webview": {}, "cordova-plugin-ionic-keyboard": {}, "cordova-plugin-advanced-http": {"OKHTTP_VERSION": "3.10.0", "ANDROIDBLACKLISTSECURESOCKETPROTOCOLS": "SSLv3,TLSv1"}, "cordova-plugin-secure-storage": {}, "cordova-plugin-nativestorage": {}, "cordova-plugin-is-debug": {}, "cordova-plugin-vibration": {}, "cordova-plugin-preview-any-file": {}, "cordova-plugin-android-permissions": {}, "cordova-sqlite-ext": {}, "cordova-plugin-network-information": {}, "cordova-plugin-whitelist": {}, "cordova-plugin-file": {}, "cordova-plugin-file-opener2": {}, "cordova-plugin-android-notch": {}, "cordova-plugin-wkwebview-engine": {}, "cordova-plugin-googleplus": {"REVERSED_CLIENT_ID": "com.googleusercontent.apps.991336545500-d6l6ci5dgarfllds5j1fseivg9dssh9d", "WEB_APPLICATION_CLIENT_ID": "991336545500-fcsk9ecre7clog8hplu7qarju65vnmg4.apps.googleusercontent.com", "GOOGLE_SIGN_IN_VERSION": "~> 6.2.1"}, "cordova-plugin-local-notification": {}, "cordova-plugin-firebasex": {"FIREBASE_ANALYTICS_COLLECTION_ENABLED": "false", "FIREBASE_PERFORMANCE_COLLECTION_ENABLED": "false", "FIREBASE_CRASHLYTICS_COLLECTION_ENABLED": "false", "FIREBASE_FCM_AUTOINIT_ENABLED": "true", "FIREBASE_ANALYTICS_WITHOUT_ADS": "false", "GOOGLE_ANALYTICS_ADID_COLLECTION_ENABLED": "true", "GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE": "true", "GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE": "true", "GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA": "true", "GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS": "true", "ANDROID_ICON_ACCENT": "#FF00FFFF", "ANDROID_FIREBASE_PERFORMANCE_MONITORING": "false", "ANDROID_PLAY_SERVICES_TAGMANAGER_VERSION": "18.0.4", "ANDROID_PLAY_SERVICES_AUTH_VERSION": "20.7.0", "ANDROID_FIREBASE_ANALYTICS_VERSION": "21.6.2", "ANDROID_FIREBASE_MESSAGING_VERSION": "23.4.1", "ANDROID_FIREBASE_CONFIG_VERSION": "21.6.3", "ANDROID_FIREBASE_PERF_VERSION": "20.5.2", "ANDROID_FIREBASE_AUTH_VERSION": "22.3.1", "ANDROID_FIREBASE_INAPPMESSAGING_VERSION": "20.4.2", "ANDROID_FIREBASE_FIRESTORE_VERSION": "24.11.1", "ANDROID_FIREBASE_FUNCTIONS_VERSION": "20.4.0", "ANDROID_FIREBASE_IID_VERSION": "21.1.0", "ANDROID_FIREBASE_INSTALLATIONS_VERSION": "17.2.0", "ANDROID_FIREBASE_CRASHLYTICS_VERSION": "18.6.4", "ANDROID_FIREBASE_CRASHLYTICS_NDK_VERSION": "18.6.4", "ANDROID_GSON_VERSION": "2.9.0", "ANDROID_FIREBASE_PERF_GRADLE_PLUGIN_VERSION": "1.4.2", "ANDROID_GRPC_OKHTTP": "1.46.0"}, "cordova-plugin-app-version": {}}, "platforms": ["browser", "android"]}, "config": {"ionic_webpack": "./webpack.config.js"}}