<ion-header class="inside-page">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="tab-user/eventIndex"></ion-back-button>
    </ion-buttons>
    <ion-title>修改詳情</ion-title>
    <ion-buttons slot="end">
      <ion-button [hidden]="!canSubmit" (click)="onPass()">
        通過
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content>
  <ion-list>
    <ion-item-group>
      <ion-item lines="full">
        <ion-label position="fixed">項目名稱</ion-label>
        <ion-text>{{ data.project_name }}</ion-text>
      </ion-item>
      <ion-item lines="full">
        <ion-label position="fixed">歸屬系統</ion-label>
        <ion-text>{{ data.system_name }}</ion-text>
      </ion-item>
      <ion-item lines="full" detail button (click)="onNameView()">
        <ion-label position="fixed">修改標題</ion-label>
        <ion-text>{{ data.name }}</ion-text>
      </ion-item>
      <ion-item lines="full" detail button (click)="onContentView()">
        <ion-label position="fixed">修改內容</ion-label>
        <ion-text>{{ data.content }}</ion-text>
      </ion-item>
    </ion-item-group>
    <ion-item-group>
      <ion-item lines="full">
        <ion-label position="fixed">跟進人</ion-label>
        <ion-text>{{ data.perform_user_nickname }}</ion-text>
      </ion-item>
      <ion-item lines="full" detail button (click)="showChild()">
        <ion-label position="fixed">子任務</ion-label>
      </ion-item>
      <ion-item lines="full" detail button (click)="onRemarkView()">
        <ion-label position="fixed">備註</ion-label>
        <ion-text>{{ data.remark }}</ion-text>
      </ion-item>
    </ion-item-group>
    <ion-item-group>
      <ion-item lines="full" [button]="data.free === 1" (click)="showFreeReason = !showFreeReason">
        <ion-label position="fixed">是否收費</ion-label>
        <ion-text>{{ data.free === 1 ? '是' : '否' }}</ion-text>
        <ion-icon [hidden]="showFreeReason" slot="end" name="ios-arrow-forward"></ion-icon>
        <ion-icon [hidden]="!showFreeReason" slot="end" name="ios-arrow-down"></ion-icon>
      </ion-item>
      <!-- [hidden]="!showFreeReason" -->
      <ion-item
          *ngIf="showFreeReason"
          lines="full"
          class="item-accordion"
          >
        <ion-text>{{ data.free_reason }}</ion-text>
      </ion-item>
    </ion-item-group>
    <ion-item-group>
      <ion-item lines="full">
        <ion-label position="fixed">創建人</ion-label>
        <ion-text>{{ data.create_user_nickname }}</ion-text>
      </ion-item>
      <ion-item lines="full">
        <ion-label position="fixed">創建時間</ion-label>
        <ion-text>{{ formatDateTime(data.created_at, 'yyyy-MM-dd HH:mm') }}</ion-text>
      </ion-item>
      <ion-item lines="full">
        <ion-label position="fixed">開發進度</ion-label>
        <ion-text>{{ getDevProcess(data.develop_process) }}</ion-text>
      </ion-item>
      <ion-item lines="full">
        <ion-label position="fixed">緊急程度</ion-label>
        <ion-text>{{ getUrgent(data.urgent) }}</ion-text>
      </ion-item>
      <ion-item *ngIf="showDeadline" lines="full">
        <ion-label position="fixed">截止日期</ion-label>
        <ion-text>{{ formatDateTime(data.deadline) }}</ion-text>
      </ion-item>
      <ion-item lines="full" detail button (click)="showAttaches()">
        <ion-label position="fixed">附件</ion-label>
      </ion-item>
    </ion-item-group>
  </ion-list>

</ion-content>
