<ion-header class="center">
  <ion-toolbar>
    <ion-title>活動報名</ion-title>
  </ion-toolbar>
</ion-header>

<ion-header class="activity-filter">
  <ion-searchbar
      [(ngModel)]="activityFilter.actCodeOrName"
      [debounce]="300"
      name="keyword"
      showCancelButton="never"
      placeholder="輸入活動編號/活動名稱"
      type="search"
      inputmode="search"
      [ngModelOptions]="{standalone: true}"
      (keyup)="onSearchKeyUp($event)"
      (ionBlur)="loadDataActivity()"
  ></ion-searchbar>
  <!-- (ionChange)="loadDataActivity()" -->
  <ion-row class="row" style="margin-bottom: 3px;">
    <ion-label class="input-label">學期</ion-label>
    <ion-select
        [(ngModel)]="activityFilter.actTerm"
        placeholder="學期"
        okText="確認"
        cancelText="取消"
        interface="popover"
        (ngModelChange)="loadDataActivity()">
      <ion-select-option value="">全部</ion-select-option>
      <ion-select-option value="0">全學期</ion-select-option>
      <ion-select-option value="1">上學期</ion-select-option>
      <ion-select-option value="2">下學期</ion-select-option>
      <ion-select-option value="3">暑假期</ion-select-option>
    </ion-select>
    <!--<ion-label class="input-label">狀態</ion-label>-->
    <!--<ion-select-->
    <!--    [(ngModel)]="activityFilter.status"-->
    <!--    placeholder="狀態"-->
    <!--    okText="確認"-->
    <!--    cancelText="取消"-->
    <!--    interface="popover"-->
    <!--    (ngModelChange)="loadDataActivity()">-->
    <!--  <ion-select-option value="">全部</ion-select-option>-->
    <!--  <ion-select-option value="0">報名中</ion-select-option>-->
    <!--  <ion-select-option value="1">不同意參加</ion-select-option>-->
    <!--  <ion-select-option value="4">辦理中</ion-select-option>-->
    <!--  <ion-select-option value="10">取錄成功</ion-select-option>-->
    <!--</ion-select>-->


  </ion-row>
  <ion-row class="row" style="margin-bottom: 3px;">
    <ion-label class="input-label">類別</ion-label>
    <ion-select
        [(ngModel)]="activityFilter.actCategory"
        placeholder="類別"
        okText="確認"
        cancelText="取消"
        interface="popover"
        (ngModelChange)="loadDataActivity()">
      <ion-select-option value="">全部</ion-select-option>
      <ion-select-option
          *ngFor="let item of categorys"
          [value]="item.id"
      >{{ item.cateName }}</ion-select-option>
    </ion-select>
  </ion-row>
  <ion-row class="row" style="margin-bottom: 3px;">
    <ion-label class="input-label">報名截止日期</ion-label>
    <!--<ion-datetime-->
    <!--    [(ngModel)]="activityFilter.actExpire"-->
    <!--    displayFormat="YYYY/MM/DD"-->
    <!--    min="1990"-->
    <!--    max="2050"-->
    <!--    (ionCancel)="onCancelActExpire()"-->
    <!--&gt;</ion-datetime>-->
    <ion-datetime-button datetime="datetime">
      <span slot="date-target" style="display: inline-block;width: 98px;background: #ffffff;height: 25px;line-height: 25px;border-radius: 7px;">{{ actExpireStr }}</span>
    </ion-datetime-button>

    <ion-modal [keepContentsMounted]="true">
      <ng-template>
        <ion-datetime
          #actDatetime
          [(ngModel)]="activityFilter.actExpire"
          [showDefaultButtons]="true"
          presentation="date"
          id="datetime"
          min="1990"
          max="2050"
          doneText="確認"
          cancelText="清除"
          (ionCancel)="onCancelActExpire(actDatetime)"
        ></ion-datetime>
      </ng-template>
    </ion-modal>
    <ion-button color="search" (click)="loadDataActivity()" >搜尋</ion-button>
  </ion-row>
</ion-header>
<ion-content #contentActivity >

  <ion-refresher slot="fixed" [pullMax]="500" (ionRefresh)="loadDataActivity($event, true, true)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-list #listActivity *ngIf="listDataActivity.length > 0; else emptyTips">
    <ion-item
        *ngFor="let item of listDataActivity"
        detail
        button
        lines="none"
        class="list-item"
        [classList]="'list-item ' + getStatusClass(item.status)"
        (click)="toDetailStudent(item)"
    >
      <div class="item-info">
        <div class="project-name">{{ item.actName }}</div>
        <div class="info">編號：{{ item.actCode }}</div>
        <div class="info">負責老師：{{ item.actTeacher }}</div>
        <div class="info">{{ item.actCategory }}</div>
        <div class="info" *ngIf="getStatus(item.status)">狀態：{{ getStatus(item.status) }}</div>
      </div>
    </ion-item>
  </ion-list>

  <ng-template #emptyTips>
    <!--<div class="page-empty-tips"></div>-->
    <div class="empty-tips">
      暫無數據
    </div>
  </ng-template>

  <ion-infinite-scroll #infiniteScrollActivity threshold="10px" (ionInfinite)="loadDataActivity($event, false)">
    <ion-infinite-scroll-content
        loadingSpinner="bubbles"
        loadingText="正在加載...">
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
