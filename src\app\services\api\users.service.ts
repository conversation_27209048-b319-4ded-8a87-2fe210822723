import { Injectable } from '@angular/core';
import {RequestService} from '@services/request/request.service';

@Injectable({
  providedIn: 'root'
})
export class UsersService {

  constructor(private request: RequestService) {
  }

  fetchUsers({ nickname, enable, level, order_by, is_engineer }: any) {
    return this.request.request({
      url: '/users',
      method: 'get',
      params: {
        nickname, enable, level, order_by, is_engineer
      }
    })
  }

}
