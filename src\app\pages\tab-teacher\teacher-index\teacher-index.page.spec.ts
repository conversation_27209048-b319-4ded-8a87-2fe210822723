import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TeacherIndexPage } from './teacher-index.page';

describe('TeacherIndexPage', () => {
  let component: TeacherIndexPage;
  let fixture: ComponentFixture<TeacherIndexPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TeacherIndexPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TeacherIndexPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
