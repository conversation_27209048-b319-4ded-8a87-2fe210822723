<!--<ion-header>-->
<!--  <ion-toolbar>-->
<!--    <ion-button (click)="onClose()">-->
<!--      <ion-icon slot="icon-only" name="arrow-back"></ion-icon>-->
<!--    </ion-button>-->
<!--    <ion-title>班別</ion-title>-->
<!--  </ion-toolbar>-->
<!--</ion-header>-->
<ion-header class="inside-page">
  <ion-toolbar>
    <ion-buttons slot="start">
      <!--<ion-back-button (click)="onClose()"></ion-back-button>-->

      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>班別</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <div
      *ngFor="let grade of classes"
      class="grade">
    <div
        *ngFor="let class of grade"
        class="class-item ion-activatable"
        (click)="onCheckClass(class)"
    >
      {{ class['classCode'] }}
      <ion-ripple-effect type="bounded"></ion-ripple-effect>
    </div>
  </div>
</ion-content>
