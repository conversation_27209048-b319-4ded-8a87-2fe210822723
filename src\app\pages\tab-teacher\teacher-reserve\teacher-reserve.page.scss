ion-content {
  --background: #FFFFFF;
  color: #606266;
  --color: #606266;

  --padding-top: 0.5rem;
  --padding-end: 0.5rem;
  --padding-start: 0.5rem;
  --padding-bottom: 0.5rem;

  .form {
    font-size: 0.8rem;
    .form-item {
      display: inline-flex;
      .label {
        display: inline-block;
        height: 1.5rem;
        line-height: 1.5rem;
      }
      .input {
        border: 1px solid #EDEDED;
        border-radius: 0.2rem;
        display: inline-block;
        width: 5rem;
        height: 1.5rem;
        margin-bottom: 0.5rem;
        margin-right: 0.5rem;
        flex: 1;
        padding: 0 0.3rem;
        line-height: 1.5rem;
        display: inline-flex;

        --padding-start: 0.5rem;

        ::ng-deep {
          .select-icon {
            .select-icon {
              line-height: 1.5rem;
              height: 1.5rem;
            }
          }
          input {
            padding-top: 0;
            padding-bottom: 0;
          }
        }
        ::ng-deep {
          .select-icon {
            .select-icon {
              line-height: 1.5rem;
              height: 1.5rem;
            }
          }
        }

          &.remark {
            height: auto;
          }


          &.rule,&.character {
            --padding-end: 20px;
          }
        }

      &.sch-year, &.name, &.rule, &.stu-num, &.remark, &.character {
          width: 100%;
        }
      &.sch-year {
        display: inline-flex;
        ::ng-deep {
          .select-icon {
            line-height: 1.5rem;
            height: 1.5rem;
          }
        }
      }
      }
    .sub-box {

      padding: 0.5rem 0.5rem;
      border-radius: 0.2rem;

      .sub-item {
        margin-bottom: 0.3rem;
        background: rgba(237, 237, 237, 0.18);
        padding: 0.5rem 0 0.5rem 0.5rem;

        .sub-form-item {
          display: inline-flex;
          width: 100%;

          .label {
            display: inline-block;
            height: 1.5rem;
            line-height: 1.5rem;
          }

          .input {
            background: #FFFFFF;
            border: 1px solid #EDEDED;
            border-radius: 0.2rem;
            display: inline-block;
            width: 5rem;
            height: 1.5rem;
            margin-bottom: 0.5rem;
            margin-right: 0.5rem;
            flex: 1;
            padding: 0 0.3rem;
            line-height: 1.5rem;
            display: inline-flex;
            --padding-start: 0.5rem;

            ::ng-deep {
              .select-icon {
                .select-icon {
                  line-height: 1.5rem;
                  height: 1.5rem;
                }
              }
              input {
                padding-top: 0;
                padding-bottom: 0;
              }
            }

            &.remark {
              height: auto;
            }


            &.rule, &.character {
              --padding-end: 20px;
            }
          }

          &.sch-year, &.name, &.rule, &.stu-num, &.remark, &.character {
            width: 100%;
          }

          &.sch-year {
            display: inline-flex;

            ::ng-deep {
              .select-icon {
                line-height: 1.5rem;
                height: 1.5rem;
              }
            }
          }
        }
      }
    }


    .files {
      min-height: 5rem;
      padding-left: 1rem;
    }

    .select-file {
      width: 5rem;
      height: 5rem;
      border: 1px solid #D6D5D5;
      border-radius: 0.3rem;
      text-align: center;
      position: relative;
      margin-bottom: 0.5rem;

      ion-icon {
        height: 4.4rem;
        width: 4.4rem;
        margin: 0.3rem;
      }
    }

    .file-preview {
      position: relative;
      padding-bottom: 0.5rem;
      min-height: 5rem;
      ion-img {
        width: 5rem;
        height: 5rem;
        border: 1px solid #D6D5D5;
        border-radius: 0.3rem;
        text-align: center;
        position: relative;
        margin-right: 0.5rem;
      }

      ion-icon {
        position: absolute;
        top: 0;
        margin: 0 auto;
        width: 1.3rem;
        height: 1.3rem;
        background: #7a7a7aab;
        color: #FFFFFF;
        border-radius: 0.3rem;
        left: 3.5rem;
      }
    }
}
}

ion-icon[icon="arrow-forward"] {
  position: absolute;
  right: 0;
  top: 0;
  height: 1.5rem;
  height: 0.7rem;
  padding: 0.4rem 0.1rem;
}

ion-select {
  ::ng-deep {
    .empty {

    }
  }
}
