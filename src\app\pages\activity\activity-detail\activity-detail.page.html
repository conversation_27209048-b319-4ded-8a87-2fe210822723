<ion-header class="inside-page none-border">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button ></ion-back-button>
    </ion-buttons>
    <ion-title>{{ title }}</ion-title>
    <ion-buttons slot="end">
      <ion-button [hidden]="!showDown || !canSelect" (click)="onDown()">
        {{ 'ACTIVITY.DETAIL.DOWN' | translate }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
  <div class="tabs none-border">
    <!--<ion-toolbar>-->
    <ion-segment [(ngModel)]="tab" (ionChange)="segmentChanged($event)">
      <ion-segment-button value="activity">
        <ion-label>{{ 'ACTIVITY.DETAIL.ACTIVITY' | translate }}</ion-label>
      </ion-segment-button>
      <ion-segment-button value="vn">
        <ion-label>{{ 'ACTIVITY.DETAIL.VENUE_AND_TIME' | translate }}</ion-label>
      </ion-segment-button>
      <ion-segment-button value="student">
        <ion-label>{{ 'ACTIVITY.DETAIL.STUDENT_LIST' | translate }}</ion-label>
      </ion-segment-button>
    </ion-segment>
    <!--</ion-toolbar>-->
  </div>
</ion-header>

<ion-content *ngIf="data && data.activity" [hidden]="tab !== 'activity'">
  <div class="activity-title">{{ actCode }} | {{ actTeacher }} | {{ actType }}</div>

  <!-- 日期 -->
  <app-info-label *ngIf="'167'.includes(data.activity.actType)" title="日期">
    <table class="info-table">
      <tr *ngIf="'16'.includes(data.activity.actType)">
        <td>{{ 'ACTIVITY.DETAIL.ACT_EXPIRE1' | translate }}</td>
        <td>{{ data.activity.actExpire }}</td>
      </tr>
      <tr *ngIf="'7' === data.activity.actType">
        <td>{{ 'ACTIVITY.DETAIL.ACT_EXPIRE2' | translate }}</td>
        <td>{{ data.activity.actExpire2 }}</td>
      </tr>
      <tr *ngIf="'16'.includes(data.activity.actType)">
        <td>{{ 'ACTIVITY.DETAIL.ACT_PUBLISH' | translate }}</td>
        <td>{{ data.activity.actPublish }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 資助 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_SUPPORT') + ' (活動費用: ' + data.activity.actMoney + ')' ">
    <table class="info-table">
      <tr *ngFor="let item of data.actSupport">
        <td>{{ item }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 名額 -->
  <app-info-label *ngIf="data.activity.actQuota" [title]="translate.instant('ACTIVITY.DETAIL.ACT_QUOTA')">
    <table class="info-table">
      <tr >
        <td>{{ data.activity.actQuota }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 歸程方法 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_BACK')">
    <table class="info-table">
      <tr *ngFor="let item of data.actBack">
        <td>{{ item }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 備註 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_REMARK')">
    <table class="info-table">
      <tr *ngFor="let item of data.activityWT">
        <td>{{ item.actPlaceRemark }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 項目 -->
  <app-info-label *ngIf="data.actPjCList && data.actPjCList.length > 0" [title]="translate.instant('ACTIVITY.DETAIL.ACT_PROJECT')">
    <table class="info-table">
      <tr *ngFor="let item of data.actPjC">
        <td>{{ item }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 活動描述 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_DESC')">
    <table class="info-table">
      <tr >
        <td>{{ data.activity.actRemark }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 招生通告 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_FILE1')" *ngIf="'1356'.includes(data.activity.actType)">
    <table class="info-table">
      <tr >
        <td>
          <i *ngIf="data.activity.actFile1" class="iconfont icon-PDF" (click)="openFile(data.activity.actFile1)"></i>
        </td>
      </tr>
    </table>
  </app-info-label>
  <!-- 取錄通告 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_FILE2')" *ngIf="'13567'.includes(data.activity.actType)">
    <table class="info-table">
      <tr >
        <td>
          <i *ngIf="data.activity.actFile2" class="iconfont icon-PDF" (click)="openFile(data.activity.actFile2)"></i>
        </td>
      </tr>
    </table>
  </app-info-label>

</ion-content>
<ion-content *ngIf="data && data.activityWT" [hidden]="tab !== 'vn'" class="vn-content">
  <div class="activity-title">{{ 'ACTIVITY.DETAIL.VENUE_TIPS' | translate }}</div>
  <div
      class="vn-box"
      *ngFor="let item of data.activityWT"
  >
    <div class="vn-title">{{ item.actOtherPlace }} | {{ item.actStartTime }}~{{ item.actEndTime }}</div>

    <div *ngIf="stuLottery && stuLottery.back" class="stu-back">
      {{ 'ACTIVITY.DETAIL.ACT_BACK' | translate }}： {{ stuLottery.back }}
    </div>
    <div class="vn-date-box">
      <div class="vn-year" *ngFor="let year of item.dateArr">
        <div class="year-label">{{ year.year }}</div>
        <div *ngFor="let month of year.months; let mi = index">
          <div class="divide" *ngIf="mi !== 0"></div>
          <div class="vn-month">
            <div class="vn-date ion-activatable" *ngFor="let day of month" (click)="onRollCall(item.actKey, year.year, day.date)">
              {{ day.dateCn }}
              <div class="vn-week">{{ day.week}}</div>
              <span class="dot" *ngIf="day.isToday"></span>
              <ion-ripple-effect type="bounded"></ion-ripple-effect>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ion-content>
<ion-content [hidden]="tab !== 'student'" >
  <div class="type-select">
    <ion-label>{{ stuListTypeStr }}</ion-label>
    <ion-button color="search" size="mini" (click)="onChangeStudentList()">{{ 'BUTTON.SELECT' | translate }}</ion-button>
  </div>
  <div class="student-list"  *ngIf="stuList && stuList.length && stuList.length > 0">
    <div class="class-box" *ngFor="let class of stuList">
      <div class="class-title">{{ class.class }}</div>
      <div class="stu-box">
        <div [className]="'stu-item ion-activatable ' + getStuStatusClass(stu)" *ngFor="let stu of class.list">
          {{ stu.stuUname}}
          <ion-ripple-effect type="bounded"></ion-ripple-effect>
        </div>
      </div>
    </div>
  </div>
  <div class="stu-empty-tips" *ngIf="stuList && stuList.length === 0">{{ 'MESSAGE.NO_DATA' | translate }}</div>
</ion-content>
