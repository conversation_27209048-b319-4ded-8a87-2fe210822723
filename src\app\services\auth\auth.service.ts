import {Injectable} from '@angular/core'

import Cookies from 'js-cookie'

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  private tokenKey = 'APP-Norray-Token'

  constructor() {
  }

  getToken() {
    Cookies.get(this.tokenKey)
    return sessionStorage.getItem(this.tokenKey)
    // return Cookies.get(tokenKey)
  }

  setToken(token) {
    Cookies.set(this.tokenKey, token)
    return sessionStorage.setItem(this.tokenKey, token)
    // return Cookies.set(tokenKey, token)
  }

  removeToken() {
    Cookies.remove(this.tokenKey)
    return sessionStorage.removeItem(this.tokenKey)
  }


}
