import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { ActivitySelectClassPage } from './activity-select-class.page';

const routes: Routes = [
  {
    path: '',
    component: ActivitySelectClassPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  // declarations: [ActivitySelectClassPage]
})
export class ActivitySelectClassPageModule {}
