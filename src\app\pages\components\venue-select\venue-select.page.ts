import { Component, Input, OnInit } from '@angular/core';
import {ModalController} from '@ionic/angular';
import {EBookingService} from '@services/api/ebooking.service';
import {LoadingService} from '@app/utils/loading.service';

@Component({
  selector: 'app-venue-select',
  templateUrl: './venue-select.page.html',
  styleUrls: ['./venue-select.page.scss'],
})
export class VenueSelectPage implements OnInit {
  @Input() date: any = ''
  @Input() natureId: any = ''
  @Input() ruleId: any = ''
  @Input() lessonIds: any = ''
  @Input() venueIds: any = ''
  public list: any = []
  public idList: any = []
  constructor(
    private modalCtrl: ModalController,
    private ebooking: EBookingService,
    private loadingService: LoadingService,
  ) { }

  ngOnInit() {
    // if (typeof this.venueIds === 'string' && this.venueIds.length > 0) {
    //   this.idList = this.venueIds.split(',')
    // }
    if (Array.isArray(this.venueIds)) {
      this.idList = this.venueIds
    }
    this.loadData()
  }

  async loadData() {
    await this.loadingService.start()
    try {
      const date = this.date
      const natureId = this.natureId
      const ruleId = this.ruleId
      const lessonIds = this.lessonIds
      const res: any = await this.ebooking.getResources({
        date,
        natureId,
        ruleId,
        resourceType: '1',
        lessonIds
      })
      console.log(res)

      const list = res.resources
      list.forEach(item => {
        item.check = this.idList.includes(item.id)
      })
      this.list = res.resources
    } catch (e) {
      console.error(e)
    }
    await this.loadingService.end()
  }

  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true
    });
  }
  onSave() {
    const data = this.list.filter(i => i.check)
    this.modalCtrl.dismiss({
      dismissed: true,
      save: true,
      data
    });
  }

}
