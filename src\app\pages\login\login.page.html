<form (submit)="handleLogin()" class="login-page">
  <ion-header no-border>
    <ion-toolbar class="login-toolbar">
<!--      <ion-row>-->
<!--        <ion-col>-->
<!--&lt;!&ndash;          <ion-icon class="setting-btn" name="settings" (click)="toSetting()"></ion-icon>&ndash;&gt;-->
<!--&lt;!&ndash;          <i class="iconfont icon-shezhi setting-btn" (click)="toSetting()"></i>&ndash;&gt;-->
<!--          <i></i>-->
<!--        </ion-col>-->
<!--      </ion-row>-->
      <ion-buttons *ngIf="pageType === 'switch'" slot="start">
        <ion-button (click)="onBack()">
          <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>
  <ion-content fullscreen [scrollY]="false" [scrollX]="false">
    <div class="content" style="display: flex;flex-direction: column;height: 100%;" [style.height]="shouldHeight">
      <ion-grid style="margin-left: 10%;margin-right: 10%;">
        <ion-row>
          <!--          <ion-col>-->
          <!--          </ion-col>-->
          <ion-col style="text-align: center;">
            <img *ngIf="logoUrl; else elseLogo" class="logo-img" [src]="logoUrl">
            <ng-template #elseLogo>
              <ion-skeleton-text animated class="logo-img"></ion-skeleton-text>
            </ng-template>
          </ion-col>
          <!--          <ion-col>-->
          <!--          </ion-col>-->
        </ion-row>
        <ion-row>
          <ion-col>
            <ion-text *ngIf="schNameCn; else elseCn" class="sch-name-cn ion-text-center">{{ schNameCn }}</ion-text>
            <ng-template #elseCn>
              <ion-skeleton-text *ngIf="!schNameCn" animated class="sch-name-cn "></ion-skeleton-text>
            </ng-template>
            <ion-text *ngIf="schNameCn; else elseCn" class="sch-name-en ion-text-center">{{ schNameEn }}</ion-text>
            <ng-template #elseEn>
              <ion-skeleton-text *ngIf="!schNameCn" animated class="sch-name-en"></ion-skeleton-text>
            </ng-template>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <ion-item class="form-item username">
              <!--              <ion-icon name="contact"></ion-icon>-->
              <ion-input
                  required name="username"
                  [(ngModel)]="loginForm.username"
                  class="form-input username"
                  inputmode="text"
                  type="text"
                  [clearInput]="true"
                  [autocorrect]="true"
                  placeholder="用戶名"
                  (ionChange)="checkCanSubmit()"
                  (keyup.enter)="handleNextInput()"
              ></ion-input>
              <ion-icon *ngIf="userCount > 0" mode="ios" [icon]="popoverIcon" (click)="onList($event)"></ion-icon>
            </ion-item>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <ion-item class="form-item">
              <!--              <ion-icon name="lock"></ion-icon>-->
              <ion-input
                  #pwd
                  required name="password"
                  [(ngModel)]="loginForm.password"
                  class="form-input"
                  inputmode="password"
                  type="password"
                  [clearInput]="true"
                  placeholder="密碼"
                  (ionChange)="checkCanSubmit()"
                  (keyup.enter)="handleLogin()"
              ></ion-input>
            </ion-item>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col style="text-align: center;">
            <ion-button
                class="submit-btn"
                type="submit"
                expand="undefined"
                [disabled]="!canSubmit"
            >{{ 'LOGIN.LOGIN' | translate}}
            </ion-button>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col style="text-align: center;">
            <a class="button forget-password" href="javascropt: void(0)" (click)="handForgetPassword()">
              {{ 'LOGIN.FORGET_PASSWORD' | translate}}
            </a>
          </ion-col>
        </ion-row>
        <ion-row *ngIf="rollCallCount > 0 && pageType === 'login'">
          <ion-col style="text-align: center;">
            <a class="button roll-call" href="javascropt: void(0)" (click)="onRollCall()">
              {{ 'LOGIN.ROLL_CALL' | translate }}
            </a>
          </ion-col>
        </ion-row>
      </ion-grid>
      <div class="login-page-footer">{{ 'LOGIN.VERSION' | translate}}: {{ version }}</div>
    </div>
  </ion-content>

</form>
