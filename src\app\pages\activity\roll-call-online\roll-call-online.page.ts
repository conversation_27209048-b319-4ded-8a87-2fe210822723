import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {ActionSheetController, AlertController, ModalController} from '@ionic/angular';
import {ActivityService} from '@services/api/activity.service';
import {LoadingService} from '@app/utils/loading.service';
import {UtilsService} from '@app/utils/utils.service';

@Component({
  selector: 'app-roll-call-online',
  templateUrl: './roll-call-online.page.html',
  styleUrls: ['./roll-call-online.page.scss'],
})
export class RollCallOnlinePage implements OnInit {

  @Input() domId = 'edit-day-modal'
  @Input() activity_id: any = ''
  @Input() data: any = {}
  @Input() year: any = ''
  @Input() day: any = ''
  @Input() itemIndex: any = ''
  @Output() public reload = new EventEmitter();
  public item: any = {}
  public yearData: any = {}
  constructor(
    private modalCtrl: ModalController,
    public actionSheetController: ActionSheetController,
    private activityService: ActivityService,
    private loadingService: LoadingService,
    private alertController: AlertController,
    private utils: UtilsService,
  ) {
  }

  ngOnInit() {
    console.log(this.data, this.itemIndex)
    if (this.data && this.itemIndex != null) {
      try {
        this.yearData = this.data.activity_date[this.itemIndex]
        const year = this.data.activity_date[this.itemIndex].actDateArray[this.year]
        const item = year.find(d => d.date === this.day)
        if (item) {
          this.item = JSON.parse(JSON.stringify(item))
        } else {
          this.item = {}
        }
      } catch (e) {
        console.error(e)
      }
    }
  }
  onClose(save = false) {
    this.modalCtrl.dismiss({
      dismissed: true,
      save
    });
  }
  getStatus(stu) {
    const status = stu.status
    // if (status === null || status === undefined) {
    //   return '未設定'
    // }
    switch (status) {
      case 'ATTEND':
        return '出席'
      case 'ABSENT':
      case 'ANOTHER ACTIVITY':
      case 'SICK LEAVE':
      case 'DETENTION':
      case 'PERSONAL LEAVE':
        return '缺席'
      // case 'ANOTHER ACTIVITY':
      //   return '缺席 : 課外活動'
      // case 'SICK LEAVE':
      //   return '缺席 : 病假'
      // case 'DETENTION':
      //   return '缺席 : 被老師罰留堂'
      // case 'PERSONAL LEAVE':
      //   return '缺席 : 事假'
      case 'LATE':
        return '遲到'
      case 'LEAVE EARLY':
        return '早退'
      default:
        return '未設定'
    }

  }
  getStatusColor(stu) {
    const status = stu.status
    // if (status === null || status === undefined) {
    //   return 'white'
    // }

    switch (status) {
      case 'ATTEND':
        return 'green'
      case 'ABSENT':
      case 'ANOTHER ACTIVITY':
      case 'SICK LEAVE':
      case 'DETENTION':
      case 'PERSONAL LEAVE':

      case 'LATE':
      case 'LEAVE EARLY':
        return 'yellow'
      default:
        return 'white'
    }

  }

  onchangeStatusToAttend(stu, i) {
    stu.status = 'ATTEND'
  }
  async onChangeStatus(stu, i) {
    await this.setStatus(stu)
    // const status = await this.setStatus(stu)
    // stu.status = status
  }

  getStatusIcon(status, option) {
    if (status) {
      return status === option ? 'checkmark' : '-'
    }
    return ''
  }

  async setStatus(stu) {
    const oldStatus = stu.status
    let status = ''
    const actionSheet = await this.actionSheetController.create({
      header: '選擇名單類型',
      buttons: [{
        icon: this.getStatusIcon(oldStatus, 'ATTEND'),
        text: '出席',
        handler: () => {
          status = 'ATTEND'
          stu.status = status
        }
      // }, {
      //   text: '缺席',
      //   handler: () => {
      //     status = 'ABSENT'
      //     stu.status = status
      //   }
      }, {
        icon: this.getStatusIcon(oldStatus, 'ANOTHER ACTIVITY'),
        text: '缺席 : 課外活動',
        handler: () => {
          status = 'ANOTHER ACTIVITY'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, 'SICK LEAVE'),
        text: '缺席 : 病假',
        handler: () => {
          status = 'SICK LEAVE'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, 'DETENTION'),
        text: '缺席 : 被老師罰留堂',
        handler: () => {
          status = 'DETENTION'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, 'PERSONAL LEAVE'),
        text: '缺席 : 事假',
        handler: () => {
          status = 'PERSONAL LEAVE'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, 'LATE'),
        text: '遲到',
        handler: () => {
          status = 'LATE'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, 'LEAVE EARLY'),
        text: '早退',
        handler: () => {
          status = 'LEAVE EARLY'
          stu.status = status
        }
      }, {
        icon: this.getStatusIcon(oldStatus, '-'),
        text: '重置',
        // icon: 'close',
        // role: 'cancel',
        handler: () => {
          status = ''
          stu.status = status
        }
      }]
    });
    await actionSheet.present();
    await actionSheet.onDidDismiss();

    // stu.status = status
    return status
  }

  async onSave() {
    const saveType = await this.saveConfirm()
    if (saveType === '0') { return }

    await this.loadingService.start()
    try {
      const activity_id = this.activity_id
      const send_notice = saveType === '2' ? '1' : '0'

      const actKey = this.data.activity_date[this.itemIndex].actKey
      const students = []

      for (const stu of this.item.students) {
        students.push({
          student_id: stu.student_id,
          status: stu.status || null,
        })

      }


      const roll_call_array = [
        {
          actKey,
          dateArray: [
            {
              date: this.item.date,
              students
            }
          ]
        }
      ]


      const res = await this.activityService.saveRollCall({ activity_id, roll_call_array, send_notice })
      this.reload.emit(true)
      this.utils.showToast({ msg: '保存成功！' })
      this.onClose(true)
      console.log(res)
    } catch (e) {
      switch (e.code) {
        case 3003:
          break
      }
      console.error(e)
      this.utils.showMsg('保存失敗，請重試！')
    }

    await this.loadingService.end()

  }


  async saveConfirm() {
    let type = '0'
    const alert = await this.alertController.create({
      header: '提示',
      message: '是否確認保存?',
      backdropDismiss: false,
      inputs: [
        // {
        //   name: 'name',
        //   placeholder: 'Your trip name',
        // },
        {
          name: 'notice',
          type: 'checkbox',
          checked: true,
          label: '發送訊息到家長手機',
          value: 'notice'
        }
      ],
      // subHeader: '',
      buttons: [
        {
          text: '取消',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (data) => {
          }
        }, {
          text: '保存',
          handler: (data) => {
            if (data.includes('notice')) {
              type = '2'
            } else {
              type = '1'
            }
          }
        }
      ]
    });
    await alert.present();
    await alert.onDidDismiss()
    return type
  }

  onAllAttend() {
    console.log('全部出席')
    const isAll = this.item.students.every(stu => stu.status === 'ATTEND')
    this.item.students.forEach(stu => {
      stu.status = isAll ? null : 'ATTEND'
    })
  }
  get allSetStr() {
    const isAll = this.item.students.every(stu => stu.status === 'ATTEND')
    return isAll ? '取消全部出席' : '全部出席'
  }
  get allSetClass() {
    const isAll = this.item.students.every(stu => stu.status === 'ATTEND')
    return isAll ? 'white' : 'green'
  }

}
