ion-content {
  & > ion-list {
    padding: 0;
    ion-item-group.list {

      & > ion-item {
        & > ion-label {
          font-size: 14px;
        }

        & > ion-text {
          font-size: 13px;
          text-align: right;
          width: 100%;
          width: 100px;
          color: #B0B0B0;

          white-space: nowrap;
        }
      }

      ion-item-group.detail {
        padding-left: 20px;
        ion-item {
          --min-height: 30px;
          ion-label {
            font-size: 12px;
          }
          ion-text {
            font-size: 12px;
            text-align: right;
            width: 100%;

            word-break: keep-all;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            width: 100%;

          }
        }

      }
    }
  }
}

i.dot {
  &:after {
    content: "";
    width: 5px;
    height: 5px;
    min-width: 5px;
    min-height: 5px;
    display: inline-block;
    vertical-align: middle;
    border-radius: 50%;

  }
  &.success:after {
    background: #4CC726;
  }
  &.warning:after {
    background: #FF9900;
  }
  &.error:after {
    color: #F56C6C;
  }
}

ion-icon {
  width: 20px;
  height: 20px;
}
