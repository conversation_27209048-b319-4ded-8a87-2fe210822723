import { Component, OnInit } from '@angular/core';
import {ActivityService} from '@services/api/activity.service';
import {UserService} from '@services/api/user.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {LoadingService} from '@app/utils/loading.service';
import {StoreService} from '@services/store/store.service';

@Component({
  selector: 'app-teacher-profile',
  templateUrl: './teacher-profile.page.html',
  styleUrls: ['./teacher-profile.page.scss'],
})
export class TeacherProfilePage implements OnInit {

  public form = {
    username: '',
    email: '',
    pass: '',
    passConfirm: '',
  }
  constructor(
    private activity: ActivityService,
    private userService: UserService,
    private nativeStorage: NativeStorage,
    private utils: UtilsService,
    private loading: LoadingService,
    private store: StoreService,
    ) { }

  ngOnInit() {
    this.loadInfo()
  }

  async loadInfo() {
    let userId = ''
    try {
      const user = await this.nativeStorage.getItem('userInfo')
      console.log(user)
      if (user) {
        userId = user.user_id
        this.form.username = user.username
      }
    } catch (e) {
      console.error(e)
    }
    if (!userId) {
      this.utils.showToast({msg: '登錄信息失效，請重新登錄！'})
      return
    }
  }

  trim(text) {
    return text.toString().replace('/ /g', '')
  }

  async onSave() {
    let userId = ''
    try {
      const user = await this.nativeStorage.getItem('userInfo')
      if (user) {
        userId = user.user_id
      }
    } catch (e) {
      console.error(e)
    }
    if (!userId) {
      this.utils.showToast({msg: '登錄信息失效，請重新登錄！'})
      return
    }
    const password = this.form.pass
    const passConfirm = this.form.passConfirm
    if (!password || !passConfirm) {
      if (this.trim(password).length === 0) {
        this.utils.showToast({msg: '請填上新密碼！'})
        return
      } else if (this.trim(passConfirm).length === 0) {
        this.utils.showToast({msg: '請填上重入新密碼！'})
        return
      }
    }
    if (await this.utils.confirm('確認重設密碼', '重設密碼後，將退出至登錄頁面！', '確認', '取消')) {
      this.handleSave(userId, password)
    }
  }
  async handleSave(userId, password) {
    await this.loading.start()
    try {
      const res: any = await this.userService.changePassword({
        userId,
        password,
      })
      this.utils.showToast({msg: '保存成功，請重新登錄！'})
      this.store.Logout()
      console.log(res)
    } catch (e) {
      console.error(e)
      this.utils.showToast({msg: '保存失敗，請重試！'})
    }
    await this.loading.end()
  }
}
