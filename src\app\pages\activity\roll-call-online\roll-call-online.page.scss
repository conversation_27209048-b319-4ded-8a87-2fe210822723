.act-info {
  background: #EEEEEE;
  font-size: 0.6rem;
  line-height: 1rem;
  padding: 0.3rem 0.5rem;
  position: relative;
  .label {
    font-weight: 500;
  }
  .act-tips {
    color: #2469F2;
    font-size: 0.5rem;
  }
}

ion-item {
  //padding: 0.2rem 0;
  .stu-item {
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-between;
    line-height: 1.1rem;
    font-size: 0.7rem;
    .index {
      width: 1rem;
      text-align: center;
      flex: 0.5;
    }
    .info {
      flex: 5;
      padding: 0 0.3rem;

      .info-1 {
        span, i {
          padding-right: 0.5rem;
        }
        i {
          font-size: 0.7rem;
        }

        .icon-nan {
          color: #4597E0;
        }
        .icon-nv {
          color: #ea6464;
        }
      }
    }
    .status {
      width: 4rem;

      display: flex;
      justify-content: center;
      justify-items: center;
      flex-direction: column;
    }
  }
}

.batch-set {
  text-align: right;
  position: absolute;
  bottom: 0.5rem;
  width: 100%;
  right: 0.5rem;
  ion-button {
    //margin-right: 16px;
    //width: 4rem;
    //width: calc(4rem - 4px);
    margin-right: 18px;
    min-width: 4rem;
    min-width: calc(4rem - 4px);
    width: 4rem;
    font-size: 0.5rem;
  }
}