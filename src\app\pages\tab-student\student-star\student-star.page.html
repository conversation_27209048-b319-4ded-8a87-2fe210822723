<ion-header class="center">
    <div class="tabs none-border">
        <!--<ion-toolbar>-->
        <ion-segment [(ngModel)]="tab" (ionChange)="segmentChanged($event)">
            <ion-segment-button class="custom-segment-button" value="LIST">
                <ion-label style="font-size: 18px;font-weight: 600;">{{ 'STAR.INPUT' | translate }}</ion-label>
                <div [hidden]="tab !== 'LIST'" class="pane"></div>
            </ion-segment-button>
            <ion-segment-button class="custom-segment-button" value="SUMMARY">
                <ion-label style="font-size: 18px;font-weight: 600;">{{ 'STAR.SEARCH' | translate }}</ion-label>
                <div [hidden]="tab !== 'SUMMARY'" class="pane"></div>
            </ion-segment-button>
        </ion-segment>
        <!--</ion-toolbar>-->
    </div>
</ion-header>

<ion-content #contentList *ngIf="listData" [hidden]="tab !== 'LIST'">
    <ion-header class="filter">
        <ion-row class="row" style="margin-bottom: 3px;">
            <ion-col col-12 class="col">
                <ion-label class="input-label">學年</ion-label>
                <ion-select
                        [(ngModel)]="listDataFilter.year_id"
                        placeholder="學年"
                        okText="確認"
                        cancelText="取消"
                        interface="popover"
                        (ngModelChange)="changeYear(true)">
                    <ion-select-option
                            *ngFor="let year of years"
                            [value]="toNumber(year.id)">
                        {{ year.year_name }}
                    </ion-select-option>
                </ion-select>
            </ion-col>
            <ion-col col-12 class="col">
                <ion-label class="input-label">學期</ion-label>
                <ion-select
                        [(ngModel)]="listDataFilter.semester"
                        placeholder="學期"
                        okText="確認"
                        cancelText="取消"
                        interface="popover"
                        (ngModelChange)="changeSemester()">
                    <ion-select-option [value]="1">上學期</ion-select-option>
                    <ion-select-option [value]="2">下學期</ion-select-option>
                </ion-select>
            </ion-col>

        </ion-row>
        <ion-row class="row" style="margin-bottom: 3px;">
            <ion-col col-24 class="col">
                <ion-label class="input-label">摘星種類</ion-label>
                <ion-select
                        [(ngModel)]="listDataFilter.scid"
                        class="long_col"
                        placeholder="類別"
                        okText="確認"
                        cancelText="取消"
                        interface="popover"
                        (ngModelChange)="loadListData()">
                    <ion-select-option value="">全部</ion-select-option>
                    <ion-select-option
                            *ngFor="let item of categories"
                            [value]="item.id"
                    >{{ item.name }}</ion-select-option>
                </ion-select>
                <ion-button color="search" (click)="loadListData()">搜尋</ion-button>
            </ion-col>
        </ion-row>
    </ion-header>
    <ion-list *ngIf="listData.length > 0; else emptyTips">
        <ion-button [hidden]="!canEdit" (click)="submit()" style="width: calc(100vw - 24px);margin: 0px 12px 8px 12px;border-radius: 4px;--background: #80B777FF;text-align: center">提 交</ion-button>
        <ion-item
                *ngFor="let item of listData;let index = index"
                detail
                button
                lines="none"
                class="list-item"
                [classList]="'list-item ' + getStatusClass(item.type_value)"
                (click)="toInput(item, index)"
        >
            <div class="item-info">
                <div class="project-name">{{ item.cname }}</div>
                <div class="info">摘星標題：{{ item.tname }}</div>
                <div class="info">摘星項目：{{ item.detail }}</div>
                <div class="info">獲取顆數：
                    <i
                            *ngFor="let starCount of [].constructor(item.max_num);let starIndex = index"
                            [classList]="getStarClass(starIndex, submitJson['pid_' + item.id].num)"
                            class="iconfont icon-ios-star-outline"></i>
                </div>
                <div class="info" >狀態：{{ getStatus(item.is_audit) }}</div>
            </div>
        </ion-item>
    </ion-list>
    <ng-template #emptyTips>
        <div class="empty-tips">
            暫無數據
        </div>
    </ng-template>
</ion-content>
<ion-content #contentSummary [hidden]="tab !== 'SUMMARY'">
    <span class="detail_btn" (click)="toDetail()">摘星明細</span>
    <div #container id="chart" style="width: 100vw;height: 40vh;"></div>
    <ion-row
            *ngFor="let cate of cate_star; let cateIndex = index"
            class="row"
            style="margin-bottom: 3px;">
        <ion-col size="6" class="col">
            <ion-item
                    detail="false"
                    lines="none"
                    class="list-item-summary"
                    [classList]="'list-item-summary ' + getSummaryItemStatusClass(cateIndex, 0)"
            >
                <div class="item-info">
                    <div class="project-name">{{ cate[0].cate_name }}</div>
                    <div class="info" style="text-align: right"><span class="status_number">{{ cate[0].cate_num }}</span><span class="total_number">/{{ cate[0].cate_total }}</span></div>
                </div>
            </ion-item>
        </ion-col>
        <ion-col *ngIf="cate.length > 1" size="6" class="col">
            <ion-item
                    detail="false"
                    lines="none"
                    class="list-item-summary"
                    [classList]="'list-item-summary ' + getSummaryItemStatusClass(cateIndex, 1)"
            >
                <div class="item-info">
                    <div class="project-name">{{ cate[1].cate_name }}</div>
                    <div class="info" style="text-align: right"><span class="status_number">{{ cate[1].cate_num }}</span><span class="total_number">/{{ cate[1].cate_total }}</span></div>
                </div>
            </ion-item>
        </ion-col>
    </ion-row>
</ion-content>
