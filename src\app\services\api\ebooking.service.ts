import { Injectable } from '@angular/core';
import {RequestService} from '@services/request/request.service';

@Injectable({
  providedIn: 'root'
})
export class EBookingService {

  constructor(private request: RequestService) {
  }


  /**
   * 獲取開始預約日期
   */
  getStartDate(ruleId: string, // [string] 規則id
  ) {
    return this.request.request({
      url: `/v2/app/em/booking/start_date`,
      method: 'GET',
      params: {
        ruleId
      }

    })
  }

  /**
   * 獲取日期信息
   */
  getDateInfo(date: string, // [string] 日期
  ) {
    return this.request.request({
      url: `/v2/app/em/date/info`,
      method: 'GET',
      params: {
        date
      }
    })
  }

  /**
   * 獲取活動性質
   */
  getNatureList() {
    return this.request.request({
      url: `/v2/app/em/nature/list`,
      method: 'GET',
    })
  }

  /**
   * 獲取場地/資源
   */
  getResources({
        date,
        natureId,
        ruleId,
        resourceType,
        lessonIds
      }: {
    date: string, // [string] 日期
    natureId: string, // [string] 性質
    ruleId: string, // [string] 規則
    resourceType: string, // [string] string1:地點,2:物品
    lessonIds: string, // [string] string課節ids
  }) {
    return this.request.request({
      url: `/v2/app/em/resources`,
      method: 'GET',
      params: {
        date,
        natureId,
        ruleId,
        resourceType,
        lessonIds
      }

    })
  }

  /**
   * 獲取規則
   */
  getRules({
        yearId,
        urTypeM,
        ruleResourceType,
        natureId
      }: {
    yearId: string, // [string] 學年ID
    urTypeM: string, // [string] 用戶類型(A,M,T,S)
    ruleResourceType: string, // [string] 1:只有地點的預約規則,2:只有物品的預約規則,3:包含地點和物品的預約規則
    natureId: number, // [integer] 活動性質
  }) {
    return this.request.request({
      url: `/v2/app/em/rule/list`,
      method: 'GET',
      params: {
        yearId,
        urTypeM,
        ruleResourceType,
        natureId
      }

    })
  }

  /**
   * 獲取日期內可以預約的課節/時間信息
   */
  getSessionList(session_type_code: string, // [string] 時間表類型,默認STD
                 ruleId: string
  ) {
    return this.request.request({
      url: `/v2/app/em/session_list`,
      method: 'GET',
      params: {
        session_type_code,
        ruleId
      }
    })
  }


  /**
   * 保存預約
   */
  submitEBooking({
        name,
        noOfStudent,
        natureId,
        ruleId,
        remark,
        attachment_id,
        subFormList
      }: {
    name: string, // [string] 活動名稱
    noOfStudent: string, // [string] 參加人數
    natureId: string, // [string] 性質
    ruleId: string, // [string] 規則
    remark: string, // [string] 備註
    attachment_id: string, // [string] 附件
    subFormList: string, // [string] [{date:2020-04-28,lesson:[1],venue:[591],resource:[{id:1,qty:1}]}] 預約信息
  }) {
    return this.request.request({
      url: `/v2/app/em/booking/submit`,
      method: 'POST',
      data: {
        name,
        noOfStudent,
        natureId,
        ruleId,
        remark,
        attachment_id,
        subFormList: JSON.stringify(subFormList)
      }

    })
  }




  /**
   * 獲取所有年份
   */
  fetchEBookingYears() {
    return this.request.request({
      url: `/ebooking/year_index`,
      method: 'GET',
    })
  }


}
