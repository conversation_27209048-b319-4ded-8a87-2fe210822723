<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button ></ion-back-button>
    </ion-buttons>
    <ion-title>訊息</ion-title>
    <ion-buttons slot="end">
      <!--查看接收學生-->
      <ion-button *ngIf="!showDot" color="primary" (click)="onCheckStudent()">
        <ion-icon icon="people-circle-outline" slot="icon-only"></ion-icon>
      </ion-button>
      <!--查看附件-->
      <ion-button *ngIf="data.attachment && data.attachment.length > 0" color="primary"  (click)="onFileView()">
        <ion-icon icon="document-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="title">{{ data.title }}</div>
  <div class="time">{{ data.time }}</div>
  <div class="content" *ngIf="data.content">{{ data.content }}</div>

</ion-content>
