<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button ></ion-back-button>
    </ion-buttons>
    <ion-title>已下載的點名紙</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-list #listActivity *ngIf="list.length > 0; else emptyTips">
    <ion-item-sliding
        *ngFor="let item of list"
        detail
        button
        lines="none"
    >
      <ion-item
          class="list-item"
          [classList]="'list-item ' + getStatusClass(item.finishStatus, item.actType)"
          (click)="toDetail(item)"
      >
        <div class="item-info">
          <div class="project-name">{{ item.actName }}</div>
          <div class="info">編號：{{ item.actCode }}</div>
          <div class="info">負責老師：{{ item.actTeacher }}</div>
          <div class="info">{{ item.actCategory }}</div>
        </div>
      </ion-item>

      <ion-item-options side="end">
        <ion-item-option color="danger" (click)="onDelete(item)">刪除</ion-item-option>
      </ion-item-options>
    </ion-item-sliding>
  </ion-list>

  <ng-template #emptyTips>
    <!--<div class="page-empty-tips"></div>-->
    <div class="empty-tips">
      暫無數據
    </div>
  </ng-template>

</ion-content>
