import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {ActionSheetController, ModalController, NavController, Platform} from '@ionic/angular';
import {LoadingService} from '@app/utils/loading.service';
import {ActivityService} from '@services/api/activity.service';
import {StorageService} from '@services/storage/storage.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {RollCallService} from '@services/sql/rollCall.service';
import {TranslateService} from '@ngx-translate/core';

import { dateFormat} from '@app/utils';
// import { RollCallOnlinePage } from '@pages/activity/roll-call-online/roll-call-online.page';
import { RollCallOfflineInputPage } from '@pages/activity/roll-call-offline-input/roll-call-offline-input.page';
import {PreviewAnyFile} from '@ionic-native/preview-any-file/ngx';

@Component({
  selector: 'app-offline-roll-call',
  templateUrl: './offline-roll-call.page.html',
  styleUrls: ['./offline-roll-call.page.scss'],
})
export class OfflineRollCallPage implements OnInit {

  public id: any = ''
  public data: any = {}
  public tab = 'activity'
  public lottery: any = {}
  public stuListType = 'actStu'

  public rollCallData: any = {}
  public user: any = {}
  constructor(
    private router: Router,
    private navCtrl: NavController,
    private platform: Platform,
    private routeInfo: ActivatedRoute,
    public nav: NavController,
    private loadingService: LoadingService,
    // private activityService: ActivityService,
    public actionSheetController: ActionSheetController,
    private modalCtrl: ModalController,
    private storageService: StorageService,
    private nativeStorage: NativeStorage,
    private utils: UtilsService,
    private rollCallService: RollCallService,
    public translate: TranslateService,
    protected cdr: ChangeDetectorRef,
    ) { }

  ngOnInit() {
    this.platform.ready().then(async () => {
      // this.statusBar.backgroundColorByHexString('#003658')
      // this.id = this.routeInfo.snapshot.queryParams['id']
      this.id = this.routeInfo.snapshot.params.id
      this.loadData()
      this.user = await this.nativeStorage.getItem('userInfo')
    })
  }

  async loadData() {
    await this.loadingService.start()
    try {
      // const res: any = await this.activityService.getActivityInfo({ id: this.id })
      const data: any = await this.rollCallService.get(this.id)
      console.log(data)
      const res = data.activityInfo

      this.rollCallData = data
      this.initLottery(data.lottery)

      const w = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

      if (res && res.activityWT) {
        res.activityWT.forEach(item => {
          const date = []
          item.dateArr = []
          for (const year in item.actDateArray) {

            if (typeof year === 'string') {
              const y = {year, months: []}
              y.year = year

              let month = -1
              let monthIndex = -1
              for (const dateStr of item.actDateArray[year]) {
                const d = new Date(dateStr)
                if (month !== d.getMonth()) {
                  monthIndex++
                }
                month = d.getMonth()

                if (!Array.isArray(y.months[monthIndex])) {
                  y.months[monthIndex] = []
                }
                const dateCn = this.getDayStr(d, 'cn')
                const dateEn = this.getDayStr(d, 'en')
                const isToday = dateFormat(new Date()) === dateFormat(d)
                const week = w[d.getDay()]

                y.months[monthIndex].push({
                  date: dateStr,
                  dateCn,
                  dateEn,
                  week,
                  isToday
                })
              }

              item.dateArr.push(y)

            }
          }

        })
      }


      this.data = res


      // await this.initRollCall(this.id)
      // await this.initLottery()

    } catch (e) {
      console.error(e)
    }

    await this.loadingService.end()
  }
  // 點名信息
  // async initRollCall(activity_id) {
  //   try {
  //     const rollCallData: any = await this.activityService.getRollCall({ activity_id })
  //
  //
  //     this.rollCallData = rollCallData
  //   } catch (e) {
  //     console.error(e)
  //   }
  // }
  // 學生名單
  async initLottery(lottery) {
    try {
      // const lottery: any = await this.activityService.getActivityLottery(this.id)
      // 報名名單
      const actStuList = []
      for (const grade in lottery.actStu) {
        if ( typeof grade === 'string') {
          console.log(grade)
          const g = grade.split('_')
          if (g.length === 2) {
            const c = {
              class: g[0],
              list: []
            }
            c.list = lottery.actStu[grade].map(i => Object.assign(i))
            actStuList.push(c)
          }
        }
      }
      lottery.actStuList = this.getStuList(lottery.actStu)
      lottery.stuLotteryList = this.getStuList(lottery.stuLottery)
      lottery.stuNotLotteryList = this.getStuList(lottery.stuNotLotteryWithClass)
      lottery.stuNotAgreeList = this.getStuList(lottery.stuNotAgree)
      lottery.stuNotReplyList = this.getStuList(lottery.stuNotReply)


      this.lottery = lottery
    } catch (e) {
      console.error(e)
    }
  }
  getStuList(data) {
    const actStuList = []
    for (const grade in data) {
      if ( typeof grade === 'string') {
        const g = grade.split('_')
        if (g.length === 2) {
          const c = {
            class: g[0],
            list: []
          }
          c.list = data[grade].map(i => Object.assign(i))
          actStuList.push(c)
        }
      }
    }
    return actStuList
  }
  getDayStr(day, type = 'cn') {
    if (type === 'cn') {
      return dateFormat(day, 'MM月dd日')// + ' ' + wStr
    } else {
      return dateFormat(day, 'MM月dd日')// + ' ' + wStr
    }
  }


  get showDown() {
    if (this.user && typeof this.user.user_type === 'string') {
      const user_type = this.user.user_type.toUpperCase()
      return user_type === 'ADMIN' || user_type === 'TEACHER'
    }
    return false
  }

  get title() {
    if (this.data && this.data.activity) {
      return this.data.activity.actName
    }
    return this.translate.instant('ACTIVITY.DETAIL.TITLE')
  }
  get actCode() {
    if (!this.data || !this.data.activity) { return '-' }
    return this.data.activity.actCode
  }
  get actTeacher() {
    if (!this.data || !this.data.activity) { return '-' }
    return this.data.activity.actTeacher
  }
  get actType() {
    if (!this.data || !this.data.activity) { return '' }
    switch (this.data.activity.actType) {
      case '1':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T1')
      case '3':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T3')
      case '5':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T5')
      case '6':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T6')
      case '7':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T7')
      case '2':
      case '4':
      default:
        return ''
    }
  }




  /* ------------------------------------------------ */
  async onChangeStudentList() {
    const actionSheet = await this.actionSheetController.create({
      header: this.translate.instant('ACTIVITY.DETAIL.SELECT_LIST_TYPE'),
      buttons: [{
        text: this.translate.instant('ACTIVITY.LIST_TYPE.APP_STU'),
        // role: 'destructive',
        // icon: 'trash',
        handler: () => {
          this.stuListType = 'actStu'
          console.log('報名名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.LOTTERY'),
        // icon: 'share',
        handler: () => {
          this.stuListType = 'stuLottery'
          console.log('取錄的學生名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.NOT_LOTTERY'),
        // icon: 'arrow-dropright-circle',
        handler: () => {
          this.stuListType = 'stuNotLottery'
          console.log('未取錄的學生名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.NOT_AGREE'),
        // icon: 'heart',
        handler: () => {
          this.stuListType = 'stuNotAgree'
          console.log('不同意參加的學生名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.NOT_REPLY'),
        // icon: 'close',
        // role: 'cancel',
        handler: () => {
          this.stuListType = 'stuNotReply'
          console.log('未回復的學生名單');
        }
      }]
    });
    await actionSheet.present();
  }

  get stuList() {
    switch (this.stuListType) {
      case 'actStu':
        return this.lottery.actStuList
      case 'stuLottery':
        return this.lottery.stuLotteryList
      case 'stuNotLottery':
        return this.lottery.stuNotLotteryList
      case 'stuNotAgree':
        return this.lottery.stuNotAgreeList
      case 'stuNotReply':
        return this.lottery.stuNotReplyList
      default:
        return []
    }
  }

  get stuListTypeStr() {
    switch (this.stuListType) {
      case 'actStu':
        return this.translate.instant('ACTIVITY.LIST_TYPE.APP_STU')
      case 'stuLottery':
        return this.translate.instant('ACTIVITY.LIST_TYPE.LOTTERY')
      case 'stuNotLottery':
        return this.translate.instant('ACTIVITY.LIST_TYPE.NOT_LOTTERY')
      case 'stuNotAgree':
        return this.translate.instant('ACTIVITY.LIST_TYPE.NOT_AGREE')
      case 'stuNotReply':
        return this.translate.instant('ACTIVITY.LIST_TYPE.NOT_REPLY')
      default:
        return this.translate.instant('ACTIVITY.LIST_TYPE.STU')
    }
  }



  // async onDown() {
  //   try {
  //     await this.loadingService.start()
  //     const activity_id = this.id
  //     const rollCallInfo: any = await this.activityService.getRollCall({ activity_id })
  //     console.log(rollCallInfo)
  //     const activityInfo: any = await this.activityService.getActivityInfo({ id: activity_id })
  //     console.log(activityInfo)
  //     const lottery: any = await this.activityService.getActivityLottery(activity_id)
  //     console.log(lottery)
  //     // actCode: "210003"
  //     // actName: "朗讀比賽"
  //     // actEname: ""
  //     // actTeacher: "王寶音校長,陳燕貞老師"
  //     // cateCode: "P"
  //     // cateName: "保良局莊啟程小學家長教師會"
  //     // cateEnName: ""
  //     // activity_date: [{…}]
  //     await this.rollCallService.add({
  //       activity_id,
  //       actCode: rollCallInfo.actCode,
  //       actName: rollCallInfo.actName,
  //       actEname: rollCallInfo.actEname,
  //       actTeacher: rollCallInfo.actTeacher,
  //       cateCode: rollCallInfo.cateCode,
  //       cateName: rollCallInfo.cateName,
  //       cateEnName: rollCallInfo.cateEnName,
  //       activity_date: rollCallInfo.activity_date,
  //       activityInfo,
  //       lottery
  //     })
  //     await this.activityService.updateReloadStatus([activity_id])
  //
  //     this.utils.showMsg(this.translate.instant('ACTIVITY.DETAIL.DOWN_SUCCESS'))
  //
  //   } catch (e) {
  //     console.error(e)
  //   }
  //   await this.loadingService.end()
  //
  // }
  segmentChanged($event) {
    this.tab = $event.detail.value
    this.cdr.detectChanges();
  }

  async onRollCall(actKey, year, day) {
    console.log(actKey, year, day)
    const itemIndex = this.rollCallData.activity_date.findIndex(i => i.actKey === actKey)
    if (itemIndex === - 1) {
      return
    }
    const item = this.rollCallData.activity_date[itemIndex]

    this.editDay(year, day, itemIndex)

  }

  async editDay(year, day, itemIndex) {
    const data = this.rollCallData
    const activity_id = this.rollCallData.activity_id
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: RollCallOfflineInputPage,
      componentProps: {
        domId: 'edit-day-modal',
        activity_id,
        data,
        year,
        day,
        itemIndex
      },
      id: 'edit-day-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    if (res.data.save) {
      await this.loadingService.start()
      // await this.initRollCall(this.id)
      await this.loadData()
      await this.loadingService.end()
    }
    return {}
  }

  openFile(file) {
    // http://test.esaccount.com
    // /ckttest/activity
    // /web/bundles/extramain/upload/pdf
    // /216b82c8d3684ba348c54.pdf

    // const serverInfo = this.storageService.serverSetting
    // // this.serverInfo.http + '://' + this.serverInfo.ip + ':' + this.serverInfo.port + '/' +
    // // this.serverInfo.remoteProjectName + '/' + this.serverInfo.uri + '/' + this.schoolInfo.schLogo
    // let url = ''
    //
    // url = serverInfo.http + '://' + serverInfo.ip + ':' + serverInfo.port
    // url += '/' + serverInfo.remoteProjectName + '/' + serverInfo.uri
    // url += '/' + 'bundles/extramain/upload/pdf/'
    // url += file

    // this.utils.showToast('暫時無法打開文件')

    // console.log(url)
    this.utils.showMsg('離線點名狀態，無法打開文件！')
  }

  getStuStatusClass(stu) {
    switch (this.stuListType) {
      case 'actStu':
        return stu.actAgree === 'Y' ? 'agree' : ''
      case 'stuLottery':
        return 'agree'
      case 'stuNotLottery':
        return ''
      case 'stuNotAgree':
        return 'not-agree'
      case 'stuNotReply':
        return ''
      default:
        return []
    }
  }
}
