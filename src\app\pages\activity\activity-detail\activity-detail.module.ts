import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { ActivityDetailPage } from './activity-detail.page';
// import { InfoLabelComponent } from '@app/components/info-label/info-label.component';
import { RollCallOnlinePage } from '@pages/activity/roll-call-online/roll-call-online.page';
import {TranslateModule} from '@ngx-translate/core';
import { InfoLabelModule } from '@app/components/info-label/info-label.module';

const routes: Routes = [
  {
    path: '',
    component: ActivityDetailPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    TranslateModule,
    InfoLabelModule
    
  ],
  declarations: [
    ActivityDetailPage,
    // InfoLabelComponent,
    RollCallOnlinePage
  ],
  entryComponents: [
    RollCallOnlinePage
  ]
})
export class ActivityDetailPageModule {}
