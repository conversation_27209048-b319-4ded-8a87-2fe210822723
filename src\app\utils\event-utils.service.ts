import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class EventUtilsService {

  public statusOptions = [
    { value: 'P', label: '待通過' },
    { value: 'C', label: '待確認' },
    { value: 'A', label: '待安排' },
    { value: 'S', label: '待開始' },
    { value: 'H', label: '進行中' },
    { value: 'T', label: '待測試' },
    { value: 'D', label: '已完成' },
    { value: 'E', label: '已關閉' },
  ]
  public statusList = {
    P: '待通過',
    C: '待確認',
    A: '待安排',
    S: '待開始',
    H: '進行中',
    T: '待測試',
    D: '已完成',
    E: '已關閉',
  }
  public devProcessList = {
    D: '開發中',
    P: '已交給客戶',
  }
  public statusClassList = {
    X: 'normal',
    C: 'normal',
    A: 'normal',
    S: 'normal',
    H: 'warning',
    T: 'normal',
    D: 'success',
    E: 'error',
  }
  public urgentList = {
    H: '緊急',
    L: '普通',
    E: '優化',
  }
  constructor() { }

  getUrgent(v) {
    const text = this.urgentList[v]
    return text ? text : '-'
  }
  getStatus(v) {
    const text = this.statusList[v]
    return text ? text : '-'
  }
  getDevProcess(v) {
    const text = this.devProcessList[v]
    return text ? text : '-'
  }
  getStatusClass(v) {
    const text = this.statusClassList[v]
    return text ? text : '-'
  }
}
