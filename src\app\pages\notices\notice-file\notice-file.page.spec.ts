import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { NoticeFilePage } from './notice-file.page';

describe('NoticeFilePage', () => {
  let component: NoticeFilePage;
  let fixture: ComponentFixture<NoticeFilePage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ NoticeFilePage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(NoticeFilePage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
