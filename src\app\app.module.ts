import {NgModule} from '@angular/core'
import {BrowserModule} from '@angular/platform-browser'
import {RouteReuseStrategy} from '@angular/router'
import {FormsModule} from '@angular/forms';
import {IonicModule, IonicRouteStrategy} from '@ionic/angular'
import {SplashScreen} from '@ionic-native/splash-screen/ngx'
// 系統狀態欄
import {StatusBar} from '@ionic-native/status-bar/ngx'
import {AppRoutingModule} from './app-routing.module'
import {AppComponent} from './app.component'
// 掃碼
import {QRScanner} from '@ionic-native/qr-scanner/ngx'
// 振动
import {Vibration} from '@ionic-native/vibration/ngx';
// 圖片預覽
// import { PhotoViewer } from '@ionic-native/photo-viewer/ngx';
import {PreviewAnyFile} from '@ionic-native/preview-any-file/ngx';
// 日曆
import {CalendarModule} from 'ion2-calendar';
// 國際化
import {TranslateModule, TranslateLoader, TranslatePipe} from '@ngx-translate/core';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import {HttpClient, HttpClientModule} from '@angular/common/http';
// 數據庫
import {SQLite, SQLiteObject} from '@ionic-native/sqlite/ngx'
// 設備
import {Device} from '@ionic-native/device/ngx';
import { AndroidNotch } from '@awesome-cordova-plugins/android-notch/ngx';
// 推送
import { AngularFireModule } from '@angular/fire/compat';
// @ts-ignore
import { FirebaseX } from '@ionic-native/firebase-x/ngx';
import { LocalNotifications } from '@awesome-cordova-plugins/local-notifications/ngx';

import { environment } from '../environments/environment';

export function HttpLoaderFactory(httpClient: HttpClient) {
    return new TranslateHttpLoader(httpClient, '../assets/i18n/', '.json');
}

// 網絡
import {Network} from '@ionic-native/network/ngx';
//
import {StorageService} from './services/storage/storage.service'
import {RequestService} from './services/request/request.service'
import {StoreService} from './services/store/store.service'
import {AuthService} from './services/auth/auth.service'
import {LoginService} from './services/api/login.service'

import {HTTP} from '@ionic-native/http/ngx'
import {NativeStorage} from '@ionic-native/native-storage/ngx'
import {IsDebug} from '@ionic-native/is-debug/ngx';


// Google
import {GooglePlus} from '@ionic-native/google-plus/ngx';
// 動畫
// 系統導航欄
// import { NavigationBarColor } from 'ionic-plugin-navigation-bar-color';
// import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
// import { LyThemeModule, LY_THEME } from '@alyle/ui';
// import { MinimaLight } from '@alyle/ui/themes/minima';


import {TextViewPage} from '@pages/events/text-view/text-view.page';

import {SelectClassPage} from '@pages/components/select-class/select-class.page';
import {SelectStudentPage} from '@pages/components/select-student/select-student.page';
import {ActivitySelectClassPage} from '@pages/components/activity-select-class/activity-select-class.page';
import {ActivitySelectStudentPage} from '@pages/components/activity-select-student/activity-select-student.page';
import {DateSelectPage} from '@pages/components/date-select/date-select.page';
import {VenueSelectPage} from '@pages/components/venue-select/venue-select.page';
import {SessionSelectPage} from '@pages/components/session-select/session-select.page';
import {ResourcesSelectPage} from '@pages/components/resources-select/resources-select.page';
import {UserPopoverModule} from '@app/components/user-popover/user-popover.module';
import {UserListPopoverModule} from '@app/components/user-list-popover/user-list-popover.module';
import {ActivitySignUpInputPage} from '@pages/activity/activity-sign-up-input/activity-sign-up-input.page';
import {StudentStarInputPage} from '@pages/tab-student/student-star-input/student-star-input.page';
import {File} from '@ionic-native/file/ngx';
import {FileOpener} from '@ionic-native/file-opener/ngx';
import {InfoLabelModule} from '@app/components/info-label/info-label.module';
import {StudentStarActivitySearchPage} from '@pages/tab-student/student-star-activity-search/student-star-activity-search.page';
// import {StudentStarDetailPage} from '@pages/tab-student/student-star-detail/student-star-detail.page';

// import { iosTransitionAnimation } from '@ionic/core/dist/collection/utils/transition/ios.transition.js';
@NgModule({
    declarations: [
        AppComponent,
        TextViewPage,
        SelectClassPage,
        SelectStudentPage,
        ActivitySelectClassPage,
        ActivitySelectStudentPage,
        DateSelectPage,
        VenueSelectPage,
        SessionSelectPage,
        ResourcesSelectPage,
        ActivitySignUpInputPage,
        StudentStarInputPage, /* 摘星輸入 */
        StudentStarActivitySearchPage, /* 摘星輸入 - 活動查詢 */
        // StudentStarDetailPage,
    ],
    entryComponents: [
        TextViewPage,
        SelectClassPage,
        SelectStudentPage,
        ActivitySelectClassPage, /* 活動班級 */
        ActivitySelectStudentPage, /* 活動班級學生 */
        DateSelectPage, /* 日期選擇 */
        VenueSelectPage, /* 日期選擇 */
        SessionSelectPage, /* 日期選擇 */
        ResourcesSelectPage, /* 日期選擇 */
        ActivitySignUpInputPage, /* 日期選擇 */
        StudentStarInputPage, /* 摘星輸入 */
        StudentStarActivitySearchPage, /* 摘星輸入 - 活動查詢 */
        // StudentStarDetailPage,
    ],
    imports: [
        AngularFireModule.initializeApp(environment.firebaseConfig),
        BrowserModule,
        HttpClientModule,
        TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: HttpLoaderFactory,
                deps: [HttpClient]
            }
        }),
        FormsModule,
        IonicModule.forRoot({
            backButtonText: '',
            // mode: 'ios',
            // navAnimation: iosTransitionAnimation,
            //   navAnimation: (
            //     AnimationC: Animation,
            //     baseEl: any,
            //     position?: any): Promise<Animation> => {
            //     const baseAnimation = new AnimationC();
            //     const hostEl = (baseEl.host || baseEl) as HTMLElement;
            //
            //     const wrapperAnimation = new AnimationC();
            //     const wrapperAnimation2 = new AnimationC();
            //     if (position.direction === 'forward') {
            //       wrapperAnimation.addElement(position.enteringEl);
            //       wrapperAnimation.fromTo('transform', `translateX(100%)`, 'translateX(0px)');
            //       wrapperAnimation.fromTo('opacity', 0.3, 1);
            //
            //       wrapperAnimation2.addElement(position.leavingEl);
            //       wrapperAnimation2.fromTo('transform', `translateX(0)`, 'translateX(-50%)');
            //       wrapperAnimation2.fromTo('opacity', 1, 0.5);
            //     }
            //
            //     if (position.direction === 'back') {
            //       wrapperAnimation.addElement(position.leavingEl);
            //       wrapperAnimation.fromTo('transform', `translateX(0)`, 'translateX(100%)');
            //       wrapperAnimation.fromTo('opacity', 1, 0);
            //
            //       wrapperAnimation2.addElement(position.enteringEl);
            //       wrapperAnimation2.fromTo('transform', `translateX(-90%)`, 'translateX(0)');
            //       wrapperAnimation2.fromTo('opacity', 0.5, 1);
            //     }
            //     return Promise.resolve(baseAnimation
            //       .addElement(hostEl)
            //       .easing('cubic-bezier(.36,.66,.04,1)')
            //       .duration(600)
            //       .add(wrapperAnimation)
            //       .add(wrapperAnimation2));
            //   }
        }),
        AppRoutingModule,
        CalendarModule,
        UserPopoverModule,
        UserListPopoverModule,
        InfoLabelModule,
        // BrowserAnimationsModule, // 動畫
        // LyThemeModule.setTheme('minima-light')
    ],
    providers: [
        StatusBar,
        // NavigationBarColor,
        SplashScreen,

        StorageService,
        RequestService,
        StoreService,
        AuthService,
        LoginService,
        HTTP,
        NativeStorage,
        QRScanner, // 掃碼
        IsDebug,
        Vibration, // 振动
        // PhotoViewer, // 圖片預覽
        PreviewAnyFile, // 文件預覽
        SQLite, // SQL
        GooglePlus, // GooglePlus
        Network, // GooglePlus
        File, // 文件
        FileOpener, // 文件
        Device, // 文件
        AndroidNotch, // 屏幕
        FirebaseX, // 推送
        LocalNotifications, // 本地推送

        {provide: RouteReuseStrategy, useClass: IonicRouteStrategy},
        // { provide: LY_THEME, useClass: MinimaLight, multi: true }
    ],
    bootstrap:
        [AppComponent],
    exports: []
})

export class AppModule {
}
