import {Injectable} from '@angular/core'
import {AlertController} from '@ionic/angular'
import Cookies from 'js-cookie'
import {StorageService} from '../storage/storage.service'
import {AuthService} from '../auth/auth.service'
import {LoginService} from '../api/login.service'
import {UserService} from '../api/user.service'
import {NativeStorage} from '@ionic-native/native-storage/ngx'
import md5 from 'md5'
import {Router} from '@angular/router';
import {LocationStrategy} from '@angular/common';
import {SystemMessageService} from '@services/api/system-message.service';
import {GooglePlus} from '@ionic-native/google-plus/ngx';
import {PushService} from '@services/api/push.service';
import {UserListService} from '@services/sql/userList.service';
import {JpushService} from '@services/jpush/jpush.service';
import {sleep} from '@app/utils';

@Injectable({
  providedIn: 'root'
})
export class StoreService {

  private msgStatusLoading = false

  constructor(
    private router: Router,
    private location: LocationStrategy,
    private alertController: AlertController,
    private storageService: StorageService,
    private loginService: LoginService,
    private authService: AuthService,
    private userService: UserService,
    private nativeStorage: NativeStorage,
    private systemMessageService: SystemMessageService,
    private google: GooglePlus,
    private pushService: PushService,
    private userListService: UserListService,
    private jpushPlugin: JpushService,
  ) {
    //
  }

  async sleep(time) {
    await setTimeout(() => {
    }, time)
    return true
  }

  async GetStore() {
      return this.storageService.get()
  }

  Login(userInfo: any) {

    const password = userInfo.password
    const username = userInfo.username.trim()

    let user_id = ''
    const school_year_id = ''
    return new Promise((resolve, reject) => {
      this.jpushPlugin.clearBadge()
      // 登錄
      this.loginService.login(username, password)
        .then(async (data: any) => {
          // if (response && response.code === 200) {
          //   const data: any = response.data
          //   this.authService.setToken(data.token)

            this.nativeStorage.setItem('username', username)
            this.nativeStorage.setItem('password', userInfo.password)

            data.username = username
            this.nativeStorage.setItem('userInfo', Object.assign({}, data))
            try {
              // let set = false
              if (data && data.user_id) { // 設置消息推送ID
                try {
                  user_id = data.user_id
                  console.log('user_id', user_id)
                  this.jpushPlugin.getId().then((registration_id: any) => {
                    console.log('registration_id', registration_id)
                    this.pushService.updatePush({user_id, registration_id}).then(res => {
                      console.log('updatePush', JSON.stringify(res))
                    })
                  })
                  // if (res && res.alias) {
                  //   set = true
                  //   // await this.jpushPlugin.setAlias(res.alias)
                  //   // this.jpushPlugin.initJpush()
                  //   console.error('設置推送ID：', res.alias)
                  // }
                } catch (e) {
                  console.error('註冊推送失敗')
                  console.error('註冊推送失敗', e)
                  console.error('註冊推送失敗', JSON.stringify(e))
                }
              }
              // if (!set) {
              //   console.error('註冊推送失敗，清空Alias')
              //   // await this.jpushPlugin.setAlias('')
              //   // this.jpushPlugin.deleteAlias()
              // }
            } catch (e) {
              console.error('推送初始化失敗', e)
            }
            // this.nativeStorage.setItem('permissions', {})
            // return this.userService.getUserInfoByToken()
            resolve(data)
        })
        // 獲取用戶權限
        // .then((data: any) => {
        //   if (data && data.user_id) { // 設置消息推送ID
        //     // this.jpushPlugin.setAlias(data.user_id)
        //     // this.jpushPlugin.initJpush()
        //   }
        //   this.nativeStorage.setItem('userInfo', Object.assign({}, data))
        //   this.nativeStorage.setItem('permissions', {})
        //   // return this.userService.getPermissionsForApp({ user_id, have_selection: '1', independent_system: '1'})
        //   resolve(data)
        // })
        // .then((data: any) => {
        //     this.nativeStorage.setItem('userInfo', Object.assign({}, data))
        //     resolve(data)
        // })
        .catch(error => {
          reject(error)
        })
    })
  }

  Logout() {
    return new Promise(async (resolve) => {

      try {
        console.log('onLogout 3')
        this.jpushPlugin.clearBadge()
        const user: any = await this.nativeStorage.getItem('userInfo')
        const user_id = user.user_id
        await this.userListService.deleteByUsername(user.username)
        const registration_id: any = await this.jpushPlugin.getId()
        const delRes = await this.pushService.deletePush({ user_id, registration_id })
        console.log(JSON.stringify(delRes))
        // await this.jpushPlugin.deleteAlias()
        // this.jpushPlugin.resetBadge() // 清除角標
        // await sleep(5000)
        console.log('退出推送成功')
      } catch (e) {
        console.log('退出推送失敗')
      }
      console.log('onLogout 4')


      this.storageService.set('userInfo', '')
      this.storageService.set('password', '')
      this.nativeStorage.setItem('password', '')
      this.authService.removeToken()
      this.nativeStorage.setItem('userInfo', {
        userId: '',
        ipAddress: '',
        clientIp: '',
        loginRole: ''
      })
      try {
        // await this.jpushPlugin.deleteAlias()
      } catch (e) {
        console.error(e)
      }
      try {
        await this.google.logout()
      } catch (e) {
        console.error(e)
      }
      window.location.href = this.location.getBaseHref()
      // this.location.replaceState('/', '', '', '')
      this.router.navigate(['login'], )
      resolve(true)
    })
  }

  LoginForToken(userInfo: any) {
    const password = userInfo.password
    const username = userInfo.username.trim()

    return new Promise((resolve, reject) => {
      // 登錄
      this.loginService.loginForToken(username, md5(password))
          .then(async (data: any) => {
            if (data && data.token !== undefined) {
              this.authService.setToken(data.token)
              // 設置 stu_id
              const user = await this.nativeStorage.getItem('userInfo')
              user.stu_id = data.stu_id
              user.default_semester = data.default_semester !== undefined && data.default_semester != null ? data.default_semester : 1
              this.nativeStorage.setItem('userInfo', user)

              resolve(data)
            }
          })
          .catch(error => {
            reject(error)
          })
    })
  }

  async getMsgStatus() {
    if (this.msgStatusLoading) { return }
    this.msgStatusLoading = true
    try {
      const res: any = await this.systemMessageService.fetchSystemMessages({
        read: 0,
        page_index: 1,
        page_size: 1,
      })
      await this.nativeStorage.setItem('hasMsg', res.total_count > 0)
    } catch (e) {
      console.error(e)
    }
    this.msgStatusLoading = false
  }
}
