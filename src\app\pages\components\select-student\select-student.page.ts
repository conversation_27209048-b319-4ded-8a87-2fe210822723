import {Component, Input, OnInit} from '@angular/core';
import {MasterService} from '@services/api/master.service';
import {LoadingService} from '@app/utils/loading.service';
import {ModalController} from '@ionic/angular';
import {NativeStorage} from '@ionic-native/native-storage/ngx';

@Component({
  selector: 'app-select-student',
  templateUrl: './select-student.page.html',
  styleUrls: ['./select-student.page.scss'],
})
export class SelectStudentPage implements OnInit {
  @Input() domId = 'select-student-page'
  @Input() classId: any;

  public class = ''
  public student = ''
  public students: any = []
  constructor(
    private modalCtrl: ModalController,
    private nativeStorage: NativeStorage,
    private master: MasterService,
    private loadingService: LoadingService,
    ) { }

  ngOnInit() {
    this.initData()
  }

  async initData() {
    if (!this.classId) { return }
    await this.loadingService.start()
    try {
      const res: any = await this.master.fetchClassStudent({ id: this.classId })
      console.log(res)
      this.students = res.datas
    } catch (e) {
      console.error(e)
    }
    await this.loadingService.end()
  }


  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true
    }, undefined, this.domId);
  }
  onSave(item) {
    this.modalCtrl.dismiss({
      save: true,
      student: item,
    }, undefined, this.domId);
  }

}
