import { Injectable, OnInit } from '@angular/core';
import {LoadingController, NavController, Platform} from '@ionic/angular'

@Injectable({
  providedIn: 'root'
})
export class LoadingService {

  public el: any
  public id =  'loading-service'
  public loading = false
  constructor(
    private loadingController: LoadingController,
    private platform: Platform,
    private navCtrl: NavController,
    ) {

    // this.platform.ready().then(() => {
    //   // this.platform.backButton.subscribeWithPriority(9999, () => {
    //     document.addEventListener('backbutton', (event) => {
    //       if (this.loading) {
    //         event.preventDefault();
    //         event.stopPropagation();
    //         console.log('屏蔽返回');
    //       } else {
    //         this.navCtrl.pop()
    //       }
    //     }, false);
    //   // });
    // });

  }

  async start() {
    if (this.isExist()) { return }
    this.loading = true
    this.el = await this.loadingController.create({
      message: '加載中...',
      // duration: 2000
      id: this.id
    });
    await this.el.present()
  }
  async end() {
    if (this.isExist()) {
      await this.el.dismiss()
      window.document.querySelectorAll('#loading-service').forEach(l => l.remove())
      this.loading = false
    }
  }
  isExist() {
    const dom = window.document.getElementById(this.id)
    return !!dom && this.el && this.el.dismiss
  }
}
