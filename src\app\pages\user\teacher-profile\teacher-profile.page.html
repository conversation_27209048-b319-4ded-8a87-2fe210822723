<ion-header class="center">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button ></ion-back-button>
    </ion-buttons>
    <ion-title>個人資料</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="onSave()">
        保存
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list>
    <ion-item-group>
      <ion-item>
        <ion-label position="floating">賬號</ion-label>
        <ion-input [(ngModel)]="form.username" [disabled]="true" readonly type="text"></ion-input>
      </ion-item>
      <!--<ion-item>-->
      <!--  <ion-label position="floating">教師姓名</ion-label>-->
      <!--  <ion-input [(ngModel)]="form.stuUname" readonly type="text"></ion-input>-->
      <!--</ion-item>-->
    </ion-item-group>
    <ion-item-divider style="padding-top: 30px;">
      <ion-label>
        重設密碼
      </ion-label>
    </ion-item-divider>
    <ion-item-group>
      <ion-item>
        <ion-label position="floating">新密碼 <ion-text *ngIf="!!form.pass" color="danger">*</ion-text></ion-label>
        <ion-input [(ngModel)]="form.pass" [required]="!!form.pass" type="password"></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="floating">重入新密碼 <ion-text *ngIf="!!form.pass" color="danger">*</ion-text></ion-label>
        <ion-input [(ngModel)]="form.passConfirm" [required]="!!form.pass" type="password"></ion-input>
      </ion-item>
    </ion-item-group>
  </ion-list>

</ion-content>
