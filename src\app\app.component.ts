import {Component} from '@angular/core';

import {Platform, ToastController} from '@ionic/angular';
import {SplashScreen} from '@ionic-native/splash-screen/ngx';
import {StatusBar} from '@ionic-native/status-bar/ngx';
import {AndroidNotch} from '@awesome-cordova-plugins/android-notch/ngx';


import {StoreService} from './services/store/store.service';
import {JpushService} from './services/jpush/jpush.service';
import {TranslateService} from '@ngx-translate/core';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {SqlService} from '@services/sql/sql.service';
// import { FirebaseX } from '@awesome-cordova-plugins/firebase-x/ngx';
import {FirebaseX} from '@ionic-native/firebase-x/ngx';

@Component({
    selector: 'app-root',
    templateUrl: 'app.component.html',
    styleUrls: ['app.component.scss'],
})
export class AppComponent {
    private backButtonPressed = false
    private subscription: any

    constructor(private platform: Platform,
                private splashScreen: SplashScreen,
                private statusBar: StatusBar,
                private androidNotch: AndroidNotch,
                private storeService: StoreService,
                private toastCtrl: ToastController,
                public translate: TranslateService,
                private nativeStorage: NativeStorage,
                private sql: SqlService,
                private firebaseX: FirebaseX,
                private jpush: JpushService,
    ) {
        this.initializeApp();

// Call this on resize or orientationchange, but *after* the deviceready event
    }

    // tslint:disable-next-line:use-lifecycle-interface
    ngAfterContentInit() {
        this.platform.ready()
            .then(async () => {
                if (this.platform.is('android')) {
                    this.detectInsets()
                }
                this.jpush.initJpush()
                // this.jpush.jPushAddEventListener()

                // const initAndroid = () => {
                //
                //     // Define custom  channel - all keys are except 'id' are optional.
                //     const customChannel = {
                //         // channel ID - must be unique per app package
                //         id: 'my_channel_id',
                //
                //         // Channel name. Default: empty string
                //         name: 'My channel name',
                //
                //         // The sound to play once a push comes. Default value: 'default'
                //         // Values allowed:
                //         // 'default' - plays the default notification sound
                //         // 'ringtone' - plays the currently set ringtone
                //         // filename - the filename of the sound file located in '/res/raw' without file extension (mysound.mp3 -> mysound)
                //         sound: 'blackberry',
                //
                //         // Vibrate on new notification. Default value: true
                //         // Possible values:
                //         // Boolean - vibrate or not
                //         // Array - vibration pattern - e.g. [500, 200, 500] - milliseconds vibrate, milliseconds pause, vibrate, pause, etc.
                //         vibration: [300, 200, 300],
                //
                //         // Whether to blink the LED
                //         light: true,
                //
                //         // LED color in ARGB format - this example BLUE color. If set to -1, light color will be default. Default value: -1.
                //         lightColor: '0xFF0000FF',
                //
                //         // Importance - integer from 0 to 4. Default value: 3
                //         // 0 - none - no sound, does not show in the shade
                //         // 1 - min - no sound, only shows in the shade, below the fold
                //         // 2 - low - no sound, shows in the shade, and potentially in the status bar
                //         // 3 - default - shows everywhere, makes noise, but does not visually intrude
                //         // 4 - high - shows everywhere, makes noise and peeks
                //         importance: 4,
                //
                //         // Show badge over app icon when non handled pushes are present. Default value: true
                //         badge: true,
                //
                //         // Show message on locked screen. Default value: 1
                //         // Possible values (default 1):
                //         // -1 - secret - Do not reveal any part of the notification on a secure lockscreen.
                //         // 0 - private - Show the notification on all lockscreens, but conceal sensitive or private information on secure lockscreens.
                //         // 1 - public - Show the notification in its entirety on all lockscreens.
                //         visibility: 1
                //     };
                //
                //     // @ts-ignore
                //     this.firebaseX.createChannel(
                //         customChannel,
                //         // @ts-ignore
                //         () => {
                //             console.log('Created custom channel: ' + customChannel.id);
                //             this.firebaseX.listChannels(
                //                 // @ts-ignore
                //                 (channels) => {
                //                     // tslint:disable-next-line:triple-equals
                //                     if (typeof channels == 'undefined') {
                //                         return;
                //                     }
                //                     // tslint:disable-next-line:prefer-for-of
                //                     for (let i = 0; i < channels.length; i++) {
                //                         console.log('Channel id=' + channels[i].id + '; name=' + channels[i].name);
                //                     }
                //                 },
                //                 (error) => {
                //                     console.log('List channels error: ' + error);
                //                 }
                //             );
                //         },
                //         (error) => {
                //             console.log('Create channel error', error);
                //         });
                // };
                // initAndroid()
                // this.firebaseX.hasPermission().then((hasPermission) => {
                //     console.log('Permission is ' + (hasPermission ? 'granted' : 'denied'));
                // });
                // console.log('this.firebaseX', this.firebaseX)

                // this.firebaseX.getToken()
                //     .then(token => {
                //         console.log(`[firebaseX] -- The token is ${token}`)
                //         alert(`[firebaseX] -- The token is ${token}`)
                //     }) // save the token server-side and use it to push notifications to this device
                //     .catch(error => {
                //         console.log('[firebaseX] -- Error getting token' + error.toString())
                //         alert('[firebaseX] -- Error getting 12token' + error.toString())
                //     });
                //
                // this.firebaseX.onMessageReceived()
                //     .subscribe(data => {
                //         console.log(`[firebaseX] -- User opened a notification `, data)
                //     });
                //
                // this.firebaseX.onTokenRefresh()
                //     .subscribe((token: string) => {
                //         console.log(`[firebaseX] -- Got a new token ${token}`)
                //     });
                // this.firebaseX.registerAuthStateChangeListener((userSignedIn) => {
                //     console.log('Auth state changed: User signed ' + (userSignedIn ? 'in' : 'out'));
                // });

                // this.firebaseX.signInUserAnonymously().then(() => {
                //     console.log('匿名登錄成功')
                //
                // }).catch(err => {
                //     console.log('匿名登錄失敗', err)
                // })

                // this.firebaseX.getId().then((fcmToken) => {
                //     console.log('[firebaseX] -- The ID is ' + fcmToken);
                // }).catch((error) => {
                //     console.error('[firebaseX] -- Error getting token', error);
                // });
            })
    }

    ionViewWillEnter() {
        this.jpush.clearBadge()
        this.detectInsets()
    }

    detectInsets() {
        console.log('detectInsets', 1)
        if (this.androidNotch) {
            this.statusBar.overlaysWebView(false);
            console.log('detectInsets', 2)
            const style = document.documentElement.style;
            console.log('detectInsets', 3, style)
            console.log('detectInsets', 4, this.androidNotch)
            this.androidNotch.getInsetTop().then((px: any) => {
                style.setProperty('--notch-inset-top', px + 'px');
                console.log('detectInsets', 4, px)
            }, (err) => console.error('Failed to get insets top:', err));

            // @ts-ignore
            this.androidNotch.getInsetRight().then((px: any) => {
                style.setProperty('--notch-inset-right', px + 'px');
            }, (err) => console.error('Failed to get insets right:', err));

            // @ts-ignore
            this.androidNotch.getInsetBottom().then((px: any) => {
                style.setProperty('--notch-inset-bottom', px + 'px');
            }, (err) => console.error('Failed to get insets bottom:', err));

            // @ts-ignore
            this.androidNotch.getInsetLeft().then((px: any) => {
                style.setProperty('--notch-inset-left', px + 'px');
            }, (err) => console.error('Failed to get insets left:', err));


            // const softButton = (screen.height - this.platform.height());
            // console.log('detectInsets', 5, softButton)
            // const body: any = document.querySelectorAll('body');
            // body.forEach((e: any) => {
            //     e.classList.add('soft-button');
            //     e.style = '--ion-soft-button:' + 0 + 'px';
            // });
        }
    }

    initializeApp() {
        this.platform.ready()
            .then(async () => {

                this.initTranslate()

                this.statusBar.styleDefault();
                this.splashScreen.hide();

                const user: any = await this.nativeStorage.getItem('userInfo')

                this.sql.init()
            });
    }

    async initTranslate() {
        this.translate.addLangs(['en', 'cn']);
        this.translate.setDefaultLang('cn');
        let lang = 'cn'
        try {
            const nl = await this.nativeStorage.getItem('lang')
            if (nl === 'cn' || nl === 'en') {
                lang = nl
            } else {
                const browserLang = this.translate.getBrowserLang();
                this.translate.use(browserLang.match(/en|cn/) ? browserLang : 'cn');
                return
            }
        } catch (e) {
            console.error(e)
        }
        this.translate.use(lang);
    }

    // async handleExitSuccess() {
    //   const toast = await this.toastCtrl.create({
    //     message: '再按一次退出应用',
    //     duration: 2000,
    //     // position: 'middle'
    //     position: 'bottom'
    //   });
    //   toast.present();
    // }
}
