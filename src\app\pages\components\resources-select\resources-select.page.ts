import {Component, Input, OnInit} from '@angular/core';
import {ModalController} from '@ionic/angular';
import {EBookingService} from '@services/api/ebooking.service';
import {LoadingService} from '@app/utils/loading.service';
import {UtilsService} from '@app/utils/utils.service';

@Component({
  selector: 'app-resources-select',
  templateUrl: './resources-select.page.html',
  styleUrls: ['./resources-select.page.scss'],
})
export class ResourcesSelectPage implements OnInit {

  @Input() date: any = ''
  @Input() natureId: any = ''
  @Input() ruleId: any = ''
  @Input() lessonIds: any = ''
  @Input() venueIds: any = ''
  @Input() resource: any = ''
  public list: any = []
  public idList: any = []
  constructor(
    private modalCtrl: ModalController,
    private ebooking: EBookingService,
    private loadingService: LoadingService,
    private utils: UtilsService,
    ) { }

  ngOnInit() {
    if (typeof this.venueIds === 'string' && this.venueIds.length > 0) {
      this.idList = this.venueIds.split(',')
    }
    this.loadData()
  }

  async loadData() {
    await this.loadingService.start()
    try {
      const date = this.date
      const natureId = this.natureId
      const ruleId = this.ruleId
      const lessonIds = this.lessonIds
      const res: any = await this.ebooking.getResources({
        date,
        natureId,
        ruleId,
        resourceType: '2',
        lessonIds
      })
      console.log(res)

      const list = res.resources
      list.forEach(item => {
        const r = this.resource.find(i => i.id === item.id)
        if (r) {
          item.check = true
          item.selectQty = r.qty
        } else {
          item.check = false
          item.selectQty = ''
        }
        item.num = Number(item.num)
      })
      this.list = res.resources
    } catch (e) {
      console.error(e)
    }
    await this.loadingService.end()
  }

  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true
    });
  }
  onSave() {
    if (!this.list.every(item => {
      const s = Number(item.selectQty)
      const q = Number(item.num)
      return s <= q
    })) {
      this.utils.showMsg('資源選擇數量不能大於資源總數')
      return
    }


    const data = this.list.filter(i => i.check)

    if (!data.every(i => i.selectQty)) {
      this.utils.showToast({ msg: '請填寫資源數量' })
      return
    }
    this.modalCtrl.dismiss({
      dismissed: true,
      save: true,
      data
    });
  }

  onChangeNum(event, item) {
    console.log(event)
    let max = 99999
    let min = 0
    const ele = event.target
    let value = 0
    if (ele.max) {
      max = Number(ele.max)
    }
    if (ele.min) {
      min = Number(ele.min)
    }
    if (ele.value) {
      value = Number(ele.value)
    }
    if (value > 0) {
      item.check = true
    }
    if (value < min) {
      ele.value = min
      item.selectQty = min
      return
    }
    if (value > max) {
      ele.value = max
      item.selectQty = max
      return;
    }
  }

}
