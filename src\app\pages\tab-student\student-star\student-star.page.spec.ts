import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StudentStarPage } from './student-star.page';

describe('StudentStarPage', () => {
  let component: StudentStarPage;
  let fixture: ComponentFixture<StudentStarPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ StudentStarPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StudentStarPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
