<ion-header class="center">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button [disabled]="false" (click)="reset()">
        重置
      </ion-button>
    </ion-buttons>
    <ion-title>預約</ion-title>
    <ion-buttons slot="end">
      <ion-button [disabled]="false" (click)="onAdd()">
        提交
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="form">
    <div class="form-item sch-year">
      <div class="label">學年：</div>
      <!--<ion-input [(ngModel)]="form.schYear" class="input sch-year"></ion-input>-->

      <ion-select
          [(ngModel)]="form.schYear"
          placeholder="學年"
          okText="確認"
          cancelText="取消"
          interface="popover"
          class="input sch-year"
          (ngModelChange)="onChangeYear()">
        <ion-select-option *ngFor="let item of years" [value]="item.id">{{ item.year_name }}</ion-select-option>
        <!--<ion-select-option class="empty" [disabled]="true" value="" *ngIf="years.length === 0">學年</ion-select-option>-->
      </ion-select>
    </div>
    <div class="form-item character">
      <div class="label">性質：</div>
      <!--<ion-input [(ngModel)]="form.character" readonly class="input character" (click)="onCharacterClick()">-->
      <!--  <ion-icon icon="arrow-forward" mode="ios"></ion-icon>-->
      <!--</ion-input>-->
      <ion-select
          [(ngModel)]="form.natureId"
          placeholder="性質"
          okText="確認"
          cancelText="取消"
          interface="popover"
          class="input character"
          (ngModelChange)="onChangeNature()">
        <ion-select-option *ngFor="let item of natures" [value]="item.natureId">{{ item.name }}</ion-select-option>
        <!--<ion-select-option class="empty" [disabled]="true" value="" *ngIf="natures.length === 0">性質</ion-select-option>-->
      </ion-select>
    </div>
    <div class="form-item rule">
      <div class="label">規則：</div>
      <!--<ion-input [(ngModel)]="form.rule" readonly class="input rule" [placeholder]="rulePlaceHolder" (click)="onRuleClick()">-->
      <!--  <ion-icon icon="arrow-forward" mode="ios"></ion-icon>-->
      <!--</ion-input>-->
      <ion-select
          [(ngModel)]="form.ruleId"
          placeholder="規則"
          okText="確認"
          cancelText="取消"
          interface="popover"
          class="input character"
          (ngModelChange)="onChangeRule()">
        <ion-select-option class="empty" [disabled]="true" value="-" *ngIf="rules.length === 0">請選擇學年和規則</ion-select-option>
        <ion-select-option *ngFor="let item of rules" [value]="item.id">{{ item.name }}</ion-select-option>
      </ion-select>
    </div>
    <div class="form-item name">
      <div class="label">活動名稱：</div>
      <ion-input [(ngModel)]="form.name" class="input name" placeholder="請輸入名稱">
      </ion-input>
    </div>
    <div class="form-item stu-num">
      <div class="label">參與人數：</div>
      <ion-input [(ngModel)]="form.num" type="number" class="input stu-num"></ion-input>
    </div>
    <div class="sub-box">
      <div class="sub-item" *ngFor="let item of form.subFormList; let i = index">
        <div class="sub-form-item">
          <div class="label">日期：</div>
          <ion-input [(ngModel)]="item.date" readonly class="input rule" placeholder="活動日期" (click)="onSelectDate(item)">
            <ion-icon icon="arrow-forward" mode="ios"></ion-icon>
          </ion-input>
        </div>
        <div class="sub-form-item">
          <div class="label">選擇課節：</div>
          <ion-input [(ngModel)]="item.lessonIds_name" readonly class="input rule" placeholder="課節" (click)="onSelectLessons(item)">
            <ion-icon icon="arrow-forward" mode="ios"></ion-icon>
          </ion-input>
        </div>
        <div class="sub-form-item">
          <div class="label">選擇場地：</div>
          <ion-input [(ngModel)]="item.venue_name" readonly class="input rule" placeholder="活動場地" (click)="onSelectVenue(item)">
            <ion-icon icon="arrow-forward" mode="ios"></ion-icon>
          </ion-input>
        </div>
        <div class="sub-form-item">
          <div class="label">選擇資源：</div>
          <ion-input [(ngModel)]="item.resource_name" readonly class="input rule" placeholder="活動資源" (click)="onSelectResources(item)">
            <ion-icon icon="arrow-forward" mode="ios"></ion-icon>
          </ion-input>
        </div>
        <div *ngIf="i > 0" class="sub-form-item" style="display: block;text-align: right;padding: 0 0.5rem;">
          <ion-button (click)="onDeleteSub(item, i)" size="small" color="danger" shape="round" class="delete-time"
          >
            刪除時間和地點
            <!--<ion-icon slot="icon-only" icon="close"></ion-icon>-->
          </ion-button>
        </div>
      </div>
      <!--<div class="label">參與人數：</div>-->
      <!--<ion-input [(ngModel)]="form.num" class="input stu-num"></ion-input>-->
      <ion-button (click)="onAddSub()" size="small" style="padding-left: 0.5rem;">添加時間和地點</ion-button>
    </div>
    <div class="files">
      <input #inputFile type="file" accept="image/*" hidden (change)="onChangeFile($event)">
      <!--<div class="file-preview" *ngFor="let item of attachment_array; let i = index">-->
      <!--  <ion-img [src]="getPath(item)"></ion-img>-->
      <!--  <ion-icon icon="remove" (click)="onRemoveFile(item, i)"></ion-icon>-->
      <!--</div>-->
      <div class="file-preview" *ngFor="let item of files; let i = index">
        <ion-img [src]="item.base64"></ion-img>
        <ion-icon icon="remove" (click)="onRemoveFile()"></ion-icon>
      </div>
      <div *ngIf="files.length === 0" class="select-file ion-activatable" (click)="onAddFile()">
        <ion-icon icon="add"></ion-icon>
        <ion-ripple-effect type="bounded"></ion-ripple-effect>
      </div>
    </div>
    <div class="form-item remark">
      <div class="label">備註：</div>
      <ion-textarea [(ngModel)]="form.remark" rows="5" class="input remark"></ion-textarea>
    </div>
  </div>

</ion-content>
