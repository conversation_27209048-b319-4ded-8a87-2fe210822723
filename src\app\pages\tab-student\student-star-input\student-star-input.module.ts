import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {Routes, RouterModule} from '@angular/router';

import {IonicModule} from '@ionic/angular';

import {TranslateModule} from '@ngx-translate/core';
import {InfoLabelModule} from '@app/components/info-label/info-label.module';
import {StudentStarInputPage} from './student-star-input.page';

const routes: Routes = [
    {
        path: '',
        component: StudentStarInputPage
    }
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        RouterModule.forChild(routes),
        TranslateModule,
        InfoLabelModule
    ],
    // declarations: [StudentStarInputPage]
})
export class StudentStarInputPageModule {
}
