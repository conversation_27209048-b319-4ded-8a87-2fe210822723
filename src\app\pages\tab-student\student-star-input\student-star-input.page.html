<ion-header class="inside-page">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>摘星項目詳情</ion-title>
    <ion-buttons slot="end">
      <ion-button [hidden]="!can_edit" (click)="onSave()">
        儲存
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <!-- 摘星種類 -->
  <app-info-label title="摘星種類">
    {{ data.cname }}
  </app-info-label>
  <app-info-label title="摘星標題">
    {{ data.tname }}
  </app-info-label>
  <app-info-label title="摘星項目">
    {{ data.detail }}
  </app-info-label>
  <!-- 類別 -->
  <app-info-label title="類別" [hidden]="data.type_value === null || data.type_value === ''">
    <span *ngIf="data.type_value === 'DESCRIBE'">描述</span>
    <span *ngIf="data.type_value === 'NAME'">名稱</span>
    <span *ngIf="data.type_value === 'AWARD'">獎項</span>
    <span *ngIf="data.type_value === 'ECA'">ECA</span>
  </app-info-label>

  <app-info-label *ngIf="data.type_value === 'DESCRIBE' || data.type_value === 'NAME' || data.type_value === 'AWARD'" title="內容">
    <header>
      <i [hidden]="!can_edit" class="iconfont icon-ios-add-circle-outli" (click)="addTextItem()" style="font-size: 14px;margin-left: 4px;"></i>
    </header>
    <ion-item *ngFor="let textItem of textList" [disabled]="!can_edit" >
      <ion-checkbox slot="start" [(ngModel)]="selectedText['tl_' + textItem.id]" (ionChange)="textListChanged($event)"></ion-checkbox>
      <ion-label>{{ textItem.detail}}</ion-label>
    </ion-item>
    <ion-item *ngFor="let textItem of addTextList;let addTextIndex = index" [disabled]="!can_edit" >
      <ion-checkbox slot="start" [(ngModel)]="selectedText['tl_' + textItem.text_id]" (ionChange)="addTextChanged($event)"></ion-checkbox>
      <ion-input clearInput [(ngModel)]="textItem.detail" placeholder="請在輸入後勾選"></ion-input>
      <i class="iconfont icon-ios-close-circle-out" (click)="removeTextItem(addTextIndex, textItem.text_id)" style="font-size: 14px;line-height: 20px;"></i>
    </ion-item>
  </app-info-label>

  <app-info-label *ngIf="data.type_value === 'ECA'" title="內容">
    <header>
      <i [hidden]="!can_edit" class="iconfont icon-search" (click)="searchActivity()" style="font-size: 14px;margin-left: 4px;"></i>
    </header>
    <ion-item *ngFor="let activityItem of selectedActivities;let activityIndex = index" [disabled]="!can_edit" class="eca-select" style="height: 24px;--min-height:24px; font-size: 10px">
      <ion-label class="eca-select-label">{{ activityItem.actName}}</ion-label>
      <i class="iconfont icon-ios-close-circle-out" (click)="removeActivity(activityIndex)" style="font-size: 14px;line-height: 20px;"></i>
    </ion-item>
  </app-info-label>

  <app-info-label title="獲取顆數">
    <ion-buttons slot="button">
      <ion-back-button ></ion-back-button>
    </ion-buttons>
    <i
      *ngFor="let starCount of [].constructor(data.max_num);let starIndex = index"
      [classList]="getStarClass(starIndex)"
      class="iconfont icon-ios-star-outline"
      (click)="changeStarCount(starIndex + 1)"></i>
  </app-info-label>

</ion-content>
