import { Injectable } from '@angular/core';
import {RequestService} from '@services/request/request.service';

@Injectable({
  providedIn: 'root'
})
export class GoogleService {


  constructor(public request: RequestService) {
  }

  /**
   * 記錄google授權 code
   */
  updateCode(code: string, // [string] Value
  ) {
    return this.request.request({
      url: `/v2/app/google/calendar/auth/record`,
      method: 'POST',
      data: {
        code
      }

    })
  }

  /**
   * 同步用戶的Google Calendar
   */
  syncGoogleCAlendar(user_id: string, // 用戶id
  ) {
    return this.request.request({
      url: `/v2/app/user/${user_id}/actions/sync-google-calendar`,
      method: 'POST',
      responseType: 'full',
      data: {
        user_id
      }
    })
  }


}
