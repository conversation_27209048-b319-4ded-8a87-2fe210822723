import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TabStudentPage } from './tab-student.page';

describe('TabStudentPage', () => {
  let component: TabStudentPage;
  let fixture: ComponentFixture<TabStudentPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TabStudentPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TabStudentPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
