<ion-header class="inside-page">
    <ion-toolbar>
        <ion-buttons slot="start">
            <ion-button (click)="onClose()">
                <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
            </ion-button>
        </ion-buttons>
        <ion-title>摘星明細</ion-title>
    </ion-toolbar>
</ion-header>

<ion-content #contentList *ngIf="listData">
    <ion-header class="filter">
        <ion-row class="row" style="margin-bottom: 3px;">
            <ion-col col-12 class="col">
                <ion-label class="input-label">學年</ion-label>
                <ion-select
                        [(ngModel)]="listDataFilter.year_id"
                        placeholder="學年"
                        okText="確認"
                        cancelText="取消"
                        interface="popover"
                        (ngModelChange)="initCategory(true)">
                    <ion-select-option
                            *ngFor="let year of years"
                            [value]="toNumber(year.id)">
                        {{ year.year_name }}
                    </ion-select-option>
                </ion-select>
            </ion-col>
            <ion-col col-12 class="col">
                <ion-label class="input-label">學期</ion-label>
                <ion-select
                        [(ngModel)]="listDataFilter.semester"
                        placeholder="學期"
                        okText="確認"
                        cancelText="取消"
                        interface="popover"
                        (ngModelChange)="loadListData()">
                    <ion-select-option [value]="1">上學期</ion-select-option>
                    <ion-select-option [value]="2">下學期</ion-select-option>
                </ion-select>
            </ion-col>

        </ion-row>
        <ion-row class="row" style="margin-bottom: 3px;">
            <ion-col col-24 class="col">
                <ion-label class="input-label">摘星種類</ion-label>
                <ion-select
                        [(ngModel)]="listDataFilter.scid"
                        class="long_col"
                        placeholder="類別"
                        okText="確認"
                        cancelText="取消"
                        interface="popover"
                        (ngModelChange)="loadListData()">
                    <ion-select-option value="">全部</ion-select-option>
                    <ion-select-option
                            *ngFor="let item of categories"
                            [value]="item.id"
                    >{{ item.name }}</ion-select-option>
                </ion-select>
                <ion-button color="search" (click)="loadListData()">搜尋</ion-button>
            </ion-col>
        </ion-row>
    </ion-header>
    <ion-list *ngIf="listData.length > 0; else emptyTips">
        <span class="detail_text">可摘星星總數: {{ totalStar }}，已獲星星總數：{{ obtainedStar }}</span>
        <ion-item
                *ngFor="let item of listData;let index = index"
                detail="false"
                lines="none"
                class="list-item"
                [classList]="'list-item ' + getStatusClass(item.type_value)"
        >
            <div class="item-info">
                <div class="project-name">{{ item.cname }}</div>
                <div class="info">摘星標題：{{ item.tname }}</div>
                <div class="info">摘星項目：{{ item.detail }}</div>
                <div class="info">獲取顆數：
                    <i
                            *ngFor="let starCount of [].constructor(item.max_num);let starIndex = index"
                            [classList]="getStarClass(starIndex, submitJson['pid_' + item.id].num)"
                            class="iconfont icon-ios-star-outline"></i>
                </div>
                <div class="info" >狀態：{{ getStatus(item.is_audit) }}</div>
            </div>
        </ion-item>
    </ion-list>
    <ng-template #emptyTips>
        <!--<div class="page-empty-tips"></div>-->
        <div class="empty-tips">
            暫無數據
        </div>
    </ng-template>

</ion-content>
