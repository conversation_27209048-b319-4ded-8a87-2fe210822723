<ion-header class="header">
  <ion-toolbar>
    <ion-title >訊息</ion-title>
    <ion-buttons slot="end">
      <ion-button [hidden]="false" (click)="onReadAll()">
        全部已讀
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-header>
  <ion-item class="header" lines="full">
    <!--<ion-row>-->
    <!--  <ion-col size="6"><ion-label>內容</ion-label></ion-col>-->
    <!--  <ion-col size="6"><ion-label>創建時間</ion-label></ion-col>-->
    <!--</ion-row>-->
    <ion-label class="title">內容</ion-label>
    <ion-text class="datetime">創建時間</ion-text>
    <ion-icon slot="end" class="icon-area"></ion-icon>
  </ion-item>
</ion-header>
<ion-content #content class="sys-msg-index" [scrollEvents]="true" (ionScroll)="onScroll($event)">
  <ion-refresher slot="fixed" [pullMax]="500" (ionRefresh)="loadData($event, true, true)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-list #list *ngIf="listData.length > 0; else emptyTips">
    <ion-item *ngFor="let item of listData" lines="full" detail button (click)="readMessage(item)">
      <!--<ion-row>-->
      <!--  <ion-col size="6"><ion-label class="title">{{ item.name }}</ion-label></ion-col>-->
      <!--  <ion-col size="6"><ion-label class="datetime">{{ item.created_at}}</ion-label></ion-col>-->
      <!--</ion-row>-->
      <i [classList]="'dot ' + getStatusClass(item.read)"></i>
      <!--<ion-label class="title">{{ item.msg_content }}</ion-label>-->
      <!--<ion-text class="datetime">{{ item.created_at}}</ion-text>-->
      <!--<ion-row>-->
      <!--  <ion-label class="title">{{ item.name }}</ion-label>-->
      <!--  <ion-text class="datetime">{{ item.created_at}}</ion-text>-->
      <!--</ion-row>-->
      <!--<ion-row>-->
      <!--  <ion-text class="content">{{ item.msg_content}}</ion-text>-->
      <!--</ion-row>-->
      <div class="msg-item">
        <div class="title-row">
          <div class="title"><h2 class="title">{{ item.name }}</h2></div>
          <div class="datetime"> <h2 class="datetime">{{ getDate(item.created_at) }}</h2></div>
        </div>
        <!--<h2 class="title">{{ item.name }} <span class="datetime">{{ item.created_at }}</span></h2>-->
        <p class="content">{{ item.msg_content}}</p>
      </div>
    </ion-item>
  </ion-list>

  <ng-template #emptyTips>
    <div class="page-empty-tips"></div>
  </ng-template>

  <ion-infinite-scroll #infiniteScroll threshold="10px" (ionInfinite)="loadData($event, false)">
    <ion-infinite-scroll-content
        loadingSpinner="bubbles"
        loadingText="正在加載...">
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>

  <!--<ion-fab [hidden]="!showFab" vertical="bottom" horizontal="end" slot="fixed">-->
  <!--  <ion-fab-button (click)="scrollToTop()">-->
  <!--    <ion-icon name="ios-arrow-up"></ion-icon>-->
  <!--  </ion-fab-button>-->
  <!--</ion-fab>-->

</ion-content>
