<ion-header class="center">
  <ion-toolbar>
    <ion-title>{{ title }}</ion-title>
    <ion-buttons slot="end">
      <!-- 沒有用戶ID = 管理員， 可新增-->
      <ion-button [hidden]="!canCreate" (click)="onCreate()">
        新增
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content #content class="stu-msg-index" [scrollEvents]="true">
  <ion-refresher slot="fixed" [pullMax]="500" (ionRefresh)="loadData($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-list #list *ngIf="listData.length > 0; else emptyTips">
    <ion-item *ngFor="let item of listData" lines="full" detail button (click)="readMessage(item)">
      <!--<ion-row>-->
      <!--  <ion-col size="6"><ion-label class="title">{{ item.name }}</ion-label></ion-col>-->
      <!--  <ion-col size="6"><ion-label class="datetime">{{ item.created_at}}</ion-label></ion-col>-->
      <!--</ion-row>-->
      <i *ngIf="showDot" [classList]="'dot ' + getStatusClass(item.unread)"></i>
      <!--<ion-label class="title">{{ item.msg_content }}</ion-label>-->
      <!--<ion-text class="datetime">{{ item.created_at}}</ion-text>-->
      <!--<ion-row>-->
      <!--  <ion-label class="title">{{ item.name }}</ion-label>-->
      <!--  <ion-text class="datetime">{{ item.created_at}}</ion-text>-->
      <!--</ion-row>-->
      <!--<ion-row>-->
      <!--  <ion-text class="content">{{ item.msg_content}}</ion-text>-->
      <!--</ion-row>-->
      <div class="msg-item">
        <div class="title-row">
          <div class="title"><h2 class="title">{{ item.title }}</h2></div>
          <div class="datetime"> <h2 class="datetime">{{ getDate(item.create_time) }}</h2></div>
        </div>
        <!--<h2 class="title">{{ item.name }} <span class="datetime">{{ item.created_at }}</span></h2>-->
        <p class="content">{{ item.content }}</p>
      </div>
    </ion-item>
  </ion-list>

  <ng-template #emptyTips>
    <div class="page-empty-tips"></div>
  </ng-template>


  <ion-infinite-scroll #infiniteScroll threshold="10px" (ionInfinite)="loadData($event, false)">
    <ion-infinite-scroll-content
        loadingSpinner="bubbles"
        loadingText="正在加載...">
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>

</ion-content>

