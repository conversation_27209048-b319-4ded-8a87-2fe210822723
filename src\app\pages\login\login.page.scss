ion-content {
  --background: #FFFFFF;
}
.form_input {
    --padding-start : 8px !important
}

.login-page {
  .login-toolbar {
    --background: none;
  }

  background: white;
  height: 100vh;
  //padding-top: 5%;

  /* 居中 */
  //display: flex;
  //display: -webkit-flex;
  //align-items: center;
  //justify-content: center;


  .logo-img {
    width: 100px;
    height: 100px;
    margin-left: auto;
    margin-right: auto;
  }

  .setting-btn {
    float: right;
    padding-right: 10%;
    font-size: 21px;
  }

  .sch-name-cn {
    color: #212121;
    font-size: 16px;
    display: block;
    margin-bottom: 5px;
    width: 100%;
    text-align: center;
    margin: auto auto 5px auto;
    //line-height: .8rem;
  }
  .sch-name-en {
    color: #212121;
    font-size: 7px;
    display: block;
    margin-bottom: 5px;
    width: 100%;
    text-align: center;
    margin: auto auto 5px auto;
    //line-height: .8rem;
  }


  .form-item {
    width: 60%;
    margin: auto;
    --background-focused: #ffffff!important;
    .form-input {
      --background: #ffffff;
      text-align: center;
      //padding-left: 30px!important;
      font-size: 13px;
      --padding-bottom: 0;
      ::ng-deep {
        input {
          width: 100%;
          margin-right: -30px;
          //padding-right: 30px;

          &:focus {
            padding-right: 30px;
          }
        }
      }
    }
  }


  .submit-btn {
    //width: 20%;
    //margin: auto;
    //background-color: #003658;
    --background: #003658;
    --ion-color-tint: #003658 !important;
    --ion-color-shade: #003658 !important;
    --ion-color-base: #003658 !important;
    //background-color: #003658;
    --color: #FFFFFF !important;
    --border-radius: 12px;
    //padding: 6px 92px;
    --padding-top: 6px;
    --padding-bottom: 6px;
    --padding-start: 92px;
    --padding-end: 92px;
    font-size: 13px;

    //width: 60%;
    ::ng-deep {
      button {
        //padding: 6px 92px;
      }
    }
  }
  .submit-btn.button-disabled {
    --background: #979797!important;
  }

  .forget-password {
    text-decoration: none;
    color: #212121;
    font-size: 10px;

  }
  .roll-call {
    text-decoration: underline;
    color: #013658;
    font-size: 11px;

  }
  .login-page-footer {

    flex: 0.2;
    //padding: 10px 0;
    color: #212121;
    font-size: 11px;
    opacity: 0.45;
    text-align: center;
  }

  .content {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-image: url("/assets/images/loginBg.png");
    background-repeat: no-repeat;
    background-size: 100% auto;
    //background-position-y: 92%;
    background-position-x: left;

    background-position-y: 100%;
  }
}
