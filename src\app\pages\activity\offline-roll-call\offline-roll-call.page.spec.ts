import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { OfflineRollCallPage } from './offline-roll-call.page';

describe('OfflineRollCallPage', () => {
  let component: OfflineRollCallPage;
  let fixture: ComponentFixture<OfflineRollCallPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ OfflineRollCallPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(OfflineRollCallPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
