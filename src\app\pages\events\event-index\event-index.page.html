<ion-header>
  <ion-toolbar class="search-toolbar">
    <!--<ion-input [(ngModel)]="filter.keyword" placeholder="項目/修改標題" inputmode="search" class="search-input"-->
    <!--           [clearInput]="true"-->
    <!--           (search)="loadData()"-->
    <!--           (keyup.enter)="loadData()"-->
    <!--&gt;</ion-input>-->

    <ion-searchbar
        [(ngModel)]="filter.keyword"
        [debounce]="300"
        name="keyword"
        showCancelButton="never"
        placeholder="項目/修改標題"
        type="search"
        inputmode="search"
        (ionChange)="loadData()"
    ></ion-searchbar>

    <ion-row class="row" style="margin-bottom: 3px;">
      <ion-select [(ngModel)]="filter.system_id" [interfaceOptions]="sysSelectOptions" placeholder="歸屬系統" [selectedText]="systemText" okText="確認" cancelText="取消" (ngModelChange)="loadData()">
        <ion-select-option value="">全部</ion-select-option>
        <ion-select-option *ngFor="let item of systems"  [value]="item.system_id">{{ item.system_name }}</ion-select-option>
      </ion-select>
    </ion-row>
    <ion-row class="row">
      <ion-col size="4">
        <ion-select [(ngModel)]="filter.status" [interfaceOptions]="statusSelectOptions" placeholder="狀態" [selectedText]="statusText" okText="確認" cancelText="取消" (ngModelChange)="loadData()">
          <ion-select-option value="">全部</ion-select-option>
          <ion-select-option value="P">待通過</ion-select-option>
          <ion-select-option value="C">待確認</ion-select-option>
          <ion-select-option value="A">待安排</ion-select-option>
          <ion-select-option value="S">待開始</ion-select-option>
          <ion-select-option value="H">進行中</ion-select-option>
          <ion-select-option value="T">待測試</ion-select-option>
          <ion-select-option value="D">已完成</ion-select-option>
          <ion-select-option value="E">已關閉</ion-select-option>
        </ion-select>
      </ion-col>
      <ion-col size="4">
        <ion-select [(ngModel)]="filter.urgent" [interfaceOptions]="urgentSelectOptions" placeholder="緊急程度" [selectedText]="urgentText" okText="確認" cancelText="取消" (ngModelChange)="loadData()">
          <ion-select-option value="">全部</ion-select-option>
          <ion-select-option value="H">緊急</ion-select-option>
          <ion-select-option value="L">普通</ion-select-option>
          <ion-select-option value="E">優化</ion-select-option>
        </ion-select>
      </ion-col>
      <ion-col size="4">
        <div class="checkbox-div">
          <ion-label>未處理的</ion-label>
          <ion-checkbox [(ngModel)]="filter.only_mine" class="checkbox" (ngModelChange)="loadData()">未處理的</ion-checkbox>
        </div>
      </ion-col>
    </ion-row>
  </ion-toolbar>
</ion-header>

<ion-content #content>
  <ion-refresher slot="fixed" [pullMax]="500" (ionRefresh)="loadData($event, true, true)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-list #list *ngIf="listData.length > 0; else emptyTips">
    <ion-item
        *ngFor="let item of listData"
        detail
        button
        lines="none"
        class="list-item"
        [classList]="'list-item ' + getStatusClass(item.status)"
        (click)="toDetail(item)"
    >
      <div class="item-info">
        <div class="project-name">{{ item.project_name }}</div>
        <div class="info">修改標題：{{ item.name }}</div>
        <div class="info">緊急程度：{{ getUrgent(item.urgent) }}</div>
        <div class="info">開發進度：{{ getDevProcess(item.develop_process) }}</div>
        <div class="info">狀態：
          <span class="status">{{ getStatus(item.status) }}</span>
          <span class="time">{{ formatDateTime(item.created_at) }}</span>
        </div>
      </div>
    </ion-item>
  </ion-list>

  <ng-template #emptyTips>
    <div class="page-empty-tips"></div>
  </ng-template>

  <ion-infinite-scroll #infiniteScroll threshold="10px" (ionInfinite)="loadData($event, false)">
    <ion-infinite-scroll-content
        loadingSpinner="bubbles"
        loadingText="正在加載...">
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
