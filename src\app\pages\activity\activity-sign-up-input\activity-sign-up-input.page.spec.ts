import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ActivitySignUpInputPage } from './activity-sign-up-input.page';

describe('ActivitySignUpInputPage', () => {
  let component: ActivitySignUpInputPage;
  let fixture: ComponentFixture<ActivitySignUpInputPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ActivitySignUpInputPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ActivitySignUpInputPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
