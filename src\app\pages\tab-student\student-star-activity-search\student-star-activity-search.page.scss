ion-header {
  .custom-segment-button {
    background-color: #FFFFFFFF;
    color: #000000FF;
    border: 0px;
    font-size: 22px;
    height: 40px;

    .pane {
      border: 2px solid #3D6EFFFF;
      position: absolute;
      bottom: 0px;
      width: 92px;
    }
  }
}
ion-header.filter {
  padding-bottom: 5px;
  font-size: 0.7rem;
  padding: 0.3rem 0.2rem;

  ion-select {
    background: #ffffff;
    border: 1px solid #EDEDED;
    border-radius: 5px;
    padding: 0.1rem 0.5rem;
    width: calc(100% - 50px);
  }
  ion-select.long_col {
    width: calc(100% - 185px);
  }
  ion-label.input-label {
    padding: 0.1rem 0.3rem;
  }

  ion-button {
    height: 1.25rem;
    vertical-align: middle;
    margin: 0 0.5rem;
  }

  ion-input {
    height: 1.25rem;
    background: #ffffff;
    border: 1px solid #EDEDED;
    border-radius: 5px;
  }

  .col {
    display: flex;
    padding: 0px 5px;
  }
}

ion-content {
  --background: #FFFFFF;
}



.list-item {
  margin: 12px;
  box-shadow:1px 1px 3px 0px rgba(0,0,0,0.18);
  border-radius:4px;
  padding-left: 10px;

  background: #457DEE;
  &.success {
    background: #A0E4E4;
    .item-info .info .status {
      color: #A0E4E4;
    }
  }
  &.warning {
    background: #FF9900;
    .item-info .info .status {
      color: #FF9900;
    }
  }
  &.error {
    background: #FFAB9F;
    .item-info .info .status {
      color: #FFAB9F;
    }
  }

  // -1=活動已截止報名視作放棄
  //     0=報名中
  //     1=不同意參加
  //     4=辦理中（頁面篩選狀態時，辦理中=4就行）
  // 5=辦理中
  //     7=抽籤失敗
  //     10=取錄成功
  //     11=通告
  //     12=活動進行中
  // 活動已截止報名視作放棄
  &.status--1 { background: #FFAB9F; .item-info .info .status { color: #FFAB9F; } }
  // 報名中
  &.status-0 { background: #61EAFF; .item-info .info .status { color: #61EAFF; } }
  // 不同意參加
  &.status-1 { background: #FFDB9E; .item-info .info .status { color: #FFDB9E; } }
  // 辦理中（頁面篩選狀態時，辦理中=4就行）
  &.status-4 { background: #C2FF9E; .item-info .info .status { color: #C2FF9E; } }
  // 辦理中
  &.status-5 { background: #C2FF9E; .item-info .info .status { color: #C2FF9E; } }
  // 抽籤失敗
  &.status-7 { background: #FF7661; .item-info .info .status { color: #FF7661; } }
  // 取錄成功
  &.status-10 { background: #A0E4E4; .item-info .info .status { color: #A0E4E4; } }
  // 通告
  &.status-11 { background: #9EF2FF; .item-info .info .status { color: #9EF2FF; } }
  // 活動進行中
  &.status-12 { background: #FF9900; .item-info .info .status { color: #FF9900; } }
  // status-default
  &.status-default { background: #E6E6E6; .item-info .info .status { color: #E6E6E6; } }

  .item-info {
    padding: 5px 0;
    width: 100%;

    word-break: keep-all;
    white-space: nowrap;
    text-overflow: ellipsis;
    .project-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: calc(100% - 10px);
      width: 100%;
      font-weight:600;
      font-size:13px;
      color:rgba(96,98,102,1);
      padding-bottom: 5px;
    }
    .info {
      font-size:11px;
      color:rgba(96,98,102,1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: calc(100% - 5px);
      width: 100%;
      .status {
        color: #457DEE;
      }
      .time {
        float: right;
        color: #B0B0B0;
      }
      .list-star {
        font-size: 14px;
      }
      .icon-ios-star {
        color: #f5a623;
      }
    }
  }
}
