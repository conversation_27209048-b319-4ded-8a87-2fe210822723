import { Injectable } from '@angular/core';
import {RequestService} from '@services/request/request.service';

@Injectable({
  providedIn: 'root'
})
export class NoticesService {

  constructor(public request: RequestService) {
  }

  /**
   * 獲取用戶通告
   */
  fetchNotices({
        user_id,
        admin_permission,
        page,
        ignore_error = false
      }: {
    user_id: string, // [integer] 用戶id
    admin_permission: string, // [integer] 1=有新增權限 0=無新增權限
    page: string, // [integer] page
    ignore_error?: boolean, // [integer] page
  }) {
    return this.request.request({
      url: `/v2/app/notices`,
      method: 'GET',
      ignore_error,
      params: {
        user_id,
        admin_permission,
        page,
      }
    })
  }

  /**
   * 新增通告
   */
  createNotice({
        creator_user_id,
        title,
        content,
        user_array,
        attachment_array
      }: {
    creator_user_id: number, // [integer] 創建者的用戶id
    title: string, // [string] 標題
    content: string, // [string] 詳情
    user_array: any, // [array] array接收對象，JSON格式
    attachment_array: any, // [array] array附件，JSON格式
  }) {
    return this.request.request({
      url: `/v2/app/notices/actions/create`,
      method: 'POST',
      data: {
        creator_user_id,
        title,
        content,
        user_array,
        attachment_array,
        // user_array: JSON.stringify(user_array),
        // attachment_array: JSON.stringify(attachment_array),
      }

    })
  }



  /**
   * 獲取通告詳情
   */
  getNotice(notice_id: string, // 通告id
  ) {
    return this.request.request({
      url: `/v2/app/notices/${notice_id}`,
      method: 'GET',
    })
  }

  /**
   * 通告-更新已讀狀態
   */
  readNotice({
        notice_id,
        user_id
      }: {
    notice_id: string, // 通告id
    user_id: number, // [integer] 用戶id
  }) {
    return this.request.request({
      url: `/v2/app/notices/${notice_id}/actions/change-read-status`,
      method: 'POST',
      data: {
        notice_id,
        user_id
      }

    })
  }


  /**
   * 上載通告的附件
   */
  updateNoticeAttachment(file: any, // [file] 上載文件
  ) {
    const data = new FormData()
    data.append('file', file, file.name)
    return this.request.request({
      url: `/v2/app/notices/actions/upload-attachment`,
      method: 'POST',
      timeout: 5 * 60,
      formData: true,
      // data: {
      //   file
      // }
      data,

    })
  }




}
