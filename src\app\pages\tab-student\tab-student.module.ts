import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { TabStudentPage } from './tab-student.page';
import { TabStudentPageRoutingModule } from './tab-student.router.module'

const routes: Routes = [
  {
    path: '',
    component: TabStudentPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    // RouterModule.forChild(routes)
    TabStudentPageRoutingModule
  ],
  declarations: [TabStudentPage]
})
export class TabStudentPageModule {}
