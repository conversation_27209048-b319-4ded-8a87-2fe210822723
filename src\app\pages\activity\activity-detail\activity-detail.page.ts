import {ChangeDetector<PERSON>ef, Component, EventEmitter, OnInit} from '@angular/core';
import {NavController, Platform, ActionSheetController, ModalController} from '@ionic/angular';
import {ActivatedRoute} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import { ActivityService } from '@services/api/activity.service';
import { dateFormat, getWeekFirst, getWeekLast } from '@app/utils/index'
import {SelectClassPage} from '@pages/components/select-class/select-class.page';
import { RollCallOnlinePage } from '@pages/activity/roll-call-online/roll-call-online.page';
import {StorageService} from '@services/storage/storage.service';
import {UtilsService} from '@app/utils/utils.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {RollCallService} from '@services/sql/rollCall.service';
import {TranslateService} from '@ngx-translate/core';
import {OpenFileService} from '@services/request/open-file.service';
// import { PreviewAnyFile  } from '@ionic-native/preview-any-file/ngx';

@Component({
  selector: 'app-activity-detail',
  templateUrl: './activity-detail.page.html',
  styleUrls: ['./activity-detail.page.scss'],
})
export class ActivityDetailPage implements OnInit {

  public id: any = ''
  public data: any = {}
  public tab = 'activity'
  public lottery: any = {}
  public stuListType = 'actStu'

  public rollCallData: any = {}
  public stuLottery: any = {}
  public user: any = {}
  constructor(
    private platform: Platform,
    private routeInfo: ActivatedRoute,
    public nav: NavController,
    private loadingService: LoadingService,
    private activityService: ActivityService,
    public actionSheetController: ActionSheetController,
    private modalCtrl: ModalController,
    private storageService: StorageService,
    private nativeStorage: NativeStorage,
    private utils: UtilsService,
    private rollCallService: RollCallService,
    public translate: TranslateService,
    // public previewAnyFile: PreviewAnyFile,
    public openFileService: OpenFileService,
    protected cdr: ChangeDetectorRef
    ) { }

  ngOnInit() {
    this.platform.ready().then(async () => {
      // this.statusBar.backgroundColorByHexString('#003658')
      // this.id = this.routeInfo.snapshot.queryParams['id']
      this.id = this.routeInfo.snapshot.params.id
      this.loadData()
      this.user = await this.nativeStorage.getItem('userInfo')
    })
  }

  async loadData() {
    await this.loadingService.start()
    try {
      const res: any = await this.activityService.getActivityInfo({ id: this.id })
      console.log(res)

      const w = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

      if (res && res.activityWT) {
        res.activityWT.forEach(item => {
          const date = []
          item.dateArr = []
          for (const year in item.actDateArray) {
            const y = { year, months: [] }
            y.year = year

            let month = -1
            let monthIndex = -1
            for (const dateStr of item.actDateArray[year]) {
              const d = new Date(dateStr)
              if (month !== d.getMonth()) {
                monthIndex++
              }
              month = d.getMonth()

              if (!Array.isArray(y.months[monthIndex])) {
                y.months[monthIndex] = []
              }
              const dateCn = this.getDayStr(d, 'cn')
              const dateEn = this.getDayStr(d, 'en')
              const isToday = dateFormat(new Date()) === dateFormat(d)

              const week = w[d.getDay()]

              y.months[monthIndex].push({
                date: dateStr,
                dateCn,
                dateEn,
                week,
                isToday
              })
            }

            item.dateArr.push(y)

          }

        })
      }


      this.data = res


      await this.initRollCall(this.id)
      await this.initLottery()
      await this.initStudentLottery()

    } catch (e) {
      console.error(e)
    }

    await this.loadingService.end()
  }
  // 點名信息
  async initRollCall(activity_id) {
    try {
      const rollCallData: any = await this.activityService.getRollCall({ activity_id })

      this.rollCallData = rollCallData
    } catch (e) {
      console.error(e)
    }
  }
  // 歸程信息
  async initStudentLottery() {
    try {
      const userNo = this.user.user_no
      const actId = this.id
      const stuLottery: any = await this.activityService.getStudentLottery({ userNo, actId })

      this.stuLottery = stuLottery
      // this.stuLottery = {back: '1111', sup: null}
    } catch (e) {
      console.error(e)
    }
  }
  // 學生名單
  async initLottery() {
    try {
      const lottery: any = await this.activityService.getActivityLottery(this.id)
      // 報名名單
      const actStuList = []
      for (const grade in lottery.actStu) {
        console.log(grade)
        const g = grade.split('_')
        if (g.length === 2) {
          const c = {
            class: g[0],
            list: []
          }
          c.list = lottery.actStu[grade].map(i => Object.assign(i))
          actStuList.push(c)
        }
      }
      lottery.actStuList = this.getStuList(lottery.actStu)
      lottery.stuLotteryList = this.getStuList(lottery.stuLottery)
      lottery.stuNotLotteryList = this.getStuList(lottery.stuNotLotteryWithClass)
      lottery.stuNotAgreeList = this.getStuList(lottery.stuNotAgree)
      lottery.stuNotReplyList = this.getStuList(lottery.stuNotReply)


      this.lottery = lottery
    } catch (e) {
      console.error(e)
    }
  }
  getStuList(data) {
    const actStuList = []
    for (const grade in data) {
      const g = grade.split('_')
      if (g.length === 2) {
        const c = {
          class: g[0],
          list: []
        }
        c.list = data[grade].map(i => Object.assign(i))
        actStuList.push(c)
      }
    }
    return actStuList
  }



  isToday(date) {
    return false
  }

  getDayStr(day, type = 'cn') {
    if (type === 'cn') {
      return dateFormat(day, 'MM月dd日')// + ' ' + wStr
    } else {
      return dateFormat(day, 'MM月dd日')// + ' ' + wStr
    }
  }


  get showDown() {
    if (this.user && typeof this.user.user_type === 'string') {
      const user_type = this.user.user_type.toUpperCase()
      return user_type === 'ADMIN' || user_type === 'TEACHER' || user_type === 'STAFF' || user_type === 'OUTTEACHER'
    }
    return false
  }

  get title() {
    if (this.data && this.data.activity) {
      return this.data.activity.actName
    }
    return this.translate.instant('ACTIVITY.DETAIL.TITLE')
  }
  get actCode() {
    if (!this.data || !this.data.activity) { return '-' }
    return this.data.activity.actCode
  }
  get actTeacher() {
    if (!this.data || !this.data.activity) { return '-' }
    return this.data.activity.actTeacher
  }
  get actType() {
    if (!this.data || !this.data.activity) { return '' }
    switch (this.data.activity.actType) {
      case '1':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T1')
      case '3':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T3')
      case '5':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T5')
      case '6':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T6')
      case '7':
        return this.translate.instant('ACTIVITY.ACT_TYPE.T7')
      case '2':
      case '4':
      default:
        return ''
    }
  }




  /* ------------------------------------------------ */
  async onChangeStudentList() {
    const actionSheet = await this.actionSheetController.create({
      header: this.translate.instant('ACTIVITY.DETAIL.SELECT_LIST_TYPE'),
      buttons: [{
        text: this.translate.instant('ACTIVITY.LIST_TYPE.APP_STU'),
        // role: 'destructive',
        // icon: 'trash',
        handler: () => {
          this.stuListType = 'actStu'
          console.log('報名名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.LOTTERY'),
        // icon: 'share',
        handler: () => {
          this.stuListType = 'stuLottery'
          console.log('取錄的學生名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.NOT_LOTTERY'),
        // icon: 'arrow-dropright-circle',
        handler: () => {
          this.stuListType = 'stuNotLottery'
          console.log('未取錄的學生名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.NOT_AGREE'),
        // icon: 'heart',
        handler: () => {
          this.stuListType = 'stuNotAgree'
          console.log('不同意參加的學生名單');
        }
      }, {
        text: this.translate.instant('ACTIVITY.LIST_TYPE.NOT_REPLY'),
        // icon: 'close',
        // role: 'cancel',
        handler: () => {
          this.stuListType = 'stuNotReply'
          console.log('未回復的學生名單');
        }
      }]
    });
    await actionSheet.present();
  }

  get canSelect() {
    if (this.data && this.data.activity) {
      return !(this.data.activity.fsactId === '0' && this.data.activity.fushu === '1')
    }
    return false
  }


  get stuList() {
    switch (this.stuListType) {
      case 'actStu':
        return this.lottery.actStuList
      case 'stuLottery':
        return this.lottery.stuLotteryList
      case 'stuNotLottery':
        return this.lottery.stuNotLotteryList
      case 'stuNotAgree':
        return this.lottery.stuNotAgreeList
      case 'stuNotReply':
        return this.lottery.stuNotReplyList
      default:
        return []
    }
  }

  get stuListTypeStr() {
    switch (this.stuListType) {
      case 'actStu':
        return this.translate.instant('ACTIVITY.LIST_TYPE.APP_STU')
      case 'stuLottery':
        return this.translate.instant('ACTIVITY.LIST_TYPE.LOTTERY')
      case 'stuNotLottery':
        return this.translate.instant('ACTIVITY.LIST_TYPE.NOT_LOTTERY')
      case 'stuNotAgree':
        return this.translate.instant('ACTIVITY.LIST_TYPE.NOT_AGREE')
      case 'stuNotReply':
        return this.translate.instant('ACTIVITY.LIST_TYPE.NOT_REPLY')
      default:
        return this.translate.instant('ACTIVITY.LIST_TYPE.STU')
    }
  }



  async onDown() {
    try {

      const activity_id = this.id

      const old = await this.rollCallService.getByActId(activity_id)
      if (old) {
        if (await this.utils.confirm('已下載', '此點名紙已下載，是否更新？')) {
          const success = await this.rollCallService.updateActivity(activity_id)
          if (success) {
            this.utils.showToast({ msg: '更新成功！' })
          } else {
            this.utils.showMsg('無法更新點名紙，請重試！', '更新失敗')
          }
        }
        return
      }

      await this.loadingService.start()


      const rollCallInfo: any = await this.activityService.getRollCall({ activity_id })
      console.log(rollCallInfo)
      const activityInfo: any = await this.activityService.getActivityInfo({ id: activity_id })
      console.log(activityInfo)
      const lottery: any = await this.activityService.getActivityLottery(activity_id)
      console.log(lottery)
      // actCode: "210003"
      // actName: "朗讀比賽"
      // actEname: ""
      // actTeacher: "王寶音校長,陳燕貞老師"
      // cateCode: "P"
      // cateName: "保良局莊啟程小學家長教師會"
      // cateEnName: ""
      // activity_date: [{…}]
      await this.rollCallService.add({
        activity_id,
        actCode: rollCallInfo.actCode,
        actName: rollCallInfo.actName,
        actEname: rollCallInfo.actEname,
        actTeacher: rollCallInfo.actTeacher,
        cateCode: rollCallInfo.cateCode,
        cateName: rollCallInfo.cateName,
        cateEnName: rollCallInfo.cateEnName,
        activity_date: rollCallInfo.activity_date,
        activityInfo,
        lottery
      })
      await this.activityService.updateReloadStatus([activity_id])

      this.utils.showMsg(this.translate.instant('ACTIVITY.DETAIL.DOWN_SUCCESS'))

    } catch (e) {
      console.error(e)
    }
    await this.loadingService.end()

  }
  segmentChanged($event) {
    this.tab = $event.detail.value
    this.cdr.detectChanges();
  }

  async onRollCall(actKey, year, day) {
    debugger
    if (!this.canSelect) { return }
    console.log(actKey, year, day)
    const itemIndex = this.rollCallData.activity_date.findIndex(i => i.actKey === actKey)
    if (itemIndex === - 1) {
      return
    }
    const item = this.rollCallData.activity_date[itemIndex]

    this.editDay(year, day, itemIndex)

  }

  async editDay(year, day, itemIndex) {

    const reload = new EventEmitter();
    reload.subscribe(async () => {

      await this.loadingService.start()
      await this.initRollCall(this.id)
      await this.loadingService.end()
    });
    const data = this.rollCallData
    const activity_id = this.id
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: RollCallOnlinePage,
      componentProps: {
        domId: 'edit-day-modal',
        activity_id,
        data,
        year,
        day,
        itemIndex,
        reload,
      },
      id: 'edit-day-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    if (res.data.save) {
      await this.loadingService.start()
      await this.initRollCall(this.id)
      await this.loadingService.end()

    }
    return {}
  }

  openFile(file) {
    // http://test.esaccount.com
    // /ckttest/activity
    // /web/bundles/extramain/upload/pdf
    // /216b82c8d3684ba348c54.pdf

    const serverInfo = this.storageService.serverSetting
    // this.serverInfo.http + '://' + this.serverInfo.ip + ':' + this.serverInfo.port + '/' +
    // this.serverInfo.remoteProjectName + '/' + this.serverInfo.uri + '/' + this.schoolInfo.schLogo
    let url = ''
    // http://************/EM-Web-PLKCKT/web/bundles/extramain/upload/pdf/e6a06b1648eac4b295d6813d6c010470.pdf
    // url = serverInfo.http + '://' + serverInfo.ip + ':' + serverInfo.port
    // url += '/' + serverInfo.remoteProjectName
    // url = 'http://jmsys.norrayhk.com:88/EM-Web-PLKCKT'
    // url += '/' + serverInfo.uri
    // url += '/' + 'bundles/extramain/upload/pdf/'
    // url += file

    url = serverInfo.web_http + '://' + serverInfo.web_ip + ':' + serverInfo.web_port + '/'
    url += serverInfo.web_remoteProjectName + '/' + serverInfo.web_uri + '/' + 'bundles/extramain/upload/pdf/'
    url += file
    console.log(url)
    if (url) {
      // this.photoViewer.show(url, name, this.options);
      // this.previewAnyFile.preview(url)
      //   .then((res: any) => console.log(res))
      //   .catch((error: any) => console.error(error));
      this.openFileService.showFile(url)
    } else {
      this.utils.showMsg('文件有損壞，無法打開')
    }
    // this.utils.showToast('暫時無法打開文件')
  }

  getStuStatusClass(stu) {
    switch (this.stuListType) {
      case 'actStu':
        return stu.actAgree === 'Y' ? 'agree' : ''
      case 'stuLottery':
        return 'agree'
      case 'stuNotLottery':
        return ''
      case 'stuNotAgree':
        return 'not-agree'
      case 'stuNotReply':
        return ''
      default:
        return []
    }
  }
}
