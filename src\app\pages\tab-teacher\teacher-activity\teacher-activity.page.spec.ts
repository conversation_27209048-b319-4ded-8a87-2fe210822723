import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TeacherActivityPage } from './teacher-activity.page';

describe('TeacherActivityPage', () => {
  let component: TeacherActivityPage;
  let fixture: ComponentFixture<TeacherActivityPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TeacherActivityPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TeacherActivityPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
