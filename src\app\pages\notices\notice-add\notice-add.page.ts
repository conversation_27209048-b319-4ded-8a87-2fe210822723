import {Component, OnInit, ViewChild} from '@angular/core';
import {NoticeStudentListPage} from '@pages/notices/notice-student-list/notice-student-list.page';
import {Alert<PERSON>ontroller, ModalController, NavController} from '@ionic/angular';
import {UtilsService} from '@app/utils/utils.service';
import {NoticesService} from '@services/api/notices.service';
import {LoadingService} from '@app/utils/loading.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {StorageService} from '@services/storage/storage.service';
import {IDeactivatableComponent} from '@app/utils/deactivatable-component.interface';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-notice-add',
  templateUrl: './notice-add.page.html',
  styleUrls: ['./notice-add.page.scss'],
})
export class NoticeAddPage implements IDeactivatableComponent {

  @ViewChild('inputFile', {}) inputFile: any;

  public title = ''
  public content = ''
  public students: any = []
  public files: any = []
  public attachment_array: any = []
  public user: any = {}
  public storageUrl = ''
  public changed = false

  public hasStudents = false
  public hasTitle = false
  public hasContent = false

  constructor(
    private modalCtrl: ModalController,
    private utils: UtilsService,
    private notices: NoticesService,
    private loadingService: LoadingService,
    private nativeStorage: NativeStorage,
    private storageService: StorageService,
    public nav: NavController,
    private alertController: AlertController,
    ) { }

  async ngOnInit() {
    this.user = await this.nativeStorage.getItem('userInfo')
    this.storageUrl = this.storageService.serverSetting.storageUrl
  }

  async canDeactivate()  {
    if (this.changed) {
    const alert = await this.alertController.create({
        // header: '是否保存並離開？',
        message: '是否確認不保存並離開？',
        buttons: [
          {
            text: '否',
            role: 'cancel',
            handler: (blah) => {
              // reject()
            }
          }, {
            text: '是',
            handler: () => {
              this.changed = false
              this.nav.pop()
            }
          }
        ]
      })
    await alert.present();
    }
    return !this.changed
  }

  onClose() {
    this.nav.pop()
  }

  get stuNum() {
    let n = 0
    for (const item of this.students) {
      n += item.students.length
    }
    // this.students.forEach(item => {
    //   n += item.students.length
    // })
    return n
  }

  get selectedNum() {
    const n = this.stuNum
    return `共${n}人`
  }



  get canSubmit() {
    // return this.stuNum > 0 && this.title.length > 0 && this.content.length > 0
    return this.hasStudents && this.hasTitle && this.hasContent
  }

  async onSend() {
    await this.loadingService.start()
    try {
      this.attachment_array = []
      for (const file of this.files) {
        await this.uploadFile(file.file)
      }
    } catch (e) {
      await this.loadingService.end()
      this.utils.showToast({ msg: '文件上載失敗，請重試' })
      return
    }
    try {
      const creator_user_id = this.user.user_id
      const title = this.title
      const content = this.content
      const attachment_array = this.attachment_array.map(i => i.attachment_id)
      const user_array = []
      this.students.forEach(item => {
        user_array.push(...item.students.map(s => s.user_id))
      })
      const res = await this.notices.createNotice({ creator_user_id, title, content, attachment_array, user_array})
      console.log(res)

      this.changed = false
      this.utils.showToast({ msg: '發送成功' })
      this.onClose()
    } catch (e) {
      debugger
      this.utils.showToast({ msg: '發送發佈，請重試' })
      console.error(e)
    }

    await this.loadingService.end()
  }


  async onSelectStudent() {
    // const data = this.rollCallData
    // const activity_id = this.id
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: NoticeStudentListPage,
      componentProps: {
        domId: 'notice-stu-list-modal',
        students: this.students
      },
      id: 'notice-stu-list-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    if (res && res.data && res.data.students) {
      this.changed = true
      this.students = res.data.students
      this.hasStudents = this.students.length > 0
    }

  }


  onAddFile() {
    this.inputFile.nativeElement.click()
  }
  onChangeFile(event) {
    // console.log(event)
    const files = []
    for (const file of event.target.files) {
      const fileItem = {
        file,
        base64: '',
      }
      files.push(fileItem)

      const reader = new FileReader();
      reader.onload = (e: any) => {
        fileItem.base64 = e.target.result
      };
      reader.readAsDataURL(file);
    }
    event.target.value = ''
    console.log(files)
    // this.uploadFile(files[0])

    this.files.push(...files)

    this.changed = true
  }

  async uploadFile(file) {
    try {
      const res: any = await this.notices.updateNoticeAttachment(file)
      if (res && res.attachment_id) {
        // "attachment_id": "14",
        //   "attachment_path": "bundles/AppUpload/Notice/f68ca431960c0035/barcode.gif"
        this.attachment_array.push({
          attachment_id: res.attachment_id,
          attachment_path: res.attachment_path,
        })
        return {
          attachment_id: res.attachment_id,
          attachment_path: res.attachment_path,
        }
      }
      // this.utils.showToast({ msg: '上載成功'})
    } catch (e) {
      console.error(e)
    }
    return {}
  }
  getPath(item) {
    return this.storageUrl + '/' + item.attachment_path
  }
  onRemoveFile(item, i) {
    // this.attachment_array.splice(i, 1)
    this.files.splice(i, 1)
  }

  onChangeTitle() {
    this.changed = true
    this.hasTitle = this.title.length > 0
  }
  onChangeContent() {
    this.changed = true
    this.hasContent = this.content.length > 0
  }
}
