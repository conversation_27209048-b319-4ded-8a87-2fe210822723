import {NgModule} from '@angular/core'
import {PreloadAllModules, RouterModule, Routes} from '@angular/router'

const routes: Routes = [
  {
    path: '',
    redirectTo: '/login',
    pathMatch: 'full',
  },
  // {
  //   path: '**',
  //   redirectTo: 'login',
  //   pathMatch: 'full'
  // },
  { path: 'login', loadChildren: () => import('./pages/login/login.module').then((m) => m.LoginPageModule)},
  { path: 'tab-student', loadChildren: () => import('./pages/tab-student/tab-student.module').then((m) => m.TabStudentPageModule) },
  { path: 'student-index', loadChildren: () => import('./pages/tab-student/student-index/student-index.module').then((m) => m.StudentIndexPageModule) },
  { path: 'student-activity', loadChildren: () => import('./pages/tab-student/student-activity/student-activity.module').then((m) => m.StudentActivityPageModule) },
  { path: 'student-personal', loadChildren: () => import('./pages/tab-student/student-personal/student-personal.module').then((m) => m.StudentPersonalPageModule) },
  { path: 'student-notice', loadChildren: () => import('./pages/tab-student/student-notice/student-notice.module').then((m) => m.StudentNoticePageModule) },

  { path: 'tab-teacher', loadChildren: () => import('./pages/tab-teacher/tab-teacher.module').then((m) => m.TabTeacherPageModule) },
  { path: 'select-class', loadChildren: () => import('./pages/components/select-class/select-class.module').then((m) => m.SelectClassPageModule) },
  { path: 'select-student', loadChildren: () => import('./pages/components/select-student/select-student.module').then((m) => m.SelectStudentPageModule) },
  { path: 'activity-detail/:id', loadChildren: () => import('./pages/activity/activity-detail/activity-detail.module').then((m) => m.ActivityDetailPageModule) },
  { path: 'roll-call-online', loadChildren: () => import('./pages/activity/roll-call-online/roll-call-online.module').then((m) => m.RollCallOnlinePageModule) },
  { path: 'teacher-reserve', loadChildren: () => import('./pages/tab-teacher/teacher-reserve/teacher-reserve.module').then((m) => m.TeacherReservePageModule) },
  { path: 'notice-add', loadChildren: () => import('./pages/notices/notice-add/notice-add.module').then((m) => m.NoticeAddPageModule) },
  { path: 'notice-detail/:id', loadChildren: () => import('./pages/notices/notice-detail/notice-detail.module').then((m) => m.NoticeDetailPageModule) },
  { path: 'offline-roll-call/:id', loadChildren: () => import('./pages/activity/offline-roll-call/offline-roll-call.module').then((m) => m.OfflineRollCallPageModule) },
  { path: 'offline-roll-call-list', loadChildren: () => import('./pages/activity/offline-roll-call-list/offline-roll-call-list.module').then((m) => m.OfflineRollCallListPageModule) },
  { path: 'roll-call-offline-input', loadChildren: () => import('./pages/activity/roll-call-offline-input/roll-call-offline-input.module').then((m) => m.RollCallOfflineInputPageModule) },
  { path: 'activity-detail-student/:id', loadChildren: () => import('./pages/activity/activity-detail-student/activity-detail-student.module').then((m) => m.ActivityDetailStudentPageModule) },
  { path: 'activity-sign-up-input', loadChildren: () => import('./pages/activity/activity-sign-up-input/activity-sign-up-input.module').then((m) => m.ActivitySignUpInputPageModule) },
  { path: 'student-profile', loadChildren: () => import('./pages/user/student-profile/student-profile.module').then((m) => m.StudentProfilePageModule) },
  { path: 'teacher-profile', loadChildren: () => import('./pages/user/teacher-profile/teacher-profile.module').then((m) => m.TeacherProfilePageModule) },



  // { path: 'notice-file', loadChildren: () => import('./pages/notices/notice-file/notice-file.module').then((m) => m.NoticeFilePageModule) },
  // { path: 'notice-student-list', loadChildren: () => import('./pages/notices/notice-student-list/notice-student-list.module').then((m) => m.NoticeStudentListPageModule) },
  // { path: 'activity-select' , loadChildren: () => import('./pages/activity/activity-select/activity-select.module').then((m) => m.ActivitySelectPageModule) },
  // { path: 'activity-select-class', loadChildren: () => import('./pages/components/activity-select-class/activity-select-class.module').then((m) => m.ActivitySelectClassPageModule) },
  // { path: 'activity-select-student', loadChildren: () => import('./pages/components/activity-select-student/activity-select-student.module').then((m) => m.ActivitySelectStudentPageModule) },
  // { path: 'date-select', loadChildren: () => import('./pages/components/date-select/date-select.module').then((m) => m.DateSelectPageModule) },
  // { path: 'venue-select', loadChildren: () => import('./pages/components/venue-select/venue-select.module').then((m) => m.VenueSelectPageModule) },
  // { path: 'session-select', loadChildren: () => import('./pages/components/session-select/session-select.module').then((m) => m.SessionSelectPageModule) },
  // { path: 'resources-select', loadChildren: () => import('./pages/components/resources-select/resources-select.module').then((m) => m.ResourcesSelectPageModule) },




  // { path: 'tab-user', loadChildren: () => import('./pages/tab-user/tab-user.module').then((m) => m.TabUserPageModule) },
  // { path: 'change-pwd', loadChildren: () => import('./pages/user/change-pwd/change-pwd.module').then((m) => m.ChangePwdPageModule) },
  // // { path: 'event-index', loadChildren: () => import('./pages/events/event-index/event-index.module').then((m) => m.EventIndexPageModule) },
  // {  path: 'event/event-detail/:id', loadChildren: () => import('./pages/events/event-detail/event-detail.module').then((m) => m.EventDetailPageModule) },
  // // { path: 'text-view', loadChildren: () => import('./pages/events/text-view/text-view.module').then((m) => m.TextViewPageModule) },
  // { path: 'event/event-child', loadChildren: () => import('./pages/events/event-child/event-child.module').then((m) => m.EventChildPageModule) },
  // { path: 'event/event-attaches', loadChildren: () => import('./pages/events/event-attaches/event-attaches.module').then((m) => m.EventAttachesPageModule) },
  // { path: 'image-preview', loadChildren: () => import('./pages/image-preview/image-preview.module').then((m) => m.ImagePreviewPageModule) },
  // { path: 'maintenance-arrangements-index', loadChildren: () => import('./pages/maintenanceArrangements/maintenance-arrangements-index/maintenance-arrangements-index.module').then((m) => m.MaintenanceArrangementsIndexPageModule) },
  // { path: 'maintenance-arrangement/maintenance-arrangement-detail/:id', loadChildren: () => import('./pages/maintenanceArrangements/maintenance-arrangement-detail/maintenance-arrangement-detail.module').then((m) => m.MaintenanceArrangementDetailPageModule) },
  // { path: 'maintenance-arrangement/maintenance-contracts-detail/:id', loadChildren: () => import('./pages/maintenanceArrangements/maintenance-contracts-detail/maintenance-contracts-detail.module').then((m) => m.MaintenanceContractsDetailPageModule) },
  // { path: 'maintenance-arrangement/maintenance-arrangement-add', loadChildren: () => import('./pages/maintenanceArrangements/maintenance-arrangement-add/maintenance-arrangement-add.module').then((m) => m.MaintenanceArrangementAddPageModule) },
  // { path: 'system-message-index', loadChildren: () => import('./pages/systemMessage/system-message-index/system-message-index.module').then((m) => m.SystemMessageIndexPageModule) },

  // { path: 'maintenance-contract-select', loadChildren: () => import('./pages/maintenanceArrangements/maintenance-contract-select/maintenance-contract-select.module').then((m) => m.MaintenanceContractSelectPageModule) },

  // { path: 'customer-select', loadChildren: () => import('./pages/maintenanceArrangements/customer-select/customer-select.module').then((m) => m.CustomerSelectPageModule) },



  // { path: 'maintenance-arrangement-user-select', loadChildren: () => import('./pages/maintenanceArrangements/maintenance-arrangement-user-select/maintenance-arrangement-user-select.module').then((m) => m.MaintenanceArrangementUserSelectPageModule) },






  // { path: 'user-index', loadChildren: () => import('./pages/user/user-index/user-index.module').then((m) => m.UserIndexPageModule) },




]

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {preloadingStrategy: PreloadAllModules, relativeLinkResolution: 'legacy'})
  ],
  exports: [RouterModule]
})
export class AppRoutingModule {
}
