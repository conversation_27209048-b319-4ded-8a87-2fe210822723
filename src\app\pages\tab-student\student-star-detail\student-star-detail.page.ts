import {ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';
import {IonContent, IonInfiniteScroll, IonList, ModalController, NavController} from '@ionic/angular';
import {StarService} from '@services/api/star.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {SelectStudentService} from '@services/utils/select-student.service';
import {Router} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import {TranslateService} from '@ngx-translate/core';
import {Events} from '@app/services/events/event.service';

@Component({
    selector: 'app-student-star',
    templateUrl: './student-star-detail.page.html',
    styleUrls: ['./student-star-detail.page.scss'],
})
export class StudentStarDetailPage implements OnInit {

    @Input() year_id: any = ''
    @Input() stu_id: any = ''
    @Input() semester: any = ''
    @ViewChild('contentList', {}) contentList: IonContent;

    public listDataFilter: any = {
        stu_id: this.stu_id,
        semester: this.semester,
        year_id: this.year_id,
        scid: '',
        is_audit: '',
        stu_input: '',
        page_index: 1,
        page_size: 100
    }
    public total_count_data = 0
    public listData: any = []
    public years: any = []
    public categories: any = []
    public submitJson: any = {}
    public totalStar: any = 0
    public obtainedStar: any = 0

    public user: any = {}
    public stuInfo: any = {}

    constructor(
        private nativeStorage: NativeStorage,
        public utils: UtilsService,
        public selectStudentService: SelectStudentService,
        private modalCtrl: ModalController,
        private navCtrl: NavController,
        private router: Router,
        private loadingService: LoadingService,
        private events: Events,
        public starService: StarService,
        public translate: TranslateService,
        protected cdr: ChangeDetectorRef,
    ) {
    }


    ngOnInit() {
        this.events.subscribe('login', () => {
            this.init()
        })
        this.init()
    }

    async init() {
        this.user = await this.nativeStorage.getItem('userInfo')
        this.listData = []
        await this.initYears()
        this.listDataFilter = {
            stu_id: this.stu_id,
            semester: this.semester,
            year_id: this.year_id,
            scid: '',
            is_audit: '',
            stu_input: '',
            is_student_select: 1,
            page_index: 1,
            page_size: 100
        }
        this.initCategory()
        this.loadListData()
    }

    async initYears() {
        try {
            const res: any = await this.starService.fetchYearsDesc({})
            this.years = res.datas
        } catch (e) {
            console.error(e)
        }
    }

    async initCategory(research = false) {
        try {
            this.listDataFilter.scid = ''
            const res: any = await this.starService.fetchStarCates({year_id: this.listDataFilter.year_id})
            console.log(res)
            this.categories = res

            if (research) {
                this.loadListData()
            }
        } catch (e) {
            console.error(e)
        }
    }

    onSearchKeyUp(event) {
        if ('Enter' === event.key) {
            this.loadListData()
        }
    }

    async loadListData(event?, isNew = true, isRefresh = false) {
        await this.loadingService.start()
        this.totalStar = 0
        this.obtainedStar = 0
        let empty = false
        try {
            const params = {}

            for (const [key, value] of Object.entries(this.listDataFilter)) {
                if (value !== '') {
                    params[key] = value
                }
            }

            const res: any = await this.starService.fetchStars(params)
            this.listData = res.project !== undefined ? res.project : []
            this.stuInfo = res.student_info
        } catch (e) {
            if (e && e.code) {
                if (e.code === 3002) {
                    empty = true
                    // await this.utils.showMsg(e.desc)
                    if (isNew) {
                        this.listData = []
                    }
                }
            }
            console.error(e)
        }
        this.contentList.scrollToTop()
        // 處理數據

        this.listData.forEach(item => {
            this.totalStar = this.totalStar + item.max_num
            const obj = {
                year_id: this.listDataFilter.year_id,
                project_id: item.id,
                num: 0,
                text_array: item.text_data,
                eca_content: item.active_data
            }

            if (item.is_audit === 'AUDITED') {
                if (item.audit_num === null || isNaN(item.audit_num)) {
                    obj.num = 0
                } else {
                    obj.num = item.audit_num
                }
            } else {
                if (item.audit_num === null || isNaN(item.audit_num)) {
                    if (item.stu_alone_num !== null && !isNaN(item.stu_alone_num)) {
                        obj.num = item.stu_alone_num
                    }
                } else {
                    obj.num = item.audit_num
                }
            }
            if (item.is_audit === 'AUDITED') {
                this.obtainedStar = this.obtainedStar + obj.num
            }
            this.submitJson['pid_' + item.id] = obj
        })

        await this.loadingService.end()
    }

    getStatus(status) {
        const s = status + ''
        switch (s) {
            case 'AUDITED':
                return '已審批'
            default:
                return '待審批'
        }
    }

    getStarClass(starIndex, starCount) {
        if (isNaN(starCount) || starIndex >= starCount) {
            return 'list-star iconfont icon-ios-star-outline'
        } else {
            return 'list-star iconfont icon-ios-star'
        }
    }

    getStatusClass(tv) {
        const s = tv + ''
        switch (s) {
            case 'DESCRIBE':
                return 'tv-0'
            case 'NAME':
                return 'tv-0'
            case 'AWARD':
                return 'tv-1'
            case 'ECA':
                return 'tv-4'
            default:
                return 'status-default'
        }
    }

    onClose(save = true) {
        this.modalCtrl.dismiss({
            dismissed: true,
            save,
            data: {
                semester: this.listDataFilter.semester,
                year_id: this.listDataFilter.year_id,
            }
        });
    }

    toNumber(value) {
        return Number(value)
    }
}
