import {Component, Input, Output, EventEmitter, OnInit} from '@angular/core'

@Component({
  selector: 'app-scanner-bar',
  templateUrl: './scanner-bar.component.html',
  styleUrls: ['./scanner-bar.component.scss'],
})
export class ScannerBarComponent implements OnInit {
  @Input() public label: string
  @Input() public placeholder: string
  @Input() public bg: string
  @Input() public type: string
  @Input() public value: string
  @Output() public valueChange = new EventEmitter();
  @Output() public scan = new EventEmitter();
  @Output() public submit = new EventEmitter();

  constructor() {
  }

  ngOnInit() {
    // console.log(`--background: ${this.bg}!important;`)
  }
  get currentClass() {
    switch (this.type) {
      case 'book':
        return 'book-bar'
      case 'reader':
        return 'reader-bar'
      case 'preBorrow':
      case 'racking':
        return 'dark-green-bar'
      case 'return-book':
        return 'light-green'
      default:
        return ''
    }
  }
  get currentStyle() {
    if (this.bg) {

      return `--background: ${this.bg}!important;`
    } else {
      return ''
    }
  }
  handleChange() {
    console.log(this.value)
    this.valueChange.emit(this.value)
  }
  handleSubmit() {
    this.submit.emit()
  }

  handleScan() {
    this.scan.emit()
  }
}
