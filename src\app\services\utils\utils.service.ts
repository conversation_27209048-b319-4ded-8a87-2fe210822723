import {Injectable} from '@angular/core'
import {ToastController, AlertController, Platform, ModalController} from '@ionic/angular'
import {StorageService} from '@services/storage/storage.service'
import {StatusBar} from '@ionic-native/status-bar/ngx'
import {TranslateService} from '@ngx-translate/core'
import {NoticeStudentListPage} from '@pages/notices/notice-student-list/notice-student-list.page';

@Injectable({
  providedIn: 'root'
})
export class UtilsService {
  private duration = 2000
  private position = 'bottom'

  constructor(
    private platform: Platform,
    private statusBar: StatusBar,
    private toastCtrl: ToastController,
    private alertController: AlertController,
    private storageService: StorageService,
    private modalCtrl: ModalController,
    public translate: TranslateService,
  ) {
  }


  async showToast({
                    msg, duration, position, showClose = false, onDidDismiss}: {
                    msg: string,
                    duration?: number,
                    position?: 'bottom' | 'top' | 'middle',
                    showClose?: boolean,
                    onDidDismiss?: any }
                  ) {
    if (!msg) {
      return
    }
    const toast = await this.toastCtrl.create({
      message: msg,
      duration: duration === undefined ? this.duration : duration,
      // @ts-ignore
      position: position === undefined ? this.position : position,
      translucent: true,
      showCloseButton: showClose,
      cssClass: 'utils-toast',
      mode: 'md',
      closeButtonText: showClose ? this.translate.instant('BUTTON.CLOSE') : '',
    })
    toast.present()
  }

  async showMsg(msg: string, title?: string, btn?: string) {
    if (!msg) {
      return
    }
    const header = (title === null || title === undefined) ? this.translate.instant('MESSAGE.TIPS') : title
    const btnText = (btn === null || btn === undefined) ?  this.translate.instant('BUTTON.CONFIRM') : btn
    const alertObj = await this.alertController.create({
      header,
      message: msg,
      buttons: [{
        text: btnText,
      }],
    })
    await alertObj.present()
    await alertObj.onDidDismiss()
  }

  public getImgUrl(img, isError) {
    if (img && !isError) {
      const {http, ip, port, remoteProjectName, uri} = this.storageService.serverSetting
      const path = `${http}://${ip}:${port}/${remoteProjectName}/${uri}/`

      return path + img
    } else {
      return 'assets/images/nopic.jpg'
    }
  }
  public get errorImage() {
    return 'assets/images/nopic.jpg'
  }
  public setInsideHeaderColor() {
    this.statusBar.backgroundColorByHexString('#457DEE')
  }



  public async showModal(component, componentProps) {
    let id = 'modal-' + new Date().getTime()
    if (!componentProps.domId) {
      componentProps.domId = id
    } else {
      id = componentProps.domId
    }
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component,
      componentProps,
      id
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    return res
  }


  async confirm(title, msg, confirmText?, cancelText?) {
    let confirm = false
    const alert = await this.alertController.create({
      header: title,
      message: msg,
      backdropDismiss: false,
      buttons: [
        {
          text: cancelText || this.translate.instant('BUTTON.CANCEL'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
            console.log('CANCEL');
          }
        }, {
          text: confirmText || this.translate.instant('BUTTON.CONFIRM'),
          handler: () => {
            confirm = true
            console.log('CONFIRM');
          }
        }
      ]
    });

    await alert.present();
    await alert.onDidDismiss();
    return confirm
  }




  getStatusClass(status, actType) {
    switch (status) {
      case '0':
        return 'warning'
      case '2':
        return 'error'
      case '3':
        return 'success'
      case '4':
        return 'warning'
      case '4_1':
        return 'warning'
      case '4_2':
        return 'warning'
      case '5':
        return 'warning'
      case '5_1':
        return 'warning'
      case '5_2':
        if (actType === '5' || actType === '3') {
          return 'success'
        } else {
          return 'success'
        }
      case '5_3':
        return 'success'
      default:
        return 'error'
    }
  }
}
