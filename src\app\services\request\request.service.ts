import {Injectable} from '@angular/core'

import {AlertController, NavController, Platform} from '@ionic/angular'

import {Router} from '@angular/router'
import {AuthService} from '../auth/auth.service'
import {StorageService} from '../storage/storage.service'
import {HTTP} from '@ionic-native/http/ngx'
import {LoadingService} from '../utils/loading.service'
import {UtilsService} from '@app/utils/utils.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {isNumber} from 'util';

@Injectable({
  providedIn: 'root'
})
export class RequestService {


  constructor(
    private platform: Platform,
    private router: Router,
    private nav: NavController,
    private alertController: AlertController,
    private authService: AuthService,
    private storageService: StorageService,
    private loading: LoadingService,
    private httpService: HTTP,
    private utils: UtilsService,
    private nativeStorage: NativeStorage,
    ) {
    this.platform.ready()
      .then(async () => {
        // nocheck pinned
        httpService.setServerTrustMode('nocheck').then((res: any) => {
            console.log('固定證書成功')
            // this.utils.showMsg('固定證書成功' + res)
          }, (error) => {
            console.log('固定證書失敗')
            // this.utils.showMsg('固定證書失敗' + error)
          });
      })
  }

  async request(obj: any) {
    const that = this
    const headers: any = {}


    let userId = ''
    try {
      const user: any = await this.nativeStorage.getItem('userInfo')
      if (user) {
        userId = user.user_id
        if (userId) {
          headers.userId = userId + ''
        }
      }
    } catch (e) {
      console.log('no user')
    }

    let timeout = 60

    if (obj.timeout && isNumber(obj.timeout)) {
      timeout = obj.timeout
    }
    // 超時5分鐘
    this.httpService.setRequestTimeout(timeout)

    // this.httpService.setDataSerializer('json');
    if (this.authService.getToken() != null && this.authService.getToken() !== '') {
      // console.log(getToken())
      headers.authorization = 'Bearer ' + this.authService.getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    headers.language = 'zh-hk'// this.storageService.store.language ||
    headers.device = this.storageService.store.device || 'Android'
    // headers.system = this.storageService.serverSetting.webCode

    const {http, ip, port, remoteProjectName, uri} = this.storageService.serverSetting
    const url = http + '://' + ip + ':' + port + '/' + remoteProjectName + '/' + uri + obj.url

    if (obj.formData) {
      this.httpService.setDataSerializer('multipart');

      return new Promise((resolve, reject) => {
        this.httpService.post(url, obj.data, headers).then(res => {
          try {
            const result = that.formatResponse(res, obj)
            if (result.success) {
              resolve(result.data)
            } else {
              reject(result.data)
            }
          } catch (e) {
            reject('服務器響應數據錯誤')
          }
        }).catch(async error => {
          console.error(error)
          const result = await that.formatError(error, obj)
          reject(result)
        })
      })

    } else if (obj.method.toLowerCase() === 'get') {
      this.httpService.setDataSerializer('json');
      const params: any = {}
      for (const key in obj.params) {
        if (obj.params.hasOwnProperty(key) && obj.params[key] !== undefined) {
          params[key] = obj.params[key] + ''
        }
      }
      return new Promise((resolve, reject) => {
        this.httpService.get(url, params, headers)
          .then(response => {
            try {
              const result = that.formatResponse(response, obj)
              if (result.success) {
                resolve(result.data)
              } else {
                reject(result.data)
              }
            } catch (e) {
              reject('服務器響應數據錯誤')
            }
          })
          .catch(async error => {
            // switch (typeof err) {
            //   case 'string':
            //   case 'number':
            //     reject(err.toString())
            //     break
            //   case 'object':
            //     if (err.message) {
            //       reject(err.message)
            //     } else {
            //       reject(JSON.stringify(err))
            //     }
            //     break
            //   default:
            //     reject(JSON.stringify(err))
            //     break
            // }

            console.log(error)
            const result = await that.formatError(error, obj)
            reject(result)
          })
      })

    } else {
      this.httpService.setDataSerializer('json');
      // const url = 'http://192.168.88.10/seschool/public/api/se/login'
      // const url = 'http://test.esaccount.com/EDSchoolCenter-LB/web/lb/readers/login'
      let postData = {
        // _format: 'json',
        ...obj.data
      };
      if (obj.formData) {
        postData = obj.data
      } else {
        postData = {
          ...obj.data
        };
      }
      return new Promise((resolve, reject) => {
        this.httpService.post(url, postData, headers)
          .then(response => {
            try {
              const result = that.formatResponse(response, obj)
              if (result.success) {
                resolve(result.data)
              } else {
                reject(result.data)
              }
            } catch (e) {
              reject('服務器響應數據錯誤')
            }
          })
          .catch(async error => {
            console.log(error)
            const result = await that.formatError(error, obj)
            reject(result)


          })
      })
      //   .then(response => {
      //     resolve(response)
      //   }).catch(async error => {
      //     const alertObj = await this.alertController.create({
      //       header: '提示',
      //       message: error.message,
      //       buttons: [{
      //         text: '好'
      //       }]
      //     });
      //     await alertObj.present();
      //     reject()
      //   })
      // })
    }
  }

  async requestWithToken(obj: any) {
    const that = this
    const headers: any = {}

    let timeout = 60

    if (obj.timeout && isNumber(obj.timeout)) {
      timeout = obj.timeout
    }
    // 超時5分鐘
    this.httpService.setRequestTimeout(timeout)

    // this.httpService.setDataSerializer('json');
    if (this.authService.getToken() != null && this.authService.getToken() !== '') {
      // console.log(getToken())
      headers.authorization = 'Bearer ' + this.authService.getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    headers.language = 'zh-hk'// this.storageService.store.language ||
    headers.device = this.storageService.store.device || 'Android'
    // headers.system = this.storageService.serverSetting.webCode

    const {star_http, star_ip, star_port, star_remoteProjectName, star_uri} = this.storageService.serverSetting
    const url = star_http + '://' + star_ip + ':' + star_port + '/' + star_remoteProjectName + '/' + star_uri + obj.url
    if (obj.formData) {
      this.httpService.setDataSerializer('multipart');

      return new Promise((resolve, reject) => {
        this.httpService.post(url, obj.data, headers).then(res => {
          try {
            const result = that.formatResponse2(res, obj)
            if (result.success) {
              resolve(result.data)
            } else {
              reject(result.data)
            }
          } catch (e) {
            reject('服務器響應數據錯誤')
          }
        }).catch(async error => {
          console.error(error)
          const result = await that.formatError(error, obj)
          reject(result)
        })
      })

    } else if (obj.method.toLowerCase() === 'get') {
      this.httpService.setDataSerializer('json');
      const params: any = {}
      for (const key in obj.params) {
        if (obj.params.hasOwnProperty(key) && obj.params[key] !== undefined) {
          params[key] = obj.params[key] + ''
        }
      }
      return new Promise((resolve, reject) => {
        this.httpService.get(url, params, headers)
            .then(response => {
              try {
                const result = that.formatResponse2(response, obj)
                if (result.success) {
                  resolve(result.data)
                } else {
                  reject(result.data)
                }
              } catch (e) {
                reject('服務器響應數據錯誤')
              }
            })
            .catch(async error => {
              console.log(error)
              const result = await that.formatError(error, obj)
              reject(result)
            })
      })

    } else {
      this.httpService.setDataSerializer('json');
      // const url = 'http://192.168.88.10/seschool/public/api/se/login'
      // const url = 'http://test.esaccount.com/EDSchoolCenter-LB/web/lb/readers/login'
      let postData = {
        // _format: 'json',
        ...obj.data
      };
      if (obj.formData) {
        postData = obj.data
      } else {
        postData = {
          ...obj.data
        };
      }
      return new Promise((resolve, reject) => {
        this.httpService.post(url, postData, headers)
            .then(response => {
              try {
                const result = that.formatResponse2(response, obj)
                if (result.success) {
                  resolve(result.data)
                } else {
                  reject(result.data)
                }
              } catch (e) {
                reject('服務器響應數據錯誤')
              }
            })
            .catch(async error => {
              console.log(error)
              const result = await that.formatError(error, obj)
              reject(result)
            })
      })
    }
  }

  formatResponse(res, params) {
    if (params.responseType === 'source') {
      return { success: true, data: res.data }
    }
    let data: any = {}
    try {
      data = JSON.parse(res.data)
    } catch (e) {
      return { success: true, data: res.data.data }
    }
    if (params.responseType === 'full') {
      // return data
      return { success: true, data }
    }
    switch (data.code) {
      case 200:
      case 1000:
        return { success: true, data: data.data }
      case 401:
        // reject('未登錄')
        this.reLoginAlert()
        return { success: false, data: res.data }
      case 3003:
      case 3002:
        // this.utils.showMsg('請聯繫系統管理員！', data.desc)
        return { success: false, data }
      case 403:
      // reject('沒有權限')
      // break
      case 404:
      // reject('請求地址錯誤')
      // break
      default:
        // return data.msg
        return { success: false, data: data.msg ? data.msg : data }
    }
  }

  formatResponse2(res, params) {
    if (params.responseType === 'source') {
      return { success: true, data: res.data }
    }
    let data: any = {}
    console.log(res, '999')
    try {
      data = JSON.parse(res.data)
    } catch (e) {
      return { success: true, data: res.data }
    }
    if (params.responseType === 'full') {
      // return data
      return { success: true, data }
    }
    switch (res.status) {
      case 200:
      case 1000:
        return { success: true, data }
      case 401:
        // reject('未登錄')
        this.reLoginAlert()
        return { success: false, data }
      case 403:
        // reject('沒有權限')
        // break
      case 404:
        // reject('請求地址錯誤')
        // break
        return { success: false, data: data.error ? data.error.message : data.message }
      default:
        // return data.msg
        return { success: false, data: data.error ? data.error : data }
    }
  }

  async formatError(error, params) {

    if (error && error.status === -3) {
      this.utils.showToast({ msg: '無法連接網絡' })
      return
    }
    const status = error.status ? error.status : ''
    let msg = ''
    this.loading.end()

    if (params.responseType === 'full') {
      return error
    }

    switch (status) {
      case 401:
        this.reLoginAlert()
        break
      case 422:
          {
            const errorObj = error.error ? JSON.parse(error.error) : []

            if (errorObj.errors !== undefined && errorObj.errors.upload_uuid !== undefined) {
            } else {
              msg = ''
              if (errorObj.error) {
                if (errorObj.error.errors) {
                  for (const key in errorObj.error.errors) {
                    msg += (msg === '' ? '' : '\r\n') + errorObj.error.errors[key].reduce(this.reduceFunction, '')
                  }
                } else {
                  msg = errorObj.error.message
                }
              } else {
                msg = ''
              }

              if (!params.hasOwnProperty('ignore_error') || !params.ignore_error) {
                const alertObj = await this.alertController.create({
                  header: '提示',
                  message: msg,
                  buttons: [{
                    text: '好'
                  }]
                });
                await alertObj.present()
              }
            }
          }
          break
      case 403:
      case 404:
      {
        if (!params.hasOwnProperty('ignore_error') || !params.ignore_error) {
          const errorObj = error.error ? JSON.parse(error.error) : []
          msg = errorObj.error ? errorObj.error.message : ''
          const alertObj = await this.alertController.create({
            header: '提示',
            message: msg,
            buttons: [{
              text: '好'
            }]
          });
          await alertObj.present()
        }
      }
      break
      case 500:
      {
        if (!params.hasOwnProperty('ignore_error') || !params.ignore_error) {
          const alertObj = await this.alertController.create({
            header: '服務器錯誤',
            message: '發生了錯誤，請聯絡系統管理員。',
            buttons: [{
              text: '好'
            }]
          });
          await alertObj.present()
        }
      }
      break
      default:
    }
    return JSON.stringify(error)
  }

  async reLoginAlert() {
    this.loading.end()
    const alertObj = await this.alertController.create({
      header: '重新登錄',
      message: '此賬戶已在其它地方登入',
      backdropDismiss: false,
      buttons: [{
        text: '重新登錄',
        handler: () => {
          // this.router.navigate(['login'])
          location.href = '/login'
          // this.nav.setTopOutlet('/login')
        }
      }]
    })
    await alertObj.present()
    await alertObj.onDidDismiss()
  }

  responseHandler(response) {

  }

  reduceFunction = function(prev, current) {
    if (Array.isArray(current)) {
      current = current.reduce(this.reduceFunction, '')
    }
    return prev + (prev !== '' ? ';\n' : '\n') + current
  }
}
