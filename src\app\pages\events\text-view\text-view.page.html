<ion-header class="inside-page">
  <ion-toolbar>
    <ion-buttons slot="start">
      <!--<ion-back-button (click)="onClose()"></ion-back-button>-->

      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{ title }}</ion-title>
    <ion-buttons slot="end">
      <ion-button [hidden]="!canEdit" (click)="onSave()">
        保存
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-textarea
      *ngIf="show"
      [(ngModel)]="content"
      [autoGrow]="true"
      [readonly]="!canEdit"
      [placeholder]="canEdit ? '請輸入內容' : '- 空 -'"
    ></ion-textarea>

</ion-content>
