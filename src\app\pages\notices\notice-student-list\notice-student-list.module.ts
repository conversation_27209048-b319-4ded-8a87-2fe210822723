import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { NoticeStudentListPage } from './notice-student-list.page';
// import {ActivitySelectClassPage} from '@pages/components/activity-select-class/activity-select-class.page';

const routes: Routes = [
  {
    path: '',
    component: NoticeStudentListPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    // RouterModule.forChild(routes)
  ],
  declarations: [NoticeStudentListPage],
  entryComponents: [NoticeStudentListPage],
  exports: [NoticeStudentListPage]
})
export class NoticeStudentListPageModule {}
