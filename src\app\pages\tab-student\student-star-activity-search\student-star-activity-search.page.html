<ion-header class="inside-page">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>摘星項目詳情</ion-title>
    <ion-buttons slot="end">
      <ion-button [hidden]="false" (click)="onSave()">
        儲存
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content #contentList *ngIf="data.length > 0">
  <ion-header class="filter">
    <ion-row class="row" style="margin-bottom: 3px;">
      <ion-col col-24 class="col">
        <ion-searchbar
                [(ngModel)]="activityFilter.actCodeOrName"
                [debounce]="300"
                name="keyword"
                showCancelButton="never"
                placeholder="輸入活動編號/活動名稱"
                type="search"
                inputmode="search"
                [ngModelOptions]="{standalone: true}"
                (keyup)="onSearchKeyUp($event)"
                (ionBlur)="loadData()"
        ></ion-searchbar>
      </ion-col>
    </ion-row>

    <ion-row class="row" style="margin-bottom: 3px;">
      <ion-col col-24 class="col">
        <ion-label class="input-label">負責老師</ion-label>
        <ion-select
                [(ngModel)]="activityFilter.act_teacher"
                class="long_col"
                placeholder="負責老師"
                okText="確認"
                cancelText="取消"
                interface="popover"
                (ngModelChange)="loadData()">
          <ion-select-option value="">全部</ion-select-option>
          <ion-select-option
                  *ngFor="let item of teachers"
                  [value]="item.id"
          >{{ item.teaUname }}</ion-select-option>
        </ion-select>
        <ion-button color="search" (click)="loadData()">搜尋</ion-button>
      </ion-col>
    </ion-row>
  </ion-header>
  <ion-list *ngIf="data.length > 0; else emptyTips">
    <ion-item *ngFor="let item of data">
      <ion-checkbox slot="start" [(ngModel)]="selectedData[item.id]"></ion-checkbox>
      <ion-label>{{item.actName}}</ion-label>
    </ion-item>
  </ion-list>

  <ng-template #emptyTips>
    <!--<div class="page-empty-tips"></div>-->
    <div class="empty-tips">
      暫無數據
    </div>
  </ng-template>
</ion-content>
