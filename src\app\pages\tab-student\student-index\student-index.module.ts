import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { StudentIndexPage } from './student-index.page';
import { CalendarModule } from 'ion2-calendar';
import { UserPopoverComponent } from '@app/components/user-popover/user-popover.component';
import {TranslateModule} from '@ngx-translate/core';
import {TeacherIndexPage} from '@pages/tab-teacher/teacher-index/teacher-index.page';

const routes: Routes = [
  {
    path: '',
    component: StudentIndexPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    CalendarModule,
    ReactiveFormsModule,
    TranslateModule,
  ],
  declarations: [
    StudentIndexPage,
    // UserPopoverComponent
  ],
  entryComponents: [
    // UserPopoverComponent
  ]
})
export class StudentIndexPageModule {}
