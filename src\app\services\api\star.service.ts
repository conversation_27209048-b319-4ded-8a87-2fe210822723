import {Injectable} from '@angular/core';
import {RequestService} from '@services/request/request.service';

@Injectable({
    providedIn: 'root'
})
export class StarService {

    constructor(private request: RequestService) {
    }

    fetchYearsDesc({}: any) {
        return this.request.request({
            url: '/master/year_index_desc',
            method: 'get',
            params: {}
        })
    }

    fetchYearsTimeSetting({year_id, is_all}: {year_id?: number, is_all?: number,}) {
        return this.request.requestWithToken({
            url: '/time-setting/actions/inquire',
            method: 'get',
            params: {
                year_id,
                is_all
            }
        })
    }

    fetchStars({stu_id, semester, year_id, scid, is_audit, stu_input, is_student_select}:
                   {
                       stu_id?: number,
                       semester?: number,
                       year_id?: number,
                       scid?: number,
                       is_audit?: number,
                       stu_input?: number,
                       is_student_select?: number
                   }) {
        return this.request.requestWithToken({
            url: '/star-input/actions/inquire',
            method: 'get',
            params: {
                stu_id,
                semester,
                year_id,
                scid,
                is_audit,
                stu_input,
                is_student_select,
            }
        })
    }

    fetchStarCates({year_id, page, page_size}:
                       {
                           year_id?: number,
                           page?: number,
                           page_size?: number
                       }) {
        return this.request.requestWithToken({
            url: '/star-cate/list',
            method: 'get',
            params: {
                year_id,
                page,
                page_size
            }
        })
    }

    getStarProject({project_id, stu_id}:
                       {
                           project_id?: number,
                           stu_id?: number
                       }) {
        return this.request.requestWithToken({
            url: '/star-input/actions/project-inquire',
            method: 'get',
            params: {
                project_id,
                stu_id
            }
        })
    }

    fetchTextList({year_id, project_id, page, page_size}:
                       {
                           year_id?: number,
                           project_id?: number,
                           page?: number,
                           page_size?: number
                       }) {
        return this.request.requestWithToken({
            url: '/star-text/list',
            method: 'get',
            params: {
                year_id,
                project_id,
                page,
                page_size
            }
        })
    }

    fetchStuActivities({year_id, semester, stu_id, act_category, act_name, act_teacher, act_lottery}:
                    {
                        year_id?: number,
                        semester?: number,
                        stu_id?: number,
                        act_category?: number,
                        act_name?: string,
                        act_teacher?: number,
                        act_lottery?: number
                    }) {
        return this.request.requestWithToken({
            url: '/star-input/stu-active-list',
            method: 'get',
            params: {
                year_id,
                semester,
                stu_id,
                act_category,
                act_name,
                act_teacher,
                act_lottery
            }
        })
    }

    fetchTeachers({lang, teacher_no, teacher_name}:
                       {
                           lang?: string,
                           teacher_no?: string,
                           teacher_name?: string
                       }) {
        return this.request.requestWithToken({
            url: '/teachers',
            method: 'get',
            params: {
                lang: 'en',
                teacher_no,
                teacher_name
            }
        })
    }
    updateStar({stu_id, is_student, year_id, semester, data_json, is_audit}:
                       {
                           stu_id?: number,
                           is_student?: number,
                           year_id?: number,
                           semester?: number,
                           data_json?: any,
                           is_audit?: number
                       }) {
        return this.request.requestWithToken({
            url: '/star-input/actions/update',
            method: 'POST',
            responseType: 'full',
            data: {
                stu_id,
                is_student,
                year_id,
                semester,
                data_json,
                is_audit
            }
        })
    }
}
