<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>學生</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="onSelectAll()">
        {{ selectStr }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list>
    <ion-item button lines="full" *ngFor="let item of list">
      <ion-checkbox [(ngModel)]="item.checked" size="mini" slot="start">全選</ion-checkbox>
      <div class="stu-info">
        <div *ngIf="getClassNo(item)" class="stu-no">({{ getClassNo(item) }})</div>
        <div class="stu-name">{{ item.stuUname }}<br>{{ item.stuEname }}</div>
      </div>
    </ion-item>
  </ion-list>
  <div class="empty-tips" *ngIf="list.length === 0">班級暫無學生</div>
</ion-content>
