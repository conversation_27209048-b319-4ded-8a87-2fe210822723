import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { RollCallOfflineInputPage } from './roll-call-offline-input.page';

const routes: Routes = [
  {
    path: '',
    component: RollCallOfflineInputPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  // declarations: [RollCallOfflineInputPage]
})
export class RollCallOfflineInputPageModule {}
