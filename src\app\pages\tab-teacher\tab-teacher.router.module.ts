import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TabTeacherPage } from '@pages/tab-teacher/tab-teacher.page';

const routes: Routes = [
  {
    path: '',
    component: TabTeacherPage,
    children: [
      // 主頁
      {
        path: 'index',
        children: [
          {
            path: '',
            loadChildren: () => import('./teacher-index/teacher-index.module').then((m) => m.TeacherIndexPageModule)
            // loadChildren: () => import('../tab1/tab1.module').then((m) => m.Tab1PageModule)
          }
        ]
      },
      // 活動報名
      {
        path: 'activity',
        children: [
          {
            path: '',
            loadChildren: () => import('./teacher-activity/teacher-activity.module').then((m) => m.TeacherActivityPageModule)
          }
        ]
      },
      // 預約
      {
        path: 'reserve',
        children: [
          {
            path: '',
            loadChildren: () => import('./teacher-reserve/teacher-reserve.module').then((m) => m.TeacherReservePageModule)
          }
        ]
      },
      // 通告
      {
        path: 'notice',
        children: [
          {
            path: '',
            loadChildren: () => import('../tab-student/student-notice/student-notice.module').then((m) => m.StudentNoticePageModule)
          }
        ],
        data: { admin: true },
      },
      {
        path: '',
        redirectTo: '/tab-teacher/index',
        // redirectTo: '/lb-admin/tab1',
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TabTeacherPageRoutingModule {}
