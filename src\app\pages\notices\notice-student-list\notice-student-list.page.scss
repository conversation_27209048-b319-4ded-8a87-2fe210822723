


ion-content {
  --background: #FFFFFF;
  .class-box {
    background: #FAFAFA;
    margin: 0.5rem;
    padding: 0.3rem 0.5rem;
    font-size: 0.7rem;
    border-radius: 0.2rem;

    .class-name {
      font-weight: bold;
      padding-bottom: 0.3rem;
    }

    .stu-box {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      flex-wrap: wrap;
      .stu-item {
        width: 5rem;
        background: #457DEE;
        margin: 0 0.5rem 0.3rem 0;
        text-align: center;
        color: #FFFFFF;
        border-radius: 0.2rem;
        padding: 0.2rem;
        //&:after {
        //  content: "X";
        //  position: fixed;
        //  right: 2.2rem;
        //}
        i {
            position: fixed;
            right: 2.2rem;
          color: #FFFFFF;
        }
        ion-icon {
          height: 1rem;
          width: 1rem;
          vertical-align: middle;
          float: right;
          color: #FFFFFF;
        }
      }
    }
  }
}
