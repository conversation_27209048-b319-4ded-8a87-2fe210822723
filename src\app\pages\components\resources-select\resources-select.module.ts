import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { ResourcesSelectPage } from './resources-select.page';

const routes: Routes = [
  {
    path: '',
    component: ResourcesSelectPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  // declarations: [ResourcesSelectPage]
})
export class ResourcesSelectPageModule {}
