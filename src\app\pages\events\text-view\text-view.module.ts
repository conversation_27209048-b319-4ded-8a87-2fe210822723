import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

// import { TextViewPage } from './text-view.page';

// const routes: Routes = [
//   {
//     path: '',
//     component: TextViewPage
//   }
// ];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    // RouterModule.forChild(routes)
  ],
  // declarations: [TextViewPage]
})
export class TextViewPageModule {}
