ion-calendar.calendar {
  font-size: 10px;
  padding: 10px 0;
    border-bottom: 1px solid #F9F9F9;
  & ::ng-deep {
    .title {
      //width: 200px;
      margin: 0 auto;
    }
    ion-calendar-week {
      & > ion-toolbar {
        min-width: 36px;
      }
    }

    ion-calendar-month {
      .days-box {
        padding: 0;
        div.days {
          height: 28px;
          button.days-btn {
            height: 23px;
            width: 23px;
            font-size: 10px;

            &.has-data p:after {
              //border-bottom: 1px solid red;
              //content: "";
              //position: absolute;
              //width: 60%;
              //bottom: -2px;
              //left: 21%;

              background: #F5A623;
              content: "";
              position: absolute;
              bottom: -6px;
              left: calc(50% - 3px);
              width: 5px;
              height: 5px;
              margin: auto;
              text-align: center;
              border: none;
              border-radius: 50%;
            }
          }
        }
      }
    }
  }

}

ion-content {
  --background: #FFFFFF;
  .content-box {
    background: #ffffff;
    .date-box {
      padding-bottom: 10px;
      .date {
        background: #F9F9F9;
        font-size: 12px;
        text-align: center;
        margin: 3px 0 0 0px;
        padding: 5px;


        position: sticky;
        top: 0;
        z-index: 1;
      }
      .row {
        display: flex;
        font-size: 12px;
        margin-bottom: 5px;
        height: 40px;
        line-height: 40px;
        margin: 3px 0;
        position: relative;

        .time {
          flex: 1;
          text-align: center;
          color: #4A90E2;
          &.hide-time {
            opacity: 0;
          }
        }
        .name {
          flex: 4;
          color: #666666;
          background: #D2EBFE;
          padding: 0 5px;
          border-radius: 3px;
          position: relative;
        }

      }
    }
  }

  .divide {
    min-height: 1px;
    /* min-width: 80%; */
    background: #457DEE;
    width: 88%;
    height: 1px;
    position: absolute;
    right: 0;
    top: -2px;
    .dot {
      width: 5px;
      height: 5px;
      background: #457dee;
      border-radius: 50%;
      left: -1px;
      position: absolute;
      bottom: -2px;
    }
  }
}

.select-date-type {
  //background: #F9F9F9;
  //border-radius: 50%;
}
