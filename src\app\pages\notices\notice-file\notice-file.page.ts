import { Component, Input, OnInit } from '@angular/core';
import {ModalController} from '@ionic/angular';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {StorageService} from '@services/storage/storage.service';
// import {PreviewAnyFile} from '@ionic-native/preview-any-file/ngx';
import {OpenFileService} from '@services/request/open-file.service';
import {LoadingService} from '@app/utils/loading.service';

@Component({
  selector: 'app-notice-file',
  templateUrl: './notice-file.page.html',
  styleUrls: ['./notice-file.page.scss'],
})
export class NoticeFilePage implements OnInit {

  @Input() files: any
  public data = []
  public storageUrl = ''

  private fileClass = {
    docx: 'word',
    doc: 'word',
    xlsx: 'excel',
    xls: 'excel',
    ppt: 'ppt',
    pdf: 'pdf',
    jpg: 'jpg',
    png: 'png',
    file: 'file',
  }

  constructor(
    private modalCtrl: ModalController,
    private utils: UtilsService,
    private storageService: StorageService,
    // private previewAnyFile: PreviewAnyFile,
    private openFileService: OpenFileService,
    private loadingService: LoadingService,
    ) { }

  async ngOnInit() {
    this.storageUrl = this.storageService.serverSetting.storageUrl
    if (this.files && this.files.length) {
      const arr = this.files.map(file => {
        return {
          path: this.getPath(file),
          name: this.getName(file),
          type: this.getType(file),
        }
      })
      this.data = arr

    } else {
      await this.utils.showMsg('無附件')
      this.onClose()
    }
  }
  getPath(file) {
    return this.storageUrl + '/' + file
  }

  getName(file) {
    const s = file.split('/')
    if (s.length > 0) {
      return s[s.length - 1]
    } else {
      return s
    }
  }
  getType(file) {
    const s = file.split('.')
    if (s.length > 0) {
      return s[s.length - 1]
    } else {
      return s
    }
  }

  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true
    });
  }

  async openFile(item) {
    debugger
    let url = ''
    let name = '預覽'
    if (item && item.path) {
      url = item.path
    }
    if (item && item.name) {
      name = item.name
    }
    // url = 'http://image.woshipm.com/wp-files/img/58.jpg'
    // url = 'http://guangdong.chinatax.gov.cn/gdsw/etaxxzzq/2019-05/13/39414a73adf1475fb8801adb83895a68/files/d8366cf8b55549949ec52c812d5926df.doc'
    if (url) {
      // this.photoViewer.show(url, name, this.options);
      // this.previewAnyFile.preview(url)
      //   .then((res: any) => console.log(res))
      //   .catch((error: any) => console.error(error));

      
      // try {
      //   await this.loadingService.start()
      //   const res = await this.openFileService.download(url)
      //   console.log(res)
      //   if (res) {
      //     this.openFileService.openFile(res.nativeURL)
      //   }
      // } catch (e) {
      //   this.utils.showToast({ msg: '打開文件失敗，請重試' })
      //   console.error(e)
      // }
      // await this.loadingService.end()

      this.openFileService.showFile(url)
    }
  }


  getFileIconClass(type) {
    const className = this.fileClass[type]
    if (className) {
      return 'iconfont icon-' + className
    }
    return 'iconfont icon-file'
  }
}
