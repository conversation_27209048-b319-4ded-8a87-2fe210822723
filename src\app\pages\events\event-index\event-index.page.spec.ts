import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { EventIndexPage } from './event-index.page';

describe('EventIndexPage', () => {
  let component: EventIndexPage;
  let fixture: ComponentFixture<EventIndexPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EventIndexPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EventIndexPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
