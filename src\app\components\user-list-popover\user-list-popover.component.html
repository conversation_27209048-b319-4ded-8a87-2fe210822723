
<ion-list *ngIf="userList && userList.length > 0; else emptyTips">
  <!--<ion-item>{{ userName }}</ion-item>-->
  <ion-item
      *ngFor="let item of userList; let i = index"
      [lines]="i === userList.length - 1 ? 'none' : 'full'"
      detail="false"
      button
      (click)="onSelect(item)">
    <ion-label>{{ item.username }}</ion-label>
    <ion-icon slot="end" icon="close" (click)="onDelete(item)"></ion-icon>
  </ion-item>
</ion-list>
<ng-template #emptyTips>
  <!--<div class="page-empty-tips"></div>-->
  <!--<div class="empty-tips">-->
  <!--  -->
  <!--</div>-->
  <ion-list>
    <!--<ion-item>{{ userName }}</ion-item>-->
    <ion-item lines="none" detail="false">
      <ion-label class="empty-tips" style="height: 1rem;color: #9e9e9e;">未登錄</ion-label>
    </ion-item>
  </ion-list>
</ng-template>
