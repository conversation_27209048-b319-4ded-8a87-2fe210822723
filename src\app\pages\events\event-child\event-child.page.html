<ion-header class="inside-page">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button></ion-back-button>
    </ion-buttons>
    <ion-title>子任務</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-list *ngIf="data.length > 0; else emptyTips">
    <ion-item-group class="list">
      <ng-container
        *ngFor="let item of data"
      >
        <ion-item lines="full" class="ion-activatable" (click)="item._show = !item._show">
          <ion-label >
            <i [classList]="'dot ' + getStatusClass(item.status)"></i>
            {{ item.name }}</ion-label>
          <ion-text class="datetime">{{ formatDate(item.begin_time) }}</ion-text>
          <ion-icon [hidden]="item._show" slot="end" name="ios-arrow-forward"></ion-icon>
          <ion-icon [hidden]="!item._show" slot="end" name="ios-arrow-down"></ion-icon>
          <ion-ripple-effect></ion-ripple-effect>
        </ion-item>
        <ion-item-group
            *ngIf="item._show"
            class="detail"
        >
          <ion-item button detail (click)="onViewName(item.name)">
            <ion-label position="fixed">內容</ion-label>
            <ion-text>{{ item.name }}</ion-text>
          </ion-item>
          <ion-item>
            <ion-label position="fixed">執行人</ion-label>
            <ion-text>{{ item.nickname }}</ion-text>
          </ion-item>
          <ion-item>
            <ion-label position="fixed">任務開始時間</ion-label>
            <ion-text>{{ item.begin_time }}</ion-text>
          </ion-item>
          <ion-item>
            <ion-label position="fixed">任務結束時間</ion-label>
            <ion-text>{{ item.end_time }}</ion-text>
          </ion-item>
          <ion-item>
            <ion-label position="fixed">狀態</ion-label>
            <ion-text>{{ getStatus(item.status) }}</ion-text>
          </ion-item>
          <ion-item>
            <ion-label position="fixed">完成時間</ion-label>
            <ion-text>{{ item.done_time }}</ion-text>
          </ion-item>
          <ion-item button detail (click)="onViewRemark(item.remark)">
            <ion-label position="fixed">備註</ion-label>
            <ion-text>{{ item.remark }}</ion-text>
          </ion-item>
        </ion-item-group>

      </ng-container>
    </ion-item-group>
  </ion-list>
  <ng-template #emptyTips>
    <div class="page-empty-tips"></div>
  </ng-template>

</ion-content>
