import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TabTeacherPage } from './tab-teacher.page';

describe('TabTeacherPage', () => {
  let component: TabTeacherPage;
  let fixture: ComponentFixture<TabTeacherPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TabTeacherPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TabTeacherPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
