# ECA-App

# 初始化


### IOS 初始化 
> Google APi 需設置3個設備
> 1. Android ID : *Client ID*
> 2. IOS ID: *IOS URL scheme*
> 3. WEB ID: *Client ID*

> PS: IOS打包要xcode 11以上
```
// 1.安裝依賴
npm install
// 2.先確認 /Plugins/cordova-plugin-googleplus/src/ios/GooglePlus.m文件的第47行serverClientID是否正確
// 2.安裝 cordova-plugin-googleplus 
cordova plugin add cordova-plugin-googleplus --save --variable REVERSED_CLIENT_ID=com.googleusercontent.apps.991336545500-d6l6ci5dgarfllds5j1fseivg9dssh9d --variable WEB_APPLICATION_CLIENT_ID=991336545500-fcsk9ecre7clog8hplu7qarju65vnmg4.apps.googleusercontent.com --variable GOOGLE_SIGN_IN_VERSION="~> 6.2.1"
// 3.生成ios平台
ionic cordova platform add ios (會有報錯)
// 4.刪除此時platforms/ios/CKT EM/resources/下的 GoogleService-Info.plist
// 5.執行cordova plugin add cordova-plugin-firebasex --variable FIREBASE_ANALYTICS_COLLECTION_ENABLED=false --variable FIREBASE_PERFORMANCE_COLLECTION_ENABLED=false --variable FIREBASE_CRASHLYTICS_COLLECTION_ENABLED=false
// 5.再執行 ionic cordova prepare ios
// 6.打包好後。用xcode打開ios項目，確認Resoureces/下的GoogleService-Info資料是否正確
// 7.再確認Plugins下的googlePlus文件47行的serverClientID是否正確
// pod
sudo arch -x86_64 gem install ffi
arch -x86_64 pod install
// 真機運行
// Edit Scheme -> Build Settings -> Runpath Search Paths 添加 
/usr/lib/swift
// 打包
ionic cordova build ios --prod

版本號
1.2.1
12100
```

### Android 打包
```
// 1.安裝依賴
npm install
// 2.添加Android平台
ionic cordova platform add android
// 3.添加Google插件
cordova plugin add cordova-plugin-googleplus --save --variable REVERSED_CLIENT_ID=991336545500-jg7h2plh1ivud5k2fbcrvdq20dqrjsma.apps.googleusercontent.com --variable WEB_APPLICATION_CLIENT_ID=991336545500-fcsk9ecre7clog8hplu7qarju65vnmg4.apps.googleusercontent.com

// 5.編譯代碼
ionic cordova prepare android

// 5.1 解決Execution failed for task ':app:mergeReleaseResources'.報錯
\platforms\android\CordovaLib\build.gradle
android {
    defaultConfig {
        aaptOptions.cruncherEnabled = false
        aaptOptions.useNewCruncher = false
    }
}


// 6.5 生成APK前，需刪除定位權限（如有）
> AndroidManifest.xml
> android.permission.ACCESS_COARSE_LOCATION
> android.permission.ACCESS_FINE_LOCATION
> android.permission.ACCESS_BACKGROUND_LOCATION

> android.permission.REQUEST_INSTALL_PACKAGES

> 移除com.google.android.gms.permission.AD_ID
<manifest> 增加 xmlns:tools="http://schemas.android.com/tools"
<uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>


// 7.打包
ionic cordova build android --prod --release


8.簽名
> 文件：key0-NorrayHK.jks
> 密碼：NorrayHK
> alias：key0
> alias password: NorrayHK
// 進入未簽名的APK文件目錄
// 簽名
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore eca-release-key.keystore app-release-unsigned.apk alias_name
// 優化
zipalign -v 4 app-release-unsigned.apk eca-app.apk


版本號
1.2.1
10201
```

## 通用搭建
```
npm install -g cordova ionic
ionic start myApp tabs
cd myApp
// ionic serve

// 0.安裝依賴
npm install

// 1.添加平台
ionic cordova platform add android
// ionic cordova platform add ios
// 2.編譯
ionic cordova prepare android
// ionic cordova prepare ios
// 3.測試運行
ionic cordova run android -l
// ionic cordova run ios -l --address=0.0.0.0

// 4.android 打包
ionic cordova build android --prod --release
keytool -genkey -v -keystore my-release-key.keystore -alias alias_name -keyalg RSA -keysize 2048 -validity 10000
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my-release-key.keystore HelloWorld-release-unsigned.apk alias_name
zipalign -v 4 HelloWorld-release-unsigned.apk HelloWorld.apk




```

## IOS 命令
```
npm i
ionic capacitor add ios
ionic cordova prepare ios



// ./platforms/ios/ 打開 .xcworkspace 文件



// --------------- capacitor ---------------
// 添加
ionic capacitor add ios

// Xcode 打開項目
ionic capacitor open ios


// --------------- cordova ---------------
// 添加
ionic cordova prepare ios

// 打開項目
// 打開platforms/ios文件夾

// 調試
ionic cordova run ios -l --address=0.0.0.0

// 發佈
ionic cordova build ios --prod


```


## 獲取簽名
```javascript

keytool -printcert -jarfile app.apk

```


### 簽名腳本
```
echo 123456|jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my-release-key.keystore app-release-unsigned.apk alias_name
del app.apk
zipalign -v 4 app-release-unsigned.apk app.apk
adb install app.apk
adb shell am start -n com.norrayhk.edsystem/com.norrayhk.edsystem.MainActivity

RELEASE_KEY_PASSWORD=norrayhk2020ecaapp
RELEASE_KEY_ALIAS=alias_name
RELEASE_STORE_PASSWORD=norrayhk2020ecaapp
RELEASE_STORE_FILE=F\:\\program\\ECA-App\\eca-release-key.keystore
```

### 桌面調試
```
ionic cordova platform add browser
ionic cordova run browser   //  使用下方語句更好,可以熱更新調試
ionic serve --cordova --platform browser --address=0.0.0.0
```



### Google 
#### Android
```

WEB_APPLICATION_CLIENT_ID=991336545500-fcsk9ecre7clog8hplu7qarju65vnmg4.apps.googleusercontent.com

Android - 10:B7:25:13:10:5D:34:6C:31:5E:5F:CF:20:96:40:80:12:EC:86:AC
991336545500-jg7h2plh1ivud5k2fbcrvdq20dqrjsma.apps.googleusercontent.com
GooglePlay - E0:1F:C8:95:DD:AD:EE:90:93:60:F5:26:4D:E8:45:BF:B7:CC:7B:1E
991336545500-4ftqeu0iv37gau74685p2jdc8rh0b0ir.apps.googleusercontent.com

cordova plugin add cordova-plugin-googleplus --save --variable REVERSED_CLIENT_ID=991336545500-jg7h2plh1ivud5k2fbcrvdq20dqrjsma.apps.googleusercontent.com --variable WEB_APPLICATION_CLIENT_ID=991336545500-fcsk9ecre7clog8hplu7qarju65vnmg4.apps.googleusercontent.com


```
#### IOS
```
WEB_APPLICATION_CLIENT_ID=991336545500-fcsk9ecre7clog8hplu7qarju65vnmg4.apps.googleusercontent.com

com.googleusercontent.apps.991336545500-d6l6ci5dgarfllds5j1fseivg9dssh9d

cordova plugin add cordova-plugin-googleplus --save --variable REVERSED_CLIENT_ID=com.googleusercontent.apps.991336545500-d6l6ci5dgarfllds5j1fseivg9dssh9d --variable WEB_APPLICATION_CLIENT_ID=991336545500-fcsk9ecre7clog8hplu7qarju65vnmg4.apps.googleusercontent.com --variable GOOGLE_SIGN_IN_VERSION="~> 6.2.1"
```
