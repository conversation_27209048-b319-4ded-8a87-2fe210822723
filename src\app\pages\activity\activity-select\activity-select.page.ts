import {Component, OnInit, ViewChild} from '@angular/core';
import {IonContent, IonInfiniteScroll, IonList, ModalController, NavController} from '@ionic/angular';
import {UtilsService} from '@app/utils/utils.service';
import {NoticesService} from '@services/api/notices.service';
import {ActivityService} from '@services/api/activity.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {SelectStudentService} from '@services/utils/select-student.service';
import {Router} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import {dateFormat} from '@app/utils';

@Component({
  selector: 'app-activity-select',
  templateUrl: './activity-select.page.html',
  styleUrls: ['./activity-select.page.scss'],
})
export class ActivitySelectPage implements OnInit {

  @ViewChild('infiniteScrollActivity', {}) infiniteScrollActivity: IonInfiniteScroll;
  @ViewChild('listActivity', {}) listActivity: IonList;
  @ViewChild('contentActivity', {}) contentActivity: IonContent;
  // 活動
  public activityFilter: any = {
    actCodeOrName: '',
    actTerm: '',
    actFinish: '',
    actCategory: '',
    actExpire: '',
    page_index: 1,
    page_size: 20
  }
  public total_count_activity = 0
  public listDataActivity: any = []
  public categorys: any = []
  public user: any = {}
  public personal = '1'
  constructor(
    private modalCtrl: ModalController,
    private utils: UtilsService,
    private notices: NoticesService,
    public activityService: ActivityService,
    private nativeStorage: NativeStorage,
    public selectStudentService: SelectStudentService,
    private navCtrl: NavController,
    private router: Router,
    private loadingService: LoadingService,
    ) { }

  get actExpireStr() {
    console.log('actExpireStr', this.activityFilter.actExpire)
    if (this.activityFilter.actExpire) {
      return dateFormat(new Date(this.activityFilter.actExpire))
    }
    return '- -'
  }
  async ngOnInit() {
    this.user = await this.nativeStorage.getItem('userInfo')
    if (this.user && this.user.access) {
      if (this.user.access.G01 && this.user.access.G01.length > 0) {
        this.personal = this.user.access.G01.substr(8, 1) || '0'
      }
    }
    this.initCategory()
  }


  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true
    });
  }


  async initCategory() {


    try {
      const res: any = await this.activityService.getAllCategory({})
      console.log(res)
      this.categorys = res
    } catch (e) {
      console.error(e)
    }
  }

  onSearchKeyUp(event) {
    if ('Enter' === event.key) {
      this.loadDataActivity()
    }
  }

  async loadDataActivity(event?, isNew = true, isRefresh = false) {
    await this.loadingService.start()
    let empty = false
    try {
      const { actTerm, actCodeOrName, actFinish, actCategory, actExpire, page_index } = this.activityFilter
      let page = page_index
      if (isNew) {
        page = 1
        this.activityFilter.page_index = 1
        this.toggleInfiniteScrollActivity(false)
      }
      let user
      try {
        user = await this.nativeStorage.getItem('userInfo')
      } catch (e) {
        await this.loadingService.end()
        this.utils.showMsg('登錄失效，請重新登錄')
        if (event && event.target) {
          event.target.complete();
        }
        return
      }

      let expire = ''

      if (actExpire) {
        const d = new Date(actExpire)
        expire = dateFormat(d, 'yyyy-MM-dd')
      }

      const actYear = user.year_id
      // const res: any = await this.eventsService.searchEvents({
      //   keyword, system_id, status, urgent, only_mine,
      //   page_index, page_size, with_hide_data
      // })

      let actCreator = ''
      if (this.personal === '1') {
        actCreator = this.user.user_id
      }
      const res: any = await this.activityService.fetchActivitys({
        actYear,
        actTerm,
        actCodeOrName,
        actFinish,
        actCategory,
        actExpire: expire,
        actCreator,
        page,
        hideInvalidActivity: '1'
      })
      console.log(res)
      if (isNew) {
        this.listDataActivity = res.datas
      } else {
        this.listDataActivity.push(...res.datas)
      }

      this.total_count_activity = res.total_count
    } catch (e) {
      if (e && e.code) {
        if (e.code === 3002) {
          empty = true
          // await this.utils.showMsg(e.desc)
          if (isNew) {
            this.listDataActivity = []
          }
        }
      }
      console.error(e)
    }
    if (event && event.target) {
      event.target.complete();
      if (!isRefresh) { // !isRefresh 下拉刷新不需要禁用
        event.target.disabled = empty || (this.activityFilter.page_index * this.activityFilter.page_size) >= this.total_count_activity
      }
    } else {
      this.toggleInfiniteScrollActivity(empty || (this.activityFilter.page_index * this.activityFilter.page_size) >= this.total_count_activity)
    }
    if (isNew) {
      this.contentActivity.scrollToTop()
    }
    this.activityFilter.page_index += 1
    await this.loadingService.end()
  }
  toggleInfiniteScrollActivity(disabled) {
    console.log(disabled)
    this.infiniteScrollActivity.disabled = disabled
  }

  onCancelActExpire(actDatetime) {
    this.activityFilter.actExpire = undefined
    // actDatetime.reset()
    console.log('this.activityFilter.actExpire', this.activityFilter.actExpire)
  }

  getStatusClass(status, actType) {
    switch (status) {
      case '0':
        return 'warning'
      case '2':
        return 'error'
      case '3':
        return 'success'
      case '4':
        return 'warning'
      case '4_1':
        return 'warning'
      case '4_2':
        return 'warning'
      case '5':
        return 'warning'
      case '5_1':
        return 'warning'
      case '5_2':
        if (actType === '5' || actType === '3') {
          return 'success'
        } else {
          return 'warning'
        }
      case '5_3':
        return 'warning'
      default:
        return 'error'
    }
  }

  getStatus(status) {
    switch (status) {
      case '0':
        return '審核中'
      case '2':
        return '不通過'
      case '3':
        return '已發通告'
      case '4_1':
        return '篩選中'
      case '4_2':
        return '開始篩選'
      case '5_1':
        return '完成篩選'
      default:
        return '未知'
    }
  }

  onSelect(item) {
    this.modalCtrl.dismiss({
      dismissed: true,
      activity: item,
      save: true
    });
  }



}
