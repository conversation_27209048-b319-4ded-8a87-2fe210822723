import { Component, OnInit } from '@angular/core';
import { StoreService } from '@services/store/store.service';
import {Router} from '@angular/router';
import {NavController, PopoverController} from '@ionic/angular';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {TranslateService} from '@ngx-translate/core';
import { GooglePlus } from '@ionic-native/google-plus/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {GoogleService} from '@services/api/google.service';
import {LoadingService} from '@app/utils/loading.service';
import to from 'await-to-js'

@Component({
  selector: 'app-user-popover',
  templateUrl: './user-popover.component.html',
  styleUrls: ['./user-popover.component.scss'],
})
export class UserPopoverComponent implements OnInit {

  public user: any = {}
  constructor(
    private store: StoreService,
    private router: Router,
    private navCtrl: NavController,
    public popoverController: PopoverController,
    private nativeStorage: NativeStorage,
    public translate: TranslateService,
    private googlePlus: GooglePlus,
    private googleService: GoogleService,
    private utils: UtilsService,
    private loadingService: LoadingService,
  ) { }

  async ngOnInit() {
    this.user = await this.nativeStorage.getItem('userInfo')
  }
  get isEng() {
    return this.translate.currentLang === 'en'
  }

  get userName() {
    if (this.user && this.user.name_cn) {
      return this.user.name_cn
    }
    return '-'
  }
  get isStudent() {
    if (this.user) {
      return this.user.user_type_m === 'S'
    }
    return true
  }
  onClose() {
    this.popoverController.dismiss()
  }
  async onLogout() {
    console.log('onLogout 1')
    await this.loadingService.start()
    this.onClose()
    console.log('onLogout 2')
    try {
      await this.store.Logout()
    } catch (e) {
      console.error(e)
    }
    await this.loadingService.end()
  }
  onSwitchAccount() {
    this.onClose()
    this.navCtrl.setDirection('forward');
    this.router.navigate(['login'], { queryParams: { pageType: 'switch' }})
  }
  onRollCall() {
    this.navCtrl.setDirection('forward');
    this.router.navigate(['offline-roll-call-list'])
    this.onClose()
  }
  async onSync() {
    console.log('onSync 1')
    this.onClose()
    console.log('onSync 2')
    await this.loadingService.start()
    console.log('onSync 3')
    // this.googlePlus.getSigningCertificateFingerprint().then(res => {
    //   console.log(res)
    //   this.utils.showMsg(res)
    // }).catch(err => {
    //   console.error(err)
    //   this.utils.showMsg(err)
    // })
    try {
      console.log('onSync 4')
      await this.googlePlus.logout()
    } catch (e) {
      console.log('onSync 5')
      console.error(e)
    }
    let err, googleResult
    [err, googleResult] = await to(this.googlePlus.login({
      scopes: 'https://www.googleapis.com/auth/calendar.events',
      webClientId: '991336545500-fcsk9ecre7clog8hplu7qarju65vnmg4.apps.googleusercontent.com',
      offline: true,
    }))
    console.log('googleResult', googleResult)
    if (!googleResult) {
      // 授權失敗
      await this.loadingService.end()
      if ( typeof err === 'string' && err.toLowerCase() === 'The user canceled the sign-in flow.'.toLowerCase()) {
        this.utils.showMsg(this.translate.instant('GOOGLE.USER_CANCEL'))
        return
      }
      switch (err) {
        case 7:
          this.utils.showMsg(this.translate.instant('GOOGLE.NETWORK_ERROR'))
          break
        case 10:
          this.utils.showMsg(this.translate.instant('GOOGLE.APP_ERROR'))
          break
        case 12501:
          this.utils.showMsg(this.translate.instant('GOOGLE.USER_CANCEL'))
          break
        default:
          this.utils.showMsg(this.translate.instant('GOOGLE.SYNC_FAILED')) //  + '\n code:' + err
          break
      }
      console.error(err)
      return
    }
    console.log('googleResult.serverAuthCode', googleResult.serverAuthCode)
    if (googleResult && googleResult.serverAuthCode) {
      // 授權成功
      let updateRes, asyncRes
      [err, updateRes] = await to(this.googleService.updateCode(googleResult.serverAuthCode))
      console.log('updateRes', updateRes, '--')
      if (!updateRes) {
        // 記錄google授權 code 失敗
        this.loadingService.end()
        if (err && err.status) {
          if (err.status === -4) {
            this.utils.showMsg(this.translate.instant('GOOGLE.TIME_OUT'))
            return
          }
        }
        // this.utils.showMsg(googleResult.serverAuthCode)
        this.utils.showMsg(this.translate.instant('GOOGLE.LOGIN_FAILED')) // + '【1】' + JSON.stringify(err)
        return
      }
      [err, asyncRes] = await to(this.googleService.syncGoogleCAlendar(this.user.user_id))
      if (!asyncRes) {
        // 同步用戶的Google Calendar 失敗
        this.loadingService.end()
        if (err && err.status) {
          if (err.status === -4) {
            this.utils.showMsg(this.translate.instant('GOOGLE.TIME_OUT'))
            return
          }
        }
        this.utils.showMsg(this.translate.instant('GOOGLE.LOGIN_FAILED')) //  + '【2】' + JSON.stringify(err)
        return
      }
      // 同步用戶成功
      this.loadingService.end()
      this.utils.showMsg(this.translate.instant('GOOGLE.LOGIN_SUCCESS'))
    } else {
      await this.loadingService.end()
      this.utils.showMsg(this.translate.instant('GOOGLE.LOGIN_FAILED')) //  + '【3】'  + JSON.stringify(googleResult)
    }
    /*
    this.googlePlus.login({
      scopes: 'https://www.googleapis.com/auth/calendar.events',
      webClientId: '991336545500-fcsk9ecre7clog8hplu7qarju65vnmg4.apps.googleusercontent.com',
      offline: true,
    })
      .then(async res => {
        // accessToken: "***************************************************************************************************************************************************************************"
        // expires: **********
        // expires_in: 3594
        // email: "<EMAIL>"
        // idToken: "eyJhbGciOiJSUzI1NiIsImtpZCI6Ijc0YmQ4NmZjNjFlNGM2Y2I0NTAxMjZmZjRlMzhiMDY5YjhmOGYzNWMiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.h7YE82B92uulNJvlVgj_X8SC7dALjIo71QfECpVV-qTemEWqL411n4GDQkymTujRa5tb4nVUl8BaLWfhF_GZVswlihKOdUvL-oBeG4qxlHgdofAA8cSLmSZt4viqprRhgF04a8tWqvLoDYwSl2onR7_OZY61Br1IJrGGgipyapX-3IF3BLLuVza-ZtwD8j4YynRiW1_iCkTBQOIRFUaE3hT5zTyOCxleAeqFwIa8uCgJgBmBFgRe4fuY3H_9mWeIUi_fJJxwTrRUzKyBu4cUGTdz0ZG9VTpEBysFEiuEcAoCJSqYCGAo90mnwNe-GFOZ5wF1mVMNzNrZf6yXzEOVwg"
        // serverAuthCode: "4/zQHiH7aW3xL6bhcwOtnMY5WcS6LoB7dbSt9jlKftCRgFco4dQesxFAv8vFCGy277cYmQYucaMRZmKZfIa6dhjD0"
        // userId: "106111416178565889303"
        // displayName: "test pe"
        // familyName: "pe"
        // givenName: "test"
        // imageUrl: "https://lh4.googleusercontent.com/-Bz7SvyYz268/AAAAAAAAAAI/AAAAAAAAAAA/AAKWJJOnhxLXmT7JncTNhgaSut293IxE2g/s96-c/photo.jpg"
        if (res && res.serverAuthCode) {
          this.googleService.updateCode(res.serverAuthCode).then(() => {
            // console.log(res)
            return this.googleService.syncGoogleCAlendar(this.user.user_id)

          }).then(() => {
            this.utils.showMsg(this.translate.instant('GOOGLE.LOGIN_SUCCESS'))
          })


            .catch(err => {
              if (err && err.status) {
                if (err.status === -4) {
                  this.utils.showMsg(this.translate.instant('GOOGLE.TIME_OUT'))
                } else {
                  this.utils.showMsg(this.translate.instant('GOOGLE.LOGIN_FAILED' + '【1】' + JSON.stringify(err)))
                }
              } else {
                this.utils.showMsg(this.translate.instant('GOOGLE.LOGIN_FAILED' + '【2】'  + JSON.stringify(err)))
              }
              console.log(err)

          }).finally(async () => {
            this.loadingService.end()
          })
        } else {
          await this.loadingService.end()
          this.utils.showMsg(this.translate.instant('GOOGLE.LOGIN_FAILED' + '【3】'  + JSON.stringify(res)))
        }
      })
      .catch(async err => {
        await this.loadingService.end()
        if ( typeof err === 'string' && err.toLowerCase() === 'The user canceled the sign-in flow.'.toLowerCase()) {
          this.utils.showMsg(this.translate.instant('GOOGLE.USER_CANCEL'))
          return
        }
        switch (err) {
          case 7:
            this.utils.showMsg(this.translate.instant('GOOGLE.NETWORK_ERROR'))
            break
          case 10:
            this.utils.showMsg(this.translate.instant('GOOGLE.APP_ERROR'))
            break
          case 12501:
            this.utils.showMsg(this.translate.instant('GOOGLE.USER_CANCEL'))
            break
          default:
            this.utils.showMsg(this.translate.instant('GOOGLE.SYNC_FAILED') + '\n code:' + err)
            break
        }
        console.error(err)
        // 12501 拒絕授權
      });
     */
  }
  onUserInfo() {
    this.onClose()
    if (this.isStudent) {
      this.router.navigate(['student-profile'])
    } else {
      this.router.navigate(['teacher-profile'])
    }
  }
}
