import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { ActivitySignUpInputPage } from './activity-sign-up-input.page';

const routes: Routes = [
  {
    path: '',
    component: ActivitySignUpInputPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  // declarations: [ActivitySignUpInputPage]
})
export class ActivitySignUpInputPageModule {}
