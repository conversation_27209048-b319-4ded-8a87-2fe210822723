import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StudentStarInputPage } from './student-star-input.page';

describe('StudentStarInputPage', () => {
  let component: StudentStarInputPage;
  let fixture: ComponentFixture<StudentStarInputPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ StudentStarInputPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StudentStarInputPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
