import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StudentNoticePage } from './student-notice.page';

describe('StudentNoticePage', () => {
  let component: StudentNoticePage;
  let fixture: ComponentFixture<StudentNoticePage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ StudentNoticePage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StudentNoticePage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
