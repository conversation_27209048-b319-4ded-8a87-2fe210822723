<ion-header class="inside-page none-border">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button ></ion-back-button>
    </ion-buttons>
    <ion-title>{{ title }}</ion-title>
    <!--<ion-buttons slot="end">-->
    <!--  <ion-button [hidden]="!showDown || !canSelect" (click)="onDown()">-->
    <!--    {{ 'ACTIVITY.DETAIL.DOWN' | translate }}-->
    <!--  </ion-button>-->
    <!--</ion-buttons>-->
  </ion-toolbar>
  <div class="tabs none-border">
    <!--<ion-toolbar>-->
    <ion-segment [(ngModel)]="tab" (ionChange)="segmentChanged($event)">
      <ion-segment-button value="activity">
        <ion-label>{{ 'ACTIVITY.DETAIL.ACTIVITY' | translate }}</ion-label>
      </ion-segment-button>
      <ion-segment-button value="vn">
        <ion-label>{{ 'ACTIVITY.DETAIL.VENUE_AND_TIME' | translate }}</ion-label>
      </ion-segment-button>
    </ion-segment>
    <!--</ion-toolbar>-->
  </div>
</ion-header>

<ion-content *ngIf="data && data.activity" [hidden]="tab !== 'activity'">
  <div class="activity-title">{{ actCode }} | {{ actTeacher }} | {{ actType }}</div>

  <!-- 日期 -->
  <app-info-label *ngIf="'167'.includes(data.activity.actType)" title="日期">
    <table class="info-table">
      <tr *ngIf="'16'.includes(data.activity.actType)">
        <td>{{ 'ACTIVITY.DETAIL.ACT_EXPIRE1' | translate }}</td>
        <td>{{ data.activity.actExpire }}</td>
      </tr>
      <tr *ngIf="'7' === data.activity.actType">
        <td>{{ 'ACTIVITY.DETAIL.ACT_EXPIRE2' | translate }}</td>
        <td>{{ data.activity.actExpire2 }}</td>
      </tr>
      <tr *ngIf="'16'.includes(data.activity.actType)">
        <td>{{ 'ACTIVITY.DETAIL.ACT_PUBLISH' | translate }}</td>
        <td>{{ data.activity.actPublish }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 資助 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_SUPPORT') + ' (活動費用: ' + data.activity.actMoney + ')' ">
    <table class="info-table">
      <tr *ngFor="let item of data.actSupport">
        <td>{{ item }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 名額 -->
  <app-info-label *ngIf="data.activity.actQuota" [title]="translate.instant('ACTIVITY.DETAIL.ACT_QUOTA')">
    <table class="info-table">
      <tr >
        <td>{{ data.activity.actQuota }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 歸程方法 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_BACK')">
    <table class="info-table">
      <tr *ngFor="let item of data.actBack">
        <td>{{ item }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 備註 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_REMARK')">
    <table class="info-table">
      <tr *ngFor="let item of data.activityWT">
        <td>{{ item.actPlaceRemark }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 項目 -->
  <app-info-label *ngIf="showProject" [title]="translate.instant('ACTIVITY.DETAIL.ACT_PROJECT')">
    <table class="info-table">
      <tr *ngFor="let item of data.actPjC">
        <td>{{ item }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 活動描述 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_DESC')">
    <table class="info-table">
      <tr >
        <td>{{ data.activity.actRemark }}</td>
      </tr>
    </table>
  </app-info-label>
  <!-- 招生通告 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_FILE1')" *ngIf="'1356'.includes(data.activity.actType)">
    <table class="info-table">
      <tr >
        <td>
          <i *ngIf="data.activity.actFile1" class="iconfont icon-PDF" (click)="openFile(data.activity.actFile1)"></i>
        </td>
      </tr>
    </table>
  </app-info-label>
  <!-- 取錄通告 -->
  <app-info-label [title]="translate.instant('ACTIVITY.DETAIL.ACT_FILE2')" *ngIf="'13567'.includes(data.activity.actType)">
    <table class="info-table">
      <tr >
        <td>
          <i *ngIf="data.activity.actFile2" class="iconfont icon-PDF" (click)="openFile(data.activity.actFile2)"></i>
        </td>
      </tr>
    </table>
  </app-info-label>

</ion-content>

<ion-content *ngIf="data && data.activityWT" [hidden]="tab !== 'vn'" class="vn-content">
  <div *ngIf="stuLottery && stuLottery.back" class="stu-back">
    {{ 'ACTIVITY.DETAIL.ACT_BACK' | translate }}： {{ stuLottery.back }}
  </div>
  <div
      class="vn-box"
      *ngFor="let item of data.activityWT"
  >
    <div class="vn-title">{{ item.actOtherPlace }} | {{ item.actStartTime }}~{{ item.actEndTime }}</div>
    <div class="vn-date-box">
      <div class="vn-year" *ngFor="let year of item.dateArr">
        <div class="year-label">{{ year.year }}</div>
        <div *ngFor="let month of year.months; let mi = index">
          <div class="divide" *ngIf="mi !== 0"></div>
          <div class="vn-month">
            <div class="vn-date" *ngFor="let day of month">
              {{ day.dateCn }}
              <div class="vn-week">{{ day.week}}</div>
              <span class="dot" *ngIf="day.isToday"></span>
              <!--<ion-ripple-effect type="bounded"></ion-ripple-effect>-->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ion-content>
<ion-tab-bar *ngIf="showAction && data.activity" slot="bottom">
  <ion-button [disabled]="!canAgree" class="tab-button" color="green" (click)="onAgree()">{{ data.activity.actFirstRemark}}</ion-button>
  <ion-button [disabled]="!canNotAgree"  class="tab-button" color="yellow" (click)="onNotAgree()">{{ data.activity.actSecRemark}}</ion-button>
</ion-tab-bar>
