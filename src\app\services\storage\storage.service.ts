import {Injectable} from '@angular/core'
import {IsDebug} from '@ionic-native/is-debug/ngx'
import {Platform} from '@ionic/angular'

@Injectable({
  providedIn: 'root'
})
export class StorageService {

  public serverSetting = {
    /* 2020/10/* */
    // https://em.plkcktps.edu.hk/nyschool_plkckt/web/api/doc

    http: 'https',
    port: '443',
    serverName: 'em.plkcktps.edu.hk',
    ip: 'em.plkcktps.edu.hk',
    projectRoot: 'nyschool_plkckt',
    remoteProjectName: 'nyschool_plkckt',
    // https://em.plkcktps.edu.hk/plkckt/activity/web/login/index
    web_http: 'https',
    web_port: '443',
    web_ip: 'em.plkcktps.edu.hk',
    web_projectRoot: 'plkckt/activity',
    web_remoteProjectName: 'plkckt/activity',
    web_uri: 'web',

    star_http: 'https',
    star_port: '443',
    star_serverName: 'em.plkcktps.edu.hk',
    star_ip: 'em.plkcktps.edu.hk',
    star_projectRoot: 'award-api',
    star_remoteProjectName: 'award-api',
    star_uri: 'public/api',


    /* 215 */
    // https://www.schoolsoftware.hk/website/nyschool_plkckt/web/api/doc
    // http://**************/ed-series/website/nyschool_plkckt/web/api/doc

    // http: 'http',
    // port: '80',
    // serverName: '**************',
    // ip: '**************',
    // projectRoot: 'ed-series/website/nyschool_plkckt',
    // remoteProjectName: 'ed-series/website/nyschool_plkckt',

    // https://www.schoolsoftware.hk/website/nyschool_plkckt/web/api/doc
    // http: 'https',
    // port: '443',
    // serverName: 'www.schoolsoftware.hk',
    // ip: 'www.schoolsoftware.hk',
    // projectRoot: 'website/nyschool_plkckt_test',
    // remoteProjectName: 'website/nyschool_plkckt_test',
    //
    // projectRoot: 'website/nyschool_plkckt',
    // remoteProjectName: 'website/nyschool_plkckt',

    uri: 'web',
    storage: 'web',

    // web_http: 'http',
    // web_ip: 'web.demo.esaccount.com',
    // web_port: '80',
    // web_projectRoot: 'EM-Web-PLKCKT',
    // web_remoteProjectName: 'EM-Web-PLKCKT',
    // web_uri: 'web',

    // http://test.esaccount.com/nyschool_plkckt/web/api/doc
    // http: 'http',
    // port: '80',
    // serverName: '*************',
    // ip: '*************',
    // serverName: 'test.esaccount.com',
    // ip: 'test.esaccount.com',
    // projectRoot: 'nyschool_plkckt',
    // remoteProjectName: 'nyschool_plkckt',
    // uri: 'web',
    // storage: 'web',

    /*http: 'http',
    port: '80',
    serverName: '*************',
    ip: '*************',
    projectRoot: 'plkckt-api',
    remoteProjectName: 'plkckt-api',
    web_http: 'http',
    web_port: '80',
    web_ip: '*************',
    web_projectRoot: 'plkckt/activity',
    web_remoteProjectName: 'plkckt/activity',
    web_uri: 'web',

    star_http: 'https',
    star_port: '443',
    star_serverName: 'www.hkschoolsoftware.com',
    star_ip: 'www.hkschoolsoftware.com',
    // star_serverName: '*************',
    // star_ip: '*************',
    star_projectRoot: 'website/plkckt-study-award-demo',
    star_remoteProjectName: 'website/plkckt-study-award-demo',
    star_uri: 'public/api',*/

    storageUrl: '',


    // rptHttp: 'http',
    // rptIp: 'report.esaccount.com',
    // rptPort: '80',
    // rptUrl: 'EDReport-LB/run',
    webCode: 'LB',
    version: '1.2.1',
  }

  public store: any = {
    language: '',
    device: '',
    // USER
    userId: '',
    avatar: '',
    name: '',
    name_cn: '',
    name_en: '',
    roles: [],
    level: '', // 系統角色
  }

  constructor(
    private isDebug: IsDebug,
    private platform: Platform
  ) {
    sessionStorage.setItem('language', 'zh-hk')
    // sessionStorage.setItem('device', 'mobile')
    sessionStorage.setItem('web_code', 'LB')
    this.platform.ready().then(() => {
      const platforms = this.platform.platforms()
      let device = 'Web'
      if (platforms.includes('android')) {
        device = 'Android'
      } else if (platforms.includes('ios')) {
        device = 'iOS'
      }
      // device = 'Web'
      sessionStorage.setItem('device', device)

      // this.serverSetting.serverName = this.serverSetting.ip = '*************'
      // this.serverSetting.projectRoot = this.serverSetting.remoteProjectName = 'EDSchoolCenter'
      this.isDebug.getIsDebug().then(isDebugging => {
        if (isDebugging) {
          // this.serverSetting.serverName = this.serverSetting.ip = '*************'
          // this.serverSetting.projectRoot = this.serverSetting.remoteProjectName = 'EDSchoolCenter'
        }
      })
    })
    // this.serverSetting.serverName = this.serverSetting.ip = 'test.esaccount.com'
    // this.serverSetting.projectRoot = this.serverSetting.remoteProjectName = 'EDSchoolCenter-LB'


    // setInterval(() => {
    //   this.store.school.time = new Date().getTime()
    // }, 100)

    // this.serverSetting.storageUrl =
    //   `${this.serverSetting.http}://${this.serverSetting.ip}:${this.serverSetting.port}` +
    //   `/${this.serverSetting.projectRoot}/${this.serverSetting.storage}`
    this.serverSetting.storageUrl =
      `${this.serverSetting.http}://${this.serverSetting.ip}:${this.serverSetting.port}` +
      `/${this.serverSetting.projectRoot}/${this.serverSetting.storage}`
  }

  set(key: any, value: any) {
    if (this.store.hasOwnProperty(key)) {
      this.store[key] = value
    }
    if (typeof value === 'object') {
      sessionStorage.setItem(key + '_object', '1')
      return sessionStorage.setItem(key, JSON.stringify(value))
    } else {
      sessionStorage.removeItem(key + '_object')
      return sessionStorage.setItem(key, value)
    }

  }

  get() {
    for (const key in this.store) {
      if (this.store.hasOwnProperty(key)) {
        if (sessionStorage.getItem(key + '_object') === '1') {
          this.store[key] = JSON.parse(sessionStorage.getItem(key))
        } else {
          this.store[key] = sessionStorage.getItem(key)
        }
      }
    }

    return this.store
  }
}
