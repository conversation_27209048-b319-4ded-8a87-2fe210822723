import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SessionSelectPage } from './session-select.page';

describe('SessionSelectPage', () => {
  let component: SessionSelectPage;
  let fixture: ComponentFixture<SessionSelectPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SessionSelectPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SessionSelectPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
