import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { NoticeAddPage } from './notice-add.page';
// import { NoticeStudentListPage } from '@pages/notices/notice-student-list/notice-student-list.page';
import { ActivitySelectPage } from '@pages/activity/activity-select/activity-select.page';
import { NoticeStudentListPageModule} from '@pages/notices/notice-student-list/notice-student-list.module';

import { CanDeactivateGuard } from '@app/utils/can-deactivate.guard';
const routes: Routes = [
  {
    path: '',
    component: NoticeAddPage,
    canDeactivate: [CanDeactivateGuard]
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    NoticeStudentListPageModule,

  ],
  declarations: [NoticeAddPage, ActivitySelectPage],
  entryComponents: [ActivitySelectPage],
  providers: [CanDeactivateGuard]
})
export class NoticeAddPageModule {}
