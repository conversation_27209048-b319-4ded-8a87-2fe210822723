import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { TeacherIndexPage } from './teacher-index.page';
import { CalendarModule } from 'ion2-calendar';
import { UserPopoverComponent } from '@app/components/user-popover/user-popover.component';
import {TranslateModule} from '@ngx-translate/core';

const routes: Routes = [
  {
    path: '',
    component: TeacherIndexPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    CalendarModule,
    // UserPopoverModule,
    ReactiveFormsModule,
    TranslateModule,
  ],
  declarations: [
    TeacherIndexPage,
    // UserPopoverComponent
  ],
  entryComponents: [
    // UserPopoverComponent
    ]
})
export class TeacherIndexPageModule {}
