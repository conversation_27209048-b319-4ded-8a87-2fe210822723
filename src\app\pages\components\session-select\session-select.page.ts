import { Component, Input, OnInit } from '@angular/core';
import {ModalController} from '@ionic/angular';
import {EBookingService} from '@services/api/ebooking.service';
import {LoadingService} from '@app/utils/loading.service';

@Component({
  selector: 'app-session-select',
  templateUrl: './session-select.page.html',
  styleUrls: ['./session-select.page.scss'],
})
export class SessionSelectPage implements OnInit {
  @Input() data: any
  @Input() lessonIds: any = []
  @Input() lessonIdList: any = []
  @Input() timeType = 'STD'
  @Input() ruleId = ''
  public list: any = []
  constructor(
    private modalCtrl: ModalController,
    private ebooking: EBookingService,
    private loadingService: LoadingService,
    ) { }

  ngOnInit() {
    // if (typeof this.lessonIds === 'string' && this.lessonIds.length > 0) {
    //   this.lessonIdList = this.lessonIds.split(',')
    // }
    if (Array.isArray(this.lessonIds)) {
      this.lessonIdList = this.lessonIds
    }
    this.loadData()
  }

  async loadData() {
    await this.loadingService.start()
    try {
      const res: any = await this.ebooking.getSessionList(this.timeType, this.ruleId)
      console.log(res)

      const list = res.timelist
      list.forEach(item => {
        item.check = this.lessonIdList.includes(item.id)
      })
      this.list = list
    } catch (e) {
      console.error(e)
    }
    await this.loadingService.end()
  }

  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true
    });
  }
  onSave() {
    const data = this.list.filter(i => i.check)
    this.modalCtrl.dismiss({
      dismissed: true,
      save: true,
      data
    });
  }

}
