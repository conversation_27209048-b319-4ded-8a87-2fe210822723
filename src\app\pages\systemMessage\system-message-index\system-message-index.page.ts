import {Component, OnInit, ViewChild} from '@angular/core';
import {SystemMessageService} from '@services/api/system-message.service';
import {IonContent, IonInfiniteScroll, IonList, NavController} from '@ionic/angular';
import {LoadingService} from '@app/utils/loading.service';
import {Router} from '@angular/router';
import {TextViewService} from '@services/utils/text-view.service';
import {dateFormat} from '@app/utils';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {StoreService} from '@services/store/store.service';

@Component({
  selector: 'app-system-message-index',
  templateUrl: './system-message-index.page.html',
  styleUrls: ['./system-message-index.page.scss'],
})
export class SystemMessageIndexPage implements OnInit {
  @ViewChild('infiniteScroll', {}) infiniteScroll: IonInfiniteScroll;
  @ViewChild('list', {}) list: IonList;
  @ViewChild('content', {}) content: IonContent;

  public filter = {
    page_index: 1,
    page_size: 20
  }
  public listData: any = []
  public total_count = 0
  public statusClassList = {
    Y: 'success',
    N: 'error',
  }
  public showFab = false

  constructor(
    private router: Router,
    private systemMessageService: SystemMessageService,
    private loadingService: LoadingService,
    private navCtrl: NavController,
    private textViewService: TextViewService,
    private nativeStorage: NativeStorage,
    private storeService: StoreService,

  ) { }

  ngOnInit() {
    this.init()
  }
  onScroll(event) {
    this.showFab = event.detail.scrollTop > 100
  };
  scrollToTop() {
    this.content.scrollToTop(300)
  }

  async init() {
    try {
      await this.loadingService.start()
      await this.loadData(null, true)
    } catch (e) {
      console.error(e)
    }
    this.loadingService.end()
  }
  async loadData(event?, isNew = true, isRefresh = false) {
    try {
      const page_size = this.filter.page_size
      const pageIndex = this.filter.page_index
      let page_index = pageIndex
      if (isNew) {
        page_index = 1
        this.filter.page_index = 1
        this.toggleInfiniteScroll(false)
      }
      const res: any = await this.systemMessageService.fetchSystemMessages({ page_index, page_size })
      console.log(res)

      if (isNew) {
        this.listData = res.data
      } else {
        this.listData.push(...res.data)
      }

      this.total_count = res.total_count
    } catch (e) {
      console.error(e)
    }

    if (event && event.target) {
      event.target.complete();
      if (!isRefresh) { // !isRefresh 下拉刷新不需要禁用
        event.target.disabled = (this.filter.page_index * this.filter.page_size) >= this.total_count
      }
    } else {
      this.toggleInfiniteScroll((this.filter.page_index * this.filter.page_size) >= this.total_count)
    }
    if (isNew) {
      this.content.scrollToTop()
    }
    this.filter.page_index += 1
  }


  toggleInfiniteScroll(disabled) {
    console.log(disabled)
    this.infiniteScroll.disabled = disabled
  }



  getStatusClass(v) {
    return v === 0 ? 'unread' : 'read'
  }

  async readMessage(item) {
    /*
    "type": "MAINTENANCE_ARRANGEMENT",
    "type": "TASK",
    "type": "EVENT",
    "type": "FEEDBACK",
    * */
    if (item.type === 'TASK' || item.type === 'EVENT' || item.type === 'FEEDBACK') {
      this.navCtrl.setDirection('forward');
      this.router.navigate(['/event/event-detail', item.mark])
    } else if (item.type === 'MAINTENANCE_ARRANGEMENT') {
      this.navCtrl.setDirection('forward');
      this.router.navigate(['maintenance-arrangement/maintenance-arrangement-detail', item.mark])
    } else {
      this.showText('訊息', item.msg_content)
    }
    try {
      item.read = 1
      await this.systemMessageService.readSystemMessages(item.message_id)
    } catch (e) {
      console.error(e)
    }
    this.storeService.getMsgStatus()
  }

  async showText(title, content) {
    await this.textViewService.showTextView(title, content)
  }
  ionViewDidEnter() {
    this.init()
    this.storeService.getMsgStatus()
  }

  getDate(v) {
    const str = v.replace(new RegExp(/-/gm), '/')
    const d = new Date(str)
    return dateFormat(d, 'yyyy-MM-dd HH:ss')
  }

  async onReadAll() {
    await this.loadingService.start()
    try {
      await this.systemMessageService.readAllSystemMessages()
      await this.loadData()
      await this.storeService.getMsgStatus()
    } catch (e) {
      console.error(e)
    }
    await this.loadingService.end()
  }

}
