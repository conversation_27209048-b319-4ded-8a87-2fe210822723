import { Component, OnInit } from '@angular/core';
import {CalendarComponentOptions} from 'ion2-calendar';
import {Subscription} from 'rxjs';
import {NavigationEnd, Router} from '@angular/router';
import {NavController, PopoverController} from '@ionic/angular';
import {LoadingService} from '@app/utils/loading.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {dateFormat, getWeekFirst, getWeekLast} from '@app/utils';
import { UserService } from '@services/api/user.service';
import {StoreService} from '@services/store/store.service';
import { UserPopoverComponent } from '@app/components/user-popover/user-popover.component';
import {Events} from '@app/services/events/event.service';

@Component({
  selector: 'app-student-index',
  templateUrl: './student-index.page.html',
  styleUrls: ['./student-index.page.scss'],
})
export class StudentIndexPage implements OnInit {

  public listData: any = []
  public filter = {
    date_select: '',
    date_from: '',
    date_to: '',
    user_ids: '',
    is_published: '',
  }
  public dateOptions: CalendarComponentOptions = {
    pickMode: 'single',
    monthFormat: 'YYYY-MM',
    showMonthPicker: true,
    from: new Date(1990, 0, 1),
    to: new Date(2050, 0, 1),
    daysConfig: [],
    monthPickerFormat: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']
  };
  public showType = 'day'
  private userInfo: any = {}
  private personal = '1'
  private clearSelect = false;
  private subscription: Subscription;
  constructor(
    private router: Router,
    private navCtrl: NavController,
    private loadingService: LoadingService,
    private nativeStorage: NativeStorage,
    private userService: UserService,
    private store: StoreService,
    public popoverController: PopoverController,
    public events: Events,
  ) { }

  async ngOnInit() {


    await this.onEnter(true);
    this.subscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd && event.url === '/tab-user/maintenanceArrangementsIndex') {
        this.onEnter();
      }
    });
    this.init()

    this.events.subscribe('login', () => {
      this.init()
    })
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  async onEnter(init = false) {
    // do your on enter page stuff here
    console.log('onEnter', init)
    if (init) { return }
    try {
      await this.loadingService.start()
      this.setDataRange(dateFormat(new Date(), 'yyyy/MM/dd'))
      // await this.loadDataByMonth()
      await this.loadData()
    } catch (e) {
    }
    this.loadingService.end()
  }

  async init(loadData = true) {
    try {
      await this.loadingService.start()
      this.userInfo = await this.nativeStorage.getItem('userInfo')

      if (this.userInfo && this.userInfo.access) {
        if (this.userInfo.access.G01 && this.userInfo.access.G01.length > 0) {
          this.personal = this.userInfo.access.G01.substr(8, 1) || '0'
        }
      }
      this.setDataRange(dateFormat(new Date(), 'yyyy/MM/dd'))
      if (loadData) {
        // this.setDataRange(dateFormat(new Date(), 'yyyy/MM/dd'))
        // await this.loadDataByMonth()
        await this.loadData()
      }
    } catch (e) {
    }
    this.loadingService.end()

  }

  async loadData(event?) {
    try {
      this.loadingService.start()

      // const { date_from, date_to, is_published } = this.filter

      let date_from = ''
      let date_to = ''
      let now = this.filter.date_select
      if (!now) {
        now = dateFormat(new Date())
      }
      if (this.showType === 'day') {
        date_from = now
        date_to = now
      } else {
        date_from = dateFormat(getWeekFirst(now))
        date_to = dateFormat(getWeekLast(now))
      }


      const user_id = this.userInfo.user_id
      const res: any = await this.userService.getUserCalendar({
        user_id,
        begin_date: date_from,
        end_date: date_to,
        personal: this.personal
      })
      console.log(res)

      // const data = res.filter(item => item.is_done !== 1)
      // this.setDaysConfig(res)
      // this.listData = data
      // res.forEach(item => {
      //   item.events.forEach(event => {
      //     event.ti
      //   })
      // })
      // for (let i = 0; i < res.length; i++) {
      //   const item = res[i]
      //   for (let j = 0; j < item.events.length; j++) {
      //     const event = item.events[j]
      //   }
      // }
      for (const item of res) {
        let hasM = false
        let showDivide = false
        const t = 0
        let preTime = ''
        for (const e of item.events) {
          e.showTime = e.time !== preTime;
          preTime = e.time;

          if (e && e.time && typeof e.time === 'string') {
            const arr = e.time.split(':')
            if (arr.length === 2) {
              const c_h = Number(arr[0])
              const c_m = Number(arr[1])
              if (c_h < 12) {
                hasM = true
              } else {
                if (hasM && !showDivide) {
                  e.showDivide = true
                  showDivide = true
                }
              }
            }
          }


        }

      }

      this.listData = res

      await this.loadDataByMonth()
    } catch (e) {
      console.error(e)
    }

    if (event && event.target) {
      event.target.complete();
    }
    this.loadingService.end()

  }
  async loadDataByMonth(event?) {
    try {
      this.loadingService.start()

      const { date_from, date_to, is_published } = this.filter

      // let date_from = ''
      // let date_to = ''
      // let now = this.filter.date_select
      // if (!now) {
      //   now = dateFormat(new Date())
      // }
      // if (this.showType === 'day') {
      //   date_from = now
      //   date_to = now
      // } else {
      //   date_from = dateFormat(getWeekFirst(now))
      //   date_to = dateFormat(getWeekLast(now))
      // }


      const user_id = this.userInfo.user_id
      const res: any = await this.userService.getUserCalendar({
        user_id,
        begin_date: date_from,
        end_date: date_to,
        personal: this.personal
      })
      console.log(res)

      // const data = res.filter(item => item.is_done !== 1)
      this.setDaysConfig(res)
      // this.listData = data
      // res.forEach(item => {
      //   item.events.forEach(event => {
      //     event.ti
      //   })
      // })
      // for (let i = 0; i < res.length; i++) {
      //   const item = res[i]
      //   for (let j = 0; j < item.events.length; j++) {
      //     const event = item.events[j]
      //   }
      // }
      // for (const item of res) {
      //   let hasM = false
      //   let showDivide = false
      //   const t = 0
      //   let preTime = ''
      //   for (const e of item.events) {
      //     e.showTime = e.time !== preTime;
      //     preTime = e.time;
      //
      //     if (e && e.time && typeof e.time === 'string') {
      //       const arr = e.time.split(':')
      //       if (arr.length === 2) {
      //         const c_h = Number(arr[0])
      //         const c_m = Number(arr[1])
      //         if (c_h < 12) {
      //           hasM = true
      //         } else {
      //           if (hasM && !showDivide) {
      //             e.showDivide = true
      //             showDivide = true
      //           }
      //         }
      //       }
      //     }
      //
      //
      //   }
      //
      // }

      // this.listData = res


    } catch (e) {
      console.error(e)
    }

    if (event && event.target) {
      event.target.complete();
    }
    this.loadingService.end()

  }

  getDaysConfig(data) {

    const dataArr = this.getDateArr(data)
    return dataArr.map(date => {

      const str = ('' + date).replace(new RegExp(/-/gm), '/')
      let css = 'has-data'
      if (dateFormat(new Date(str)) === dateFormat(new Date())) {
        css += ' today'
      }
      return {
        cssClass: css,
        date: dateFormat(new Date(str)),
        marked: false
      }
    })
  }
  setDaysConfig(data) {
    const daysConfig = this.getDaysConfig(data)
    const has_today = daysConfig.some(i => i.date === dateFormat(new Date()))
    if (!has_today) {
      daysConfig.push({
        cssClass: 'today',
        date: dateFormat(new Date()),
        marked: false
      })
    }
    const newOptions = {
      daysConfig
    };
    this.dateOptions = {
      ...this.dateOptions,
      ...newOptions
    };
  }



  onChange(event) {
    if (this.clearSelect) {
      this.clearSelect = false
      this.filter.date_select = ''
    }
  }

  onSelect(event) {
    const select = dateFormat(new Date(event.time))
    console.log(event, select, this.filter.date_select)
    if (this.filter.date_select === select) {
      this.clearSelect = true
    }
    setTimeout(() => {
      this.loadData()
    })
  }
  onMonthChange(event) {
    // this.filter.date_select = ''
    this.setDataRange(event.newMonth.string)
    // this.loadData()
    this.loadDataByMonth()
  }
  async toNow() {
    const d = dateFormat(new Date())
    this.setDataRange(d)
    await this.loadData()
    this.filter.date_select = d
  }

  setDataRange(dateStr) {
    const str = dateStr.replace(new RegExp(/-/gm), '/')
    const d = new Date(str)
    let start: any = new Date(d.getFullYear(), d.getMonth(), 1)
    let end: any = new Date(d.getFullYear(), d.getMonth() + 1, 0)

    // // 上個月
    // d.setMonth(d.getMonth() - 1, 1)
    // start = dateFormat(d)
    // // 下個月
    // d.setMonth(d.getMonth() + 3, 0)
    // end = dateFormat(d)

    // 日曆可見
    start = dateFormat(getWeekFirst(start))
    end = dateFormat(getWeekLast(end))
    // 當月
    // start = dateFormat(start)
    // end = dateFormat(end)

    this.filter.date_from = start
    this.filter.date_to = end
  }

  getStatusClass(is_done) {
    if (is_done === 0) {
      return 'error'
    } else if (is_done === 2) {
      return 'warning'
    }

    return ''
  }

  getDateArr(data) {
    const arr = []
    for (const item of data) {
      const index = arr.findIndex(d => d === item.date)
      if (index === -1) {
        arr.push(item.date)
      }
    }
    return arr
  }

  formatDateTime(date, time) {
    let text = '-'
    if (date) {
      text = date
      if (time) {
        text += ' ' + time
      }
      const str = text.replace(new RegExp(/-/gm), '/')
      return dateFormat(new Date(str), 'yyyy-MM-dd HH:mm')
    }
    return text
  }

  get showData() {
    return this.listData
    // const min = new Date()
    // const max = new Date()
    //
    // if (this.showType === 'day') {
    //   const min = new Date(this.filter.date_select)
    //   const max = new Date(this.filter.date_select)
    // } else {
    //
    // }
    //
    // return this.listData.filter(item => {
    //   if (this.filter.date_select) {
    //     return item.date === this.filter.date_select && item.is_done !== 1
    //   }
    //   return true
    // })
  }

  onClickItem(row) {
    this.navCtrl.setDirection('forward');
    this.router.navigate(['activity-detail-student', row.id])
  }

  onExit() {
    this.store.Logout()
  }

  async onMenu(ev: any) {
    const popover = await this.popoverController.create({
      component: UserPopoverComponent,
      event: ev,
      translucent: true
    });
    return await popover.present();
  }


  onLogout() {
    this.store.Logout()
  }

  get showTypeStr() {
    if (this.showType === 'day') {
      return '日'
    } else {
      return '周'
    }
  }
  onChangeShowType() {
    this.showType = this.showType === 'day' ? 'week' : 'day'
    this.loadData()
  }
}
