import {Component, EventEmitter, OnInit, ViewChild} from '@angular/core';
import { EBookingService } from '@services/api/ebooking.service';
import {UtilsService} from '@app/utils/utils.service';
import { DateSelectPage } from '@pages/components/date-select/date-select.page';
import { VenueSelectPage } from '@pages/components/venue-select/venue-select.page';
import { SessionSelectPage } from '@pages/components/session-select/session-select.page';
import { ResourcesSelectPage } from '@pages/components/resources-select/resources-select.page';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {NoticesService} from '@services/api/notices.service';
import {LoadingService} from '@app/utils/loading.service';

@Component({
  selector: 'app-teacher-reserve',
  templateUrl: './teacher-reserve.page.html',
  styleUrls: ['./teacher-reserve.page.scss'],
})
export class TeacherReservePage implements OnInit {

  @ViewChild('inputFile', {}) inputFile: any;

  public form: any = {
    schYear: '',
    name: '',
    natureId: '',
    ruleId: '',
    num: '',
    remark: '',
    subFormList: []
  }
  public years: any = []
  public natures: any = []
  public rules: any = []
  public user: any = {}
  public start_booking_date = ''
  public timeType = 'STD'

  public files: any = []
  constructor(
    private ebooking: EBookingService,
    private utils: UtilsService,
    private nativeStorage: NativeStorage,
    private notices: NoticesService,
    private loading: LoadingService,
  ) { }

  async ngOnInit() {
    this.reset()
  }
  async init() {
    this.user = await this.nativeStorage.getItem('userInfo')
    this.form.schYear = this.user.year_id
    await this.initYears()
    await this.initNatures()
  }

  async initYears() {
    try {
      const res: any = await this.ebooking.fetchEBookingYears()
      this.years = res.data
      const year = res.data.find(i => (i.id + '') === (this.user.year_id + ''))
      if (year) {
        this.form.schYear = year.id
      }
      console.log(res)
    } catch (e) {
      console.log(e)
    }
  }
  // async initDate() {
  //   try {
  //     const res: any = await this.ebooking.getDateInfo()
  //     debugger
  //     console.log(res)
  //   } catch (e) {
  //     console.log(e)
  //   }
  // }
  async initNatures() {
    try {
      const res: any = await this.ebooking.getNatureList()
      this.natures = res
      console.log(res)
    } catch (e) {
      console.log(e)
    }
  }
  async fetchRule() {
    try {
      const yearId = this.form.schYear
      const urTypeM = this.user.user_type_m
      // switch (this.user.user_type_m) {
      //   case 'admin':
      //     urTypeM = 'A'
      //     break
      //   case 'teacher':
      //     urTypeM = 'T'
      //     break
      //   case 'student':
      //     urTypeM = 'S'
      //     break
      // }
      const ruleResourceType = '3'
      const natureId = this.form.natureId

      if (!yearId || !urTypeM || !natureId) {
        return
      }

      const res: any = await this.ebooking.getRules({
        yearId,
        urTypeM,
        ruleResourceType,
        natureId
      })
      this.rules = res
      console.log(res)
    } catch (e) {
      console.log(e)
    }
  }
  async initStartDate(ruleId) {
    try {
      const res: any = await this.ebooking.getStartDate(ruleId)
      this.start_booking_date = res.start_booking_date
      this.timeType = res.date_info.timeType
    } catch (e) {
      console.error(e)
    }
  }

  onChangeYear() {
    this.form.ruleId = ''
    this.fetchRule()
  }
  onChangeNature() {
    this.form.ruleId = ''
    this.fetchRule()
  }
  onChangeRule() {
    if (!this.form.ruleId) { return }
    this.initStartDate(this.form.ruleId)
  }

get rulePlaceHolder() {
    if (this.form.schYear && this.form.natureId) {
    return '活動規則'
    } else {
      return '請選擇學年及活動性質'
    }
  }
venuePlaceHolder(item) {
    const arr = []
    if (!this.form.schYear) {
      arr.push('學年')
    }
    if (!this.form.natureId) {
      arr.push('性質')
    }
    if (!this.form.ruleId) {
      arr.push('規則')
    }
    if (!item.date) {
      arr.push('日期')
    }
    if (!item.lesson || item.lesson.length === 0) {
      arr.push('課節')
    }

    if (arr.length > 0) {
    return '請選擇' + arr.join('、')
    } else {
      return '活動地點'
    }
  }
resPlaceHolder(item) {
    const arr = []
    if (!this.form.schYear) {
      arr.push('學年')
    }
    if (!this.form.natureId) {
      arr.push('性質')
    }
    if (!this.form.ruleId) {
      arr.push('規則')
    }
    if (!item.date) {
      arr.push('日期')
    }
    if (!item.lesson || item.lesson.length === 0) {
      arr.push('課節')
    }

    if (arr.length > 0) {
    return '請選擇' + arr.join('、')
    } else {
      return '活動資源'
    }
  }
  validForm() {
    const form = this.form
    if (form) {
      if (!form.schYear) {
        this.utils.showMsg('請選擇學年')
        return false
      }
      if (!form.natureId) {
        this.utils.showMsg('請選擇活動性質')
        return false
      }
      if (!form.name) {
        this.utils.showMsg('請輸入活動名稱')
        return false
      }
      if (form.subFormList.length === 0) {
        this.utils.showMsg('請添加時間地點')
        return false
      } else {
        for (const item of form.subFormList) {
          if (!item.date) {
            this.utils.showMsg('請選擇日期')
            return false
          }
          if (item.lesson.length === 0) {
            this.utils.showMsg('請選擇課節')
            return false
          }
          if (item.venue.length === 0 && item.resource.length === 0) {
            this.utils.showMsg('請選擇場地或資源')
            return false
          }
        }
      }


      return true
    } else {
      return false
    }
  }

async onAdd() {
    if (!this.validForm()) { return }
    if (!await this.utils.confirm('提交預約', '是否確認提交預約信息？')) { return }
    try {
      await this.loading.start()
      const name = this.form.name
      const noOfStudent = this.form.num
      const natureId = this.form.natureId
      const ruleId = this.form.ruleId
      const remark = this.form.remark
      const subFormList = this.form.subFormList.map(item => {
        return {
          date: item.date,
          lesson: item.lesson,
          venue: item.venue,
          resource: item.resource.map(i => {
            return {
              id: i.id,
              qty: i.qty
            }
          }),
        }
      })
      let file: any = {}
      let attachment_id = ''
      if (this.files && this.files.length > 0) {
        try {
          file = await this.uploadFile(this.files[0].file)
          attachment_id = file.attachment_id
          console.log('file', file)
        } catch (e) {
          console.error(e)
          await this.loading.end()
          this.utils.showMsg('圖片上傳失敗，請重試！')
          return
        }
      }

      await this.ebooking.submitEBooking({
        name,
        noOfStudent,
        natureId,
        ruleId,
        remark,
        attachment_id,
        subFormList
      })
      await this.loading.end()
      await this.utils.showMsg('預約成功！')
      this.reset()
    } catch (e) {
      await this.loading.end()
      console.error()
    }
  }
async reset() {
    this.form = {
      schYear: '',
      name: '',
      natureId: '',
      ruleId: '',
      num: '',
      remark: '',
      subFormList: [{
        date: '',
        lesson: [],
        venue: [],
        resource: []
      }]
    }
    this.timeType = 'STD'
    this.start_booking_date = ''
    this.files = []
    await this.init()
  }

onCharacterClick() {

  }
onRuleClick() {

  }

async selectDate(date) {
    const select = new EventEmitter();
    select.subscribe(date => {
      console.log('select', date)
    });
    const res: any = await this.utils.showModal(DateSelectPage, {
      select,
      date,
      startDate: this.start_booking_date,
    })
    console.log(res)
    return res.data
  }
async selectVenue(date, lesson, venue) {
    const natureId = this.form.natureId
    const ruleId = this.form.ruleId
    const res: any = await this.utils.showModal(VenueSelectPage, {
      date,
      natureId,
      ruleId,
      lessonIds: lesson,
      venueIds: venue
    })
    console.log(res)
    return res.data
  }
async selectResources(date, lessonIds, resource) {
    const natureId = this.form.natureId
    const ruleId = this.form.ruleId
    const res: any = await this.utils.showModal(ResourcesSelectPage, {
      date,
      natureId,
      ruleId,
      lessonIds,
      resource
    })
    console.log(res)
    return res.data
  }
async selectLesson(lessonIds) {
    const res: any = await this.utils.showModal(SessionSelectPage, {
      lessonIds,
      ruleId: this.form.ruleId,
      timeType: this.timeType
    })
    console.log(res)
    return res.data
  }

  onAddSub() {
    let date = ''
    if (this.start_booking_date) {
      date = this.start_booking_date
    }
    this.form.subFormList.push({
      date,
      lesson: [],
      venue: [],
      resource: []
    })
  }

  async onDeleteSub(item, i) {
    if (await this.utils.confirm('確認刪除', '刪除選擇的時間和地點？')) {
      this.form.subFormList.splice(i, 1)
    }
  }
async onSelectDate(item) {
    const res: any = await this.selectDate(item.date)
    if (res.save) {
      item.date = res.date || ''
      item.lesson = []
      item.lessonIds_name = ''
      item.venue = []
      item.venue_name = ''
      item.resource = []
      item.resource_name = ''
      if (res && res.date) {
        try {
          const dateInfo: any = await this.ebooking.getDateInfo(res.date)
          this.timeType = dateInfo.timeType
        } catch (e) {
          console.error(e)
        }
      }
    }
  }
async onSelectLessons(item) {
    if (!this.validityYear()) { return }
    if (!this.validityNatureId()) { return }
    if (!this.validityRuleId()) { return }
    if (!this.validityDate(item)) { return }

    const res: any = await this.selectLesson(item.lesson)
    if (res.save) {
      item.lesson = res.data.map(i => i.id)
      item.lessonIds_name = res.data.map(i => i.sesDescr).join(',')
    }
  }
async onSelectVenue(item) {
    if (!this.validityYear()) { return }
    if (!this.validityNatureId()) { return }
    if (!this.validityRuleId()) { return }
    if (!this.validityDate(item)) { return }
    if (!this.validityLesson(item)) { return }

    const lesson = item.lesson.join(',')
    const venue = item.venue
    const res: any = await this.selectVenue(
      item.date,
      lesson,
      venue
    )
    if (res.save) {
      item.venue_name = res.data.map(i => {
        return i.name
      }).join(',')
      item.venue = res.data.map(i => i.id)
    }
  }
async onSelectResources(item) {
    if (!this.validityYear()) { return }
    if (!this.validityNatureId()) { return }
    if (!this.validityRuleId()) { return }
    if (!this.validityDate(item)) { return }
    if (!this.validityLesson(item)) { return }

    const lesson = item.lesson.join(',')
    const resource = item.resource
    const res: any = await this.selectResources(
      item.date,
      lesson,
      resource
    )
    if (res.save) {
      item.resource = res.data.map(i => {
        return {
          id: i.id,
          qty: i.selectQty
        }
      })
      item.resource_name = res.data.map(i => {
        return i.name + '(' + i.selectQty + ')'
      }).join(',')
    }
  }

validityYear() {
    if (!this.form.schYear) {
      this.utils.showToast({ msg: '請選擇學年！' })
      return false
    }
    return true
  }
validityNatureId() {
    if (!this.form.natureId) {
      this.utils.showToast({ msg: '請選擇活動性質！' })
      return false
    }
    return true
  }
validityRuleId() {
    if (!this.form.ruleId) {
      this.utils.showToast({ msg: '請選擇活動規則！' })
      return false
    }
    return true
  }
validityDate(item) {
    if (!item.date) {
      this.utils.showToast({ msg: '請選擇日期！' })
      return false
    }
    return true
  }
validityLesson(item) {
    if (!item.lesson || item.lesson === '' || item.lesson.length === 0) {
      this.utils.showToast({ msg: '請選擇課節！' })
      return false
    }
    return true
  }
  /* 預約附件 */

  onChangeFile(event) {
    // console.log(event)
    const files = []
    for (const file of event.target.files) {
      const fileItem = {
        file,
        base64: '',
      }
      files.push(fileItem)

      const reader = new FileReader();
      reader.onload = (e: any) => {
        fileItem.base64 = e.target.result
      };
      reader.readAsDataURL(file);
    }
    event.target.value = ''
    console.log(files)
    // this.uploadFile(files[0])

    this.files.push(...files)

  }

  onRemoveFile() {
    // this.attachment_array.splice(i, 1)
    this.files = []
  }

  onAddFile() {
    this.inputFile.nativeElement.click()
  }

  async uploadFile(file) {
    try {
      const res: any = await this.notices.updateNoticeAttachment(file)
      if (res && res.attachment_id) {
        // "attachment_id": "14",
        //   "attachment_path": "bundles/AppUpload/Notice/f68ca431960c0035/barcode.gif"
        return {
          attachment_id: res.attachment_id,
          attachment_path: res.attachment_path,
        }
      }
      // this.utils.showToast({ msg: '上載成功'})
    } catch (e) {
      console.error(e)
    }
    return {}
  }

}
