import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { EventChildPage } from './event-child.page';

describe('EventChildPage', () => {
  let component: EventChildPage;
  let fixture: ComponentFixture<EventChildPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EventChildPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EventChildPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
