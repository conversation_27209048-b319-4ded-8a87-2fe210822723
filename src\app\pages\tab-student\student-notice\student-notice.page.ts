import {Component, OnInit, ViewChild} from '@angular/core';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {LoadingService} from '@app/utils/loading.service';
import {NoticesService} from '@services/api/notices.service';
import {dateFormat} from '@app/utils';
import {NavigationEnd, Router, ActivatedRoute} from '@angular/router';
import {IonContent, IonInfiniteScroll, IonList, NavController} from '@ionic/angular';
import {Subscription} from 'rxjs';
import {Events} from '@app/services/events/event.service';
import { JpushService } from '@services/jpush/jpush.service';

@Component({
  selector: 'app-student-notice',
  templateUrl: './student-notice.page.html',
  styleUrls: ['./student-notice.page.scss'],
})
export class StudentNoticePage implements OnInit {

  @ViewChild('infiniteScroll', {}) infiniteScroll: IonInfiniteScroll;
  @ViewChild('content', {}) content: IonContent;
  @ViewChild('list', {}) list: IonList;
  private subscription: Subscription;
  public loading = false
  public user_id = ''
  public listData: any = []
  public user: any = {}
  public admin_permission = '0'

  public filter: any = {
    page_index: 0,
    page_size: 20,
  }
  public total_count = 0
  constructor(
    private nativeStorage: NativeStorage,
    private loadingService: LoadingService,
    private notices: NoticesService,
    private router: Router,
    private navCtrl: NavController,
    private route: ActivatedRoute,
    private events: Events,
    private jpushService: JpushService,
  ) {
    events.subscribe('notice:reload', () => {
      this.loadData(true)

    })
    events.subscribe('login', () => {
      this.init()
    })
    events.subscribe('notice:changeReadStatus', ({id, unread}) => {
      this.changeReadStatus(id, unread)
    })
  }
  async init() {
    setInterval(() => {
        this.jpushService.getId().then((id) => {
            console.log('jpush id:', id)
        }).then((e) => {
          console.log('jpush id error:', e)
        })
    }, 1000)
    this.filter = {
      page_index: 0,
      page_size: 20,
    }
    this.total_count = 0
    this.user = await this.nativeStorage.getItem('userInfo')
    if (this.user && this.user.access) {
      if (this.user.access.G02 && this.user.access.G02.length > 0) {
        this.admin_permission = this.user.access.G02.substr(0, 1) || '0'
      }
    }
    this.loadData()

    // this.subscription = this.router.events.subscribe((event) => {
    //   if (event instanceof NavigationEnd) {
    //     if (event.url === '/tab-teacher/notice' || event.url === '/tab-student/notice') {
    //       this.loadData();
    //     }
    //   }
    // })

  }

  ionViewDidEnter() {
    this.events.publish('notice:changeBadge', {});
  }

  async ngOnInit() {
    this.init()
  }

  get title() {
    // @ts-ignore
    return (this.route.snapshot.data && this.route.snapshot.data.admin) ? '已發送訊息' : '訊息'
  }
  get canCreate() {
    return this.admin_permission === '1'
  }
  // ionViewDidEnter() {
  //   this.loadData()
  // }
  // ionViewWillEnter() {
  //   this.loadData()
  // }

  get showDot() {
    if (this.user && typeof this.user.user_type === 'string') {
      const user_type = this.user.user_type.toUpperCase()
      return user_type === 'STUDENT'
    }
    return false
  }

  async loadData(event?, isNew = true, isRefresh = false) {
    if (this.loading) {

      if (event && event.target) {
        event.target.complete();
      }
      return
    }

    if (isNew) {
      this.content.scrollToTop()
    }

    this.events.publish('notice:changeBadge');

    if (!isNew && !isRefresh) {
      if ((this.filter.page_index * this.filter.page_size) >= this.total_count) {

        if (event && event.target) {
          event.target.complete();
        }
        return
      }
    }
    this.loading = true
    await this.loadingService.start()
    let empty = false
    try {

      const { actTerm, page_index } = this.filter
      let page = page_index
      if (isNew) {
        page = 1
        this.filter.page_index = 1
        this.toggleInfiniteScroll(false)
      }

      const user_id = this.user.user_id
      const admin_permission = this.admin_permission
      if (!user_id) { return }
      const res: any = await this.notices.fetchNotices({ user_id, admin_permission, page})
      console.log(res)
      // this.listData = res.notices
      if (isNew) {
        this.listData = res.notices
      } else {
        this.listData.push(...res.notices)
      }
      this.total_count = res.count
    } catch (e) {
      if (e && e.code) {
        if (e.code === 3002) {
          empty = true
          // await this.utils.showMsg(e.desc)
          if (isNew) {
            this.listData = []
          }
        }
      }
      console.error(e)
    }
    if (event && event.target) {
      event.target.complete();
      if (!isRefresh) { // !isRefresh 下拉刷新不需要禁用
        event.target.disabled = empty || (this.filter.page_index * this.filter.page_size) >= this.total_count
      }
    } else {
      this.toggleInfiniteScroll(empty || (this.filter.page_index * this.filter.page_size) >= this.total_count)
    }
    this.filter.page_index += 1
    await this.loadingService.end()
    this.loading = false
  }
  toggleInfiniteScroll(disabled) {
    console.log(disabled)
    this.infiniteScroll.disabled = disabled
  }

  readMessage(item) {
    // item.unread = '0'
    this.navCtrl.setDirection('forward');
    this.router.navigate(['notice-detail', item.notice_id])
  }

  changeReadStatus(id, unread) {
    const item = this.listData.find(i => i.notice_id === id)
    if (item) {
      item.unread = unread
    }
  }


  getStatusClass(v) {
    return v === '1' ? 'unread' : 'read'
  }


  getDate(v) {
    const str = v.replace(new RegExp(/-/gm), '/')
    const d = new Date(str)
    return dateFormat(d, 'yyyy-MM-dd HH:mm')
  }

  onCreate() {
    this.navCtrl.setDirection('forward');
    this.router.navigate(['notice-add'])
  }

}
