import { Injectable } from '@angular/core';
import {RequestService} from '@services/request/request.service';

@Injectable({
  providedIn: 'root'
})
export class EventsService {

  constructor(public request: RequestService) {
}

  searchEvents({system_id, project_id, keyword, only_mine, status, task_user_id, urgent, page_index, page_size, hide_list, with_hide_data}:
                 {
                   system_id?: string, project_id?: string, keyword?: string, only_mine?: string,
                   status?: string, task_user_id?: string, urgent?: string,
                   page_index?: number, page_size: number, hide_list?: string, with_hide_data?: string
                 }) {
    return this.request.request({
      url: '/events/actions/search',
      method: 'get',
      params: {
        system_id, project_id, keyword, only_mine, status, task_user_id, urgent, page_index, page_size, hide_list, with_hide_data
      }
    })
  }
  getEvent(event_id: string) {
    return this.request.request({
      url: '/events/actions/inquire',
      method: 'get',
      params: {
        event_id
      }
    })
  }
  updateEventStatus({event_id, status, arrange_user_id, remark}: any) {
    return this.request.request({
      url: '/events/actions/update-status',
      method: 'post',
      data: {
        event_id,
        status,
        arrange_user_id,
        remark,
      }
    })
  }
}
