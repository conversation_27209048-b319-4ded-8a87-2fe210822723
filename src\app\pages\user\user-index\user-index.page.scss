.header:after {

  //background-image: none;
  //border-bottom: 1px solid #EFEFEF;
}

.content {
  --background: var(--content-page-background);
  .list {
    background: var(--content-page-background);
    padding-top: 0;
    //ion-item {
    //  --padding-start: 0;
    //  ion-label {
    //    padding-left: 16px;
    //  }
    //}
  }
  .version {
    text-align: center;
    display: block;
    padding: 10px 0;
    //color: #bdbdbd;
    color: #212121;
    font-size: 11px;
    opacity: 0.45;
  }
}
