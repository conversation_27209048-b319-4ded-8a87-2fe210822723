import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TabStudentPage } from '@pages/tab-student/tab-student.page';

const routes: Routes = [
  {
    path: '',
    component: TabStudentPage,
    children: [
      // 主頁
      {
        path: 'index',
        children: [
          {
            path: '',
            loadChildren: () => import('./student-index/student-index.module').then((m) => m.StudentIndexPageModule)
            // loadChildren: '../tab1/tab1.module#Tab1PageModule'
          }
        ]
      },
      // 活動報名
      {
        path: 'activity',
        children: [
          {
            path: '',
            loadChildren: () => import('./student-activity/student-activity.module').then((m) => m.StudentActivityPageModule)
          }
        ]
      },
      // 我的活動
      {
        path: 'my-activity',
        children: [
          {
            path: '',
            loadChildren: () => import('./student-personal/student-personal.module').then((m) => m.StudentPersonalPageModule)
          }
        ]
      },
      // 通告
      {
        path: 'notice',
        children: [
          {
            path: '',
            loadChildren: () => import('./student-notice/student-notice.module').then((m) => m.StudentNoticePageModule)
          }
        ],
        data: { admin: false },
      },
      // // 摘星計劃
      // {
      //   path: 'star',
      //   children: [
      //     {
      //       path: '',
      //       loadChildren: () => import('./student-star/student-star.module').then((m) => m.StudentStarPageModule)
      //     }
      //   ]
      // },
      // // 摘星計劃 - 輸入
      // {
      //   path: 'star-input',
      //   children: [
      //     {
      //       path: '',
      //       loadChildren: () => import('./student-star-input/student-star-input.module').then((m) => m.StudentStarInputPageModule)
      //     }
      //   ]
      // },
      // // 摘星計劃 - 查找活動
      // {
      //   path: 'star-input/activity/search',
      //   children: [
      //     {
      //       path: '',
      //       loadChildren: () => import('./student-star-activity-search/student-star-activity-search.module').then((m) => m.StudentStarActivitySearchPageModule)
      //     }
      //   ]
      // },
      // {
      //   path: 'star-input/detail',
      //   children: [
      //     {
      //       path: '',
      //       loadChildren: () => import('./student-star-detail/student-star-detail.module').then((m) => m.StudentStarDetailPageModule)
      //     }
      //   ]
      // },
      {
        path: '',
        redirectTo: '/tab-student/index',
        // redirectTo: '/lb-admin/tab1',
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TabStudentPageRoutingModule {}
