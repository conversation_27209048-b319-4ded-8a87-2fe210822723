import {Component, Input, Output, OnInit, EventEmitter} from '@angular/core';
import {CalendarComponentOptions} from 'ion2-calendar';
import {ModalController} from '@ionic/angular';
import { dateFormat } from '@app/utils';

@Component({
  selector: 'app-date-select',
  templateUrl: './date-select.page.html',
  styleUrls: ['./date-select.page.scss'],
})
export class DateSelectPage implements OnInit {
  @Input() date: any = dateFormat(new Date())
  @Input() startDate = ''
  @Output() public select = new EventEmitter();
  public date_select = ''
  public date_from = ''
  public date_to = ''
  public dateOptions: CalendarComponentOptions = {
    pickMode: 'single',
    monthFormat: 'YYYY-MM',
    showMonthPicker: false,
    from: new Date(1990, 0, 1),
    to: new Date(2050, 0, 1),
    daysConfig: []
  };
  constructor(
    private modalCtrl: ModalController,
  ) { }

  ngOnInit() {
    this.date_select = this.date
    if (this.startDate) {
      this.dateOptions.from = new Date(this.startDate)
    } else {
      this.dateOptions.from = new Date(1990, 0, 1)
    }

  }

  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true,
        date: dateFormat(new Date(this.date_select))
    });
  }
  onSelect(e) {
    const date = dateFormat(new Date(e.time))
    this.select.emit(date)
    this.modalCtrl.dismiss({
      save: true,
      date
    });
  }
  onChange(e) {
    const date = dateFormat(new Date(e))
    console.log('onChange', e)
    this.modalCtrl.dismiss({
      save: true,
      date
    });
  }
  onMonthChange(e) {
    console.log('onMonthChange', e)
  }

}
