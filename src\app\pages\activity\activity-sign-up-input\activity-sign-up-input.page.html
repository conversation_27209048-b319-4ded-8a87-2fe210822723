<ion-header class="inside-page">
  <ion-toolbar>
    <ion-buttons slot="start">
      <!--<ion-back-button (click)="onClose()"></ion-back-button>-->

      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>參加活動</ion-title>
    <ion-buttons slot="end">
      <ion-button [hidden]="false" (click)="onSave()">
        保存
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="project-box" *ngIf="showProject">
    <div class="label">項目</div>
    <div class="tips">限制選擇{{ min || '*' }}(最少)~ {{ max || '*' }}(最多)個項目</div>
    <div class="list" *ngIf="actPjCList && actPjCList.length > 0">
      <div class="item" *ngFor="let item of actPjCList; let i = index">
        <ion-checkbox [(ngModel)]="actPjCList[i].check"></ion-checkbox>
        <ion-label class="checkbox-label">{{ item.name }}</ion-label>
      </div>
    </div>
  </div>
  <div class="sup-box">
    <div class="label">是否申請資助</div>
    <ion-radio-group [(ngModel)]="actSupport" class="list" *ngIf="actSupportList && actSupportList.length > 0">
      <div class="item" *ngFor="let item of actSupportList; let i = index">
        <ion-radio mode="md" [value]="item.id"></ion-radio>
        <ion-label class="radio-label">{{ item.name }}</ion-label>
      </div>
    </ion-radio-group>
  </div>
  <div class="back-box">
    <div class="label">歸程方法</div>
    <ion-radio-group [(ngModel)]="actBack" class="list" *ngIf="actBackList && actBackList.length > 0">
      <div class="item" *ngFor="let item of actBackList; let i = index">
        <ion-radio mode="md" [value]="item.id"></ion-radio>
        <ion-label class="radio-label">{{ item.name }}</ion-label>
      </div>
    </ion-radio-group>
  </div>
  <div class="input-box">
    <div class="label">聯絡電話</div>
    <div class="item">
      <ion-input [(ngModel)]="phone" type="tel" clearInput></ion-input>
    </div>
  </div>

</ion-content>
