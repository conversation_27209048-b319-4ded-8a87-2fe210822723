import { Component, OnInit } from '@angular/core';
import {ActivityService} from '@services/api/activity.service';
import {UserService} from '@services/api/user.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {LoadingService} from '@app/utils/loading.service';
import {StoreService} from '@services/store/store.service';

@Component({
  selector: 'app-student-profile',
  templateUrl: './student-profile.page.html',
  styleUrls: ['./student-profile.page.scss'],
})
export class StudentProfilePage implements OnInit {

  public form = {
    username: '',
    stuUname: '',
    stuEname: '',
    email: '',
    pass: '',
    passConfirm: '',
    stuPhone: '',
    stuParent: '',
  }
  constructor(
    private activity: ActivityService,
    private userService: UserService,
    private nativeStorage: NativeStorage,
    private utils: UtilsService,
    private loading: LoadingService,
    private store: StoreService,
  ) { }

  ngOnInit() {
    this.loadInfo()
  }

  async loadInfo() {
    let userId = ''
    try {
      const user = await this.nativeStorage.getItem('userInfo')
      console.log(user)
      if (user) {
        userId = user.user_id
      }
    } catch (e) {
      console.error(e)
    }
    if (!userId) {
      this.utils.showToast({msg: '登錄信息失效，請重新登錄！'})
      return
    }
    try {
      await this.loading.start()
      const res: any = await this.userService.getStudentInfo({ userId })
      console.log('spp', res)
      if (res) {
        this.form.username = res.username
        this.form.email = res.email
        this.form.stuUname = res.stuUname
        this.form.stuEname = res.stuEname
        this.form.stuParent = res.stuParent
        this.form.stuPhone = res.stuPhone
      } else {
        this.utils.showToast({msg: '網絡異常，請檢查網絡！'})
      }
    } catch (e) {
      console.error(e)
      this.utils.showToast({msg: '網絡異常，請檢查網絡！'})
    }
    await this.loading.end()
  }

  trim(text) {
    return text.toString().replace('/ /g', '')
  }

  async onSave() {
    let userId = ''
    try {
      const user = await this.nativeStorage.getItem('userInfo')
      if (user) {
        userId = user.user_id
      }
    } catch (e) {
      console.error(e)
    }
    if (!userId) {
      this.utils.showToast({msg: '登錄信息失效，請重新登錄！'})
      return
    }
    const stuPhone = this.form.stuPhone
    const stuParent = this.form.stuParent
    const email = this.form.email
    const password = this.form.pass
    const passConfirm = this.form.passConfirm
    if (password || passConfirm) {
      if (this.trim(password).length === 0) {
        this.utils.showToast({msg: '請填上新密碼！'})
        return
      } else if (this.trim(passConfirm).length === 0) {
        this.utils.showToast({msg: '請填上重入新密碼！'})
        return
      }
    }
    this.handleSave(userId, stuPhone, stuParent, password, email)
  }
  async handleSave(userId, stuPhone, stuParent, password, email) {
    await this.loading.start()
    try {
      const res: any = await this.userService.editStudentInfo({
        userId,
        stuPhone,
        stuParent,
        password,
        email
      })
      if (password) {
        this.utils.showToast({msg: '保存成功，請重新登錄！'})
        this.store.Logout()
      } else {
        this.utils.showToast({msg: '保存成功！'})
      }
      console.log(res)
    } catch (e) {
      console.error(e)
      this.utils.showToast({msg: '保存失敗，請重試！'})
    }
    await this.loading.end()
  }
}
