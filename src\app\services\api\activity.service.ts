import { Injectable } from '@angular/core';
import {RequestService} from '@services/request/request.service';

@Injectable({
  providedIn: 'root'
})
export class ActivityService {

  constructor(public request: RequestService) {
  }


  /**
   * 獲取課外活動
   */
  fetchActivitys({
        actYear,
        actTerm,
        actCode,
        actName,
        actCodeOrName,
        actCategory,
        actTeacher,
        actExpire,
        actFinish,
        actCreator,
        page,
        hideInvalidActivity
      }: {
    actYear?: number, // [integer] 年度id
    actTerm?: number, // [integer] 學期
    actCode?: string, // [string] 活動編號
    actName?: string, // [string] 活動名稱
    actCodeOrName?: string, // [string] 活動編號或活動名稱
    actCategory?: string, // [string] 服務類別ID
    actTeacher?: string, // [string] 負責老師
    actExpire?: string, // [string] 截止報名日期
    actFinish?: string, // [string] 狀態
    actCreator?: string, // [integer] 創建人ID
    page?: number, // [integer] 頁數
    hideInvalidActivity?: string, // [integer] 頁數
  }) {
    return this.request.request({
      url: `/v2/app/teacher/activities`,
      method: 'GET',
      params: {
        actYear,
        actTerm,
        actCode,
        actName,
        actCodeOrName,
        actCategory,
        actTeacher,
        actExpire,
        actFinish,
        actCreator,
        page,
        hideInvalidActivity
      }

    })
  }



  /**
   * 獲取所有類別，無分頁
   */
  getAllCategory({ name, id }: {
    name?: string, // [string] 名稱搜索
    id?: number, // [integer] id
  }) {
    return this.request.request({
      url: `/extra/master_category_getAll`,
      method: 'GET',
      params: {
        name,
        id,
      }

    })
  }

  /**
   * 學生系統-參與的活動
   */
  fetchActivityByStudent({
        userNo,
        actYear,
        actTerm,
      }: {
    userNo?: string, // [string] 學生編號
    actYear?: number, // [integer] 年度id
    actTerm?: number, // [integer] 學期
    actCategory?: string, // [string] 活動類別
    actName?: string, // [string] 活動名稱
  }) {
    return this.request.request({
      url: `/v2/app/teacher/activities/student-activities`,
      method: 'GET',
      params: {
        userNo,
        actYear,
        actTerm,
      }
    })
  }

  /**
   * 根據ID獲取課外活動修改內容
   */
  getActivityInfo({ id }: {
    id: string, // [integer] id
  }) {
    return this.request.request({
      url: `/extra/activity_info`,
      method: 'GET',
      params: {
        id
      }
    })
  }




  /**
   * 活動學生信息
   */
  getActivityLottery(id: number, // [integer] 活動id
  ) {
    return this.request.request({
      url: `/extra/activity_lottery`,
      method: 'GET',
      params: {
        id
      }

    })
  }



  /**
   * 獲取活動的點名紙
   */
  getRollCall({ activity_id, actKey, actDate }: {
    activity_id: string, // 活動id
    actKey?: number, // [integer] 活動時間組別key
    actDate?: string, // [string] 日期
  }) {
    return this.request.request({
      url: `/v2/app/roll-call/${activity_id}`,
      method: 'GET',
      params: {
        activity_id,
        actKey,
        actDate
      }

    })
  }


  /**
   * 保存點名紙
   */
  saveRollCall({ activity_id, send_notice, roll_call_array }: {
    activity_id: string, // 活動id
    send_notice: string, // 活動id
    roll_call_array: any, // [array] JSON數據
  }) {
    return this.request.request({
      url: `/v2/app/roll-call/${activity_id}/actions/save`,
      method: 'POST',
      responseType: 'full',
      data: {
        activity_id,
        send_notice,
        roll_call_array
      }

    })
  }

  /**
   * 已重新下載離線點名紙的活動，清除檢測狀態
   */
  updateReloadStatus(activity_id_array: any // [array] 活動id, JSON數據
  ) {
    return this.request.request({
      url: `/v2/app/roll-call/actions/update-reload-status`,
      method: 'POST',
      data: {
        activity_id_array: JSON.stringify(activity_id_array)
      }

    })
  }

  /**
   * 檢測已下載的離線點名紙是否需要更新
   */
  checkReload(activity_id_array: any // [array] 活動id, JSON數據
  ) {
    return this.request.request({
      url: `/v2/app/roll-call/actions/check-reload`,
      method: 'POST',
      data: {
        activity_id_array
      }

    })
  }

  /**
   * 學生系統-參與的活動
   */
  fetchStudentActivity({
        userNo,
        actYear,
        actTerm,
        page
      }: {
    userNo: string, // [string] 學生編號
    actYear: string, // [integer] 年度id
    actTerm?: string, // [integer] 學期
    page?: string, // [string] 活動類別
  }) {
    return this.request.request({
      url: `/v2/app/student/activities/my-activities`,
      method: 'GET',
      params: {
        userNo,
        actYear,
        actTerm,
        page,
      }

    })
  }

  /**
   * App - 學生頁面 - 可參加的活動列表
   */
  fetchStudentActivities({
        userNo,
        actYear,
        actTerm,
        actCategory,
        actCode,
        actName,
        actCodeOrName,
        actDateStart,
        actDateEnd,
        actExpire,
        activity_id,
        page
      }: {
    userNo: string, // [string] 學生編號
    actYear: string, // [integer] 年度id
    actTerm?: string, // [integer] 學期
    actCategory?: string, // [string] 活動類別
    actCode?: string, // [string] 活動編號
    actName?: string, // [string] 活動名稱
    actCodeOrName?: string, // [string] 活動編號或活動名稱
    actDateStart?: string, // [string] 活動日期-開始
    actDateEnd?: string, // [string] 活動日期-結束
    actExpire?: string, // [string] 活動報名截止日期
    activity_id?: string, // [string] 狀態
    page?: number, // [integer] 頁
  }) {
    return this.request.request({
      url: `/v2/app/student/activities`,
      method: 'GET',
      params: {
        userNo,
        actYear,
        actTerm,
        actCategory,
        actCode,
        actName,
        actCodeOrName,
        actDateStart,
        actDateEnd,
        actExpire,
        activity_id,
        page
      }

    })
  }






  /**
   * 學生同意參加時檢查是否超額或撞期
   */
  checkClashStuAgree({ userId, actId }: {
    userId: string, // [integer] 用戶ID
    actId: string, // [integer] 活動ID
  }) {
    return this.request.request({
      url: `/extra/check_clash_stu_agree`,
      method: 'GET',
      params: {
        userId,
        actId
      }

    })
  }



  /**
   * 同意/不同意
   */
  saveStudentAgree({
        userNo,
        actId,
        agree,
        actPj
      }: {
    userNo: string, // [string] 學生編號
    actId: string, // [integer] 活動id
    agree: string, // [integer] 同意=Y/不同意=N
    actPj?: string, // [integer] 活動項目
  }) {
    return this.request.request({
      url: `/extra/student/ajax_agree`,
      method: 'POST',
      data: {
        userNo,
        actId,
        agree,
        actPj
      }

    })
  }

  /**
   * 同意參加活動後填寫信息
   */
  saveStudentNotice({
        userNo,
        actId,
        actPj,
        actSupport,
        actBack,
        stuPhoneNum
      }: {
    userNo: string, // [string] 學生編號
    actId: string, // [integer] 活動id
    actPj: string, // [integer] 選擇的附設項目
    actSupport: string, // [integer] 選擇的資助
    actBack: string, // [integer] 選擇的歸程
    stuPhoneNum: string, // [integer] 該活動的聯繫電話
  }) {
    return this.request.request({
      url: `/extra/student/ajax_notice`,
      method: 'POST',
      data: {
        userNo,
        actId,
        actPj,
        actSupport,
        actBack,
        stuPhoneNum
      }

    })
  }

  /**
   * ajax_get_lottery
   */
  getStudentLottery({
        userNo,
        actId
      }: {
    userNo: string, // [string] 學生編號
    actId: number, // [integer] 活動id
  }) {
    return this.request.request({
      url: `/extra/student/ajax_get_lottery`,
      method: 'GET',
      params: {
        userNo,
        actId
      }
    })
  }





}
