import { Injectable } from '@angular/core';
import {RequestService} from '@services/request/request.service';

@Injectable({
  providedIn: 'root'
})
export class PushService {

  constructor(public request: RequestService) {
  }



  /**
   * 更新推送設備的登記
   */
  updatePush({
        user_id,
        registration_id
      }: {
    user_id: string, // 用戶id
    registration_id: string, // [integer] 登記id
  }) {
    return this.request.request({
      url: `/v2/app/push-registrations/${user_id}/actions/update`,
      method: 'POST',
      data: {
        user_id,
        registration_id
      }

    })
  }

  /**
   * 刪除推送設備的登記
   */
  deletePush({
        user_id,
        registration_id
      }: {
    user_id: string, // 用戶id
    registration_id: string, // [integer] 登記id
  }) {
    return this.request.request({
      url: `/v2/app/push-registrations/${user_id}/actions/delete`,
      method: 'POST',
      data: {
        user_id,
        registration_id
      }

    })
  }
  /**
   * 清空推送設備的登記
   */
  clearPush(registration_id: string, // [integer] 登記id
  ) {
    return this.request.request({
      url: `/v2/app/push-registrations/actions/empty-registration`,
      method: 'POST',
      data: {
        registration_id
      }

    })
  }



}
