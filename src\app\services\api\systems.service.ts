import { Injectable } from '@angular/core';
import {RequestService} from '../request/request.service'

@Injectable({
  providedIn: 'root'
})
export class SystemsService {

  constructor(public request: RequestService) {
  }

  fetchSystems(system_name?: string) {
    return this.request.request({
      url: '/systems',
      method: 'get',
      params: {
        system_name
      }
    })
  }
  getSystem(system_id: string) {
    return this.request.request({
      url: '/systems/actions/inquire',
      method: 'get',
      params: {
        system_id
      }
    })
  }
}
