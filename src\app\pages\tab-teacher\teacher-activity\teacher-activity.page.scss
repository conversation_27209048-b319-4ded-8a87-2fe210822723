ion-header.activity-filter, ion-header.student-filter {
  padding-bottom: 5px;
  font-size: 0.7rem;
  padding: 0.3rem 0.2rem;


  ion-select {

    background: #ffffff;
    border: 1px solid #EDEDED;
    border-radius: 5px;
    padding: 0.1rem 0.5rem;
  }
  ion-label.input-label {
    padding: 0.1rem 0.3rem;
  }

  ion-datetime {

    background: #ffffff;
    border: 1px solid #EDEDED;
    border-radius: 5px;
    padding: 0.1rem 0.5rem;
    //width: 40%;
    width: 6rem;
    height: 1.25rem;
    line-height: 1.05rem;
    text-align: center;
    ::ng-deep {
      .datetime-text {
        line-height: 1.05rem;
      }
    }
  }
  ::ng-deep {
    ion-datetime-button::part(native) {
      padding: 1px;
    }
  }
  ion-button {
    height: 1.25rem;
    vertical-align: middle;
    margin: 0 0.5rem;
  }

  ion-input {
    height: 1.25rem;
    background: #ffffff;
    border: 1px solid #EDEDED;
    border-radius: 5px;
  }

  .stu-select-btn {
    --padding-start: 0.3rem;
    --padding-end: 0.3rem;
  }




}

ion-content {
  --background: #FFFFFF;
}



.list-item {
  margin: 12px;
  box-shadow:1px 1px 3px 0px rgba(0,0,0,0.18);
  border-radius:4px;
  padding-left: 10px;

  background: #457DEE;
  &.success {
    background: #A0E4E4;
    .item-info .info .status {
      color: #A0E4E4;
    }
  }
  &.warning {
    background: #FF9900;
    .item-info .info .status {
      color: #FF9900;
    }
  }
  &.error {
    background: #FFAB9F;
    .item-info .info .status {
      color: #FFAB9F;
    }
  }
  .item-info {
    padding: 5px 0;
    width: 100%;

    word-break: keep-all;
    white-space: nowrap;
    text-overflow: ellipsis;
    .project-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: calc(100% - 10px);
      width: 100%;
      font-weight:600;
      font-size:13px;
      color:rgba(96,98,102,1);
      padding-bottom: 5px;
    }
    .info {
      font-size:11px;
      color:rgba(96,98,102,1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: calc(100% - 5px);
      width: 100%;
      .status {
        color: #457DEE;
      }
      .time {
        float: right;
        color: #B0B0B0;
      }
    }
  }
}
