import {Component, OnInit, ViewChild} from '@angular/core';
import { IonContent, IonInfiniteScroll, IonList, NavController} from '@ionic/angular';
import {ActivityService} from '@services/api/activity.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {dateFormat} from '@app/utils';
import {SelectStudentService} from '@services/utils/select-student.service';
import {Router} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import {Events} from '@app/services/events/event.service';

@Component({
  selector: 'app-student-activity',
  templateUrl: './student-activity.page.html',
  styleUrls: ['./student-activity.page.scss'],
})
export class StudentActivityPage implements OnInit {

  @ViewChild('infiniteScrollActivity', {}) infiniteScrollActivity: IonInfiniteScroll;
  @ViewChild('listActivity', {}) listActivity: IonList;
  @ViewChild('contentActivity', {}) contentActivity: IonContent;

  // 活動
  public activityFilter: any = {
    actCodeOrName: '',
    actTerm: '',
    actCategory: '',
    actExpire: '',
    status: '',
    page_index: 1,
    page_size: 20
  }
  public total_count_activity = 0
  public listDataActivity: any = []
  public categorys: any = []

  public user: any = {}
  public personal = '1'
  constructor(
    public activityService: ActivityService,
    private nativeStorage: NativeStorage,
    public utils: UtilsService,
    public selectStudentService: SelectStudentService,
    private navCtrl: NavController,
    private router: Router,
    private loadingService: LoadingService,
    private events: Events,
  ) { }

  get actExpireStr() {
    console.log('actExpireStr', this.activityFilter.actExpire)
    if (this.activityFilter.actExpire) {
      return dateFormat(new Date(this.activityFilter.actExpire))
    }
    return '- -'
  }

  ngOnInit() {
    this.events.subscribe('login', () => {
      this.init()
    })
    this.events.subscribe('activity:changeStatus', ({id, status}) => {
      this.changeStatus(id, status)
    })
    this.init()
  }

  async init() {
    this.user = await this.nativeStorage.getItem('userInfo')
    if (this.user && this.user.access) {
      if (this.user.access.G01 && this.user.access.G01.length > 0) {
        this.personal = this.user.access.G01.substr(8, 1) || '0'
      }
    }
    this.initCategory()
    this.listDataActivity = []
    this.activityFilter = {
      actCodeOrName: '',
      actTerm: '',
      actCategory: '',
      actExpire: '',
      status: '',
      page_index: 1,
      page_size: 20
    }
  }
  async initCategory() {


    try {
      const res: any = await this.activityService.getAllCategory({})
      console.log(res)
      this.categorys = res
    } catch (e) {
      console.error(e)
    }
  }

  onSearchKeyUp(event) {
    if ('Enter' === event.key) {
      this.loadDataActivity()
    }
  }

  async loadDataActivity(event?, isNew = true, isRefresh = false) {
    await this.loadingService.start()
    let empty = false
    try {
      const { actTerm, actCodeOrName, actCategory, actExpire, page_index } = this.activityFilter
      let page = page_index
      if (isNew) {
        page = 1
        this.activityFilter.page_index = 1
        this.toggleInfiniteScrollActivity(false)
      }
      let user
      try {
        user = await this.nativeStorage.getItem('userInfo')
      } catch (e) {
        await this.loadingService.end()
        this.utils.showMsg('登錄失效，請重新登錄')
        if (event && event.target) {
          event.target.complete();
        }
        return
      }

      let expire = ''

      if (actExpire) {
        const d = new Date(actExpire)
        expire = dateFormat(d, 'yyyy-MM-dd')
      }

      const actYear = user.year_id
      const userNo = user.user_no
      // const res: any = await this.eventsService.searchEvents({
      //   keyword, system_id, status, urgent, only_mine,
      //   page_index, page_size, with_hide_data
      // })
      let actCreator = ''
      if (this.personal === '1') {
        actCreator = this.user.user_id
      }

      const res: any = await this.activityService.fetchStudentActivities({
        userNo,
        actCodeOrName,
        actYear,
        actTerm,
        actCategory,
        actExpire: expire,
        page
      })
      console.log(res)
      if (isNew) {
        this.listDataActivity = res.datas
      } else {
        this.listDataActivity.push(...res.datas)
      }

      this.total_count_activity = res.count
    } catch (e) {
      if (e && e.code) {
        if (e.code === 3002) {
          empty = true
          // await this.utils.showMsg(e.desc)
          if (isNew) {
            this.listDataActivity = []
          }
        }
      }
      console.error(e)
    }
    if (event && event.target) {
      event.target.complete();
      if (!isRefresh) { // !isRefresh 下拉刷新不需要禁用
        event.target.disabled = empty || (this.activityFilter.page_index * this.activityFilter.page_size) >= this.total_count_activity
      }
    } else {
      this.toggleInfiniteScrollActivity(empty || (this.activityFilter.page_index * this.activityFilter.page_size) >= this.total_count_activity)
    }
    if (isNew) {
      this.contentActivity.scrollToTop()
    }
    this.activityFilter.page_index += 1
    await this.loadingService.end()
  }
  toggleInfiniteScrollActivity(disabled) {
    console.log(disabled)
    this.infiniteScrollActivity.disabled = disabled
  }

  onCancelActExpire(actDatetime) {
    this.activityFilter.actExpire = undefined
    // actDatetime.reset()
    console.log('this.activityFilter.actExpire', this.activityFilter.actExpire)
  }
  getStatusClass(status) {
    const s = status + ''
    switch (s) {
      case '-1': // 活動已截止報名視作放棄
        return 'status--1'
      case '0': // 報名中
        return 'status-0'
      case '1': // 不同意參加
        return 'status-1'
      case '4': // 辦理中（頁面篩選狀態時，辦理中=4就行）
        return 'status-4'
      case '5': // 辦理中
        return 'status-5'
      case '7': // 抽籤失敗
        return 'status-7'
      case '10': // 取錄成功
        return 'status-10'
      case '11': // 通告
        return 'status-11'
      case '12': // 活動進行中
        return 'status-12'
      default:
        return 'status-default'
    }
    // -1=活動已截止報名視作放棄
    //     0=報名中
    //     1=不同意參加
    //     4=辦理中（頁面篩選狀態時，辦理中=4就行）
    // 5=辦理中
    //     7=抽籤失敗
    //     10=取錄成功
    //     11=通告
    //     12=活動進行中
  }
  getStatus(status) {
    const s = status + ''
    switch (s) {
      case '-1': // 活動已截止報名視作放棄
        return '活動已截止報名視作放棄'
      case '0': // 報名中
        return '報名中'
      case '1': // 不同意參加
        return '不同意參加'
      case '4': // 辦理中（頁面篩選狀態時，辦理中=4就行）
        return '辦理中'
      case '5': // 辦理中
        return '辦理中'
      case '7': // 抽籤失敗
        return '抽籤失敗'
      case '10': // 取錄成功
        return '取錄成功'
      case '11': // 通告
        return '通告'
      case '12': // 活動進行中
        return '活動進行中'
      default:
        return ''
    }
    // -1=活動已截止報名視作放棄
    //     0=報名中
    //     1=不同意參加
    //     4=辦理中（頁面篩選狀態時，辦理中=4就行）
    // 5=辦理中
    //     7=抽籤失敗
    //     10=取錄成功
    //     11=通告
    //     12=活動進行中
  }


  toDetailStudent(item) {
    this.navCtrl.setDirection('forward');
    this.router.navigate(['activity-detail-student', item.id], { queryParams: { view: 'signup' }})
  }

  changeStatus(id, status) {
    const arr = this.listDataActivity
    const itemIndex = arr.findIndex(i => i.id === id)
    if (itemIndex !== -1) {
      this.listDataActivity = []
      const item = arr[itemIndex]
      item.status = status
      arr[itemIndex] = item
      // setTimeout(() => {
      this.listDataActivity = arr.map(i => Object.assign({}, i))
      // }, 100)
    }
  }

}
