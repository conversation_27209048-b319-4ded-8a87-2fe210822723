import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StudentActivityPage } from './student-activity.page';

describe('StudentActivityPage', () => {
  let component: StudentActivityPage;
  let fixture: ComponentFixture<StudentActivityPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ StudentActivityPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StudentActivityPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
