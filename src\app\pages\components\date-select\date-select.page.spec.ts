import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DateSelectPage } from './date-select.page';

describe('DateSelectPage', () => {
  let component: DateSelectPage;
  let fixture: ComponentFixture<DateSelectPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DateSelectPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DateSelectPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
