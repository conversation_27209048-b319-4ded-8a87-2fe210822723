<ion-header class="inside-page">
  <ion-toolbar>
    <ion-buttons slot="start">
      <!--<ion-back-button (click)="onClose()"></ion-back-button>-->

      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>學生</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list>
    <ion-item
        *ngFor="let item of students; let i = index"
        class="student-item"
        lines="full" button detail
        (click)="onSave(item)"
    >
      <div class="index">{{ item.stuClassNo }}</div>
      <div class="student-info">
        <ion-note>{{ item.stuUname }}</ion-note>
        <ion-note>{{ item.stuEname }}</ion-note>
      </div>
    </ion-item>
  </ion-list>
  <div class="empty-tips" *ngIf="students.length === 0">班級暫無學生</div>

</ion-content>
