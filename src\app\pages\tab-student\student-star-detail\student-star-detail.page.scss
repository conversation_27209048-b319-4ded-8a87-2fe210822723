ion-header {
  .custom-segment-button {
    background-color: #FFFFFFFF;
    color: #000000FF;
    border: 0px;
    font-size: 22px;
    height: 40px;

    .pane {
      border: 2px solid #3D6EFFFF;
      position: absolute;
      bottom: 0px;
      width: 92px;
    }
  }
}
ion-header.filter {
  padding-bottom: 5px;
  font-size: 0.7rem;
  padding: 0.3rem 0.2rem;

  ion-select {
    background: #ffffff;
    border: 1px solid #EDEDED;
    border-radius: 5px;
    padding: 0.1rem 0.5rem;
    width: calc(100% - 50px);
  }
  ion-select.long_col {
    width: calc(100% - 184px);
  }
  ion-label.input-label {
    padding: 0.1rem 0.3rem;
  }

  ion-button {
    height: 1.25rem;
    vertical-align: middle;
    margin: 0 0.5rem;
  }

  ion-input {
    height: 1.25rem;
    background: #ffffff;
    border: 1px solid #EDEDED;
    border-radius: 5px;
  }

  .col {
    display: flex;
    padding: 0px 5px;
  }
}

ion-content {
  --background: #FFFFFF;
}


.list-item {
  margin: 4px 12px;
  box-shadow:1px 1px 3px 0px rgba(0,0,0,0.18);
  border-radius:4px;
  padding-left: 10px;

  background: #457DEE;
  &.success {
    background: #A0E4E4;
    .item-info .info .status {
      color: #A0E4E4;
    }
  }
  &.warning {
    background: #FF9900;
    .item-info .info .status {
      color: #FF9900;
    }
  }
  &.error {
    background: #FFAB9F;
    .item-info .info .status {
      color: #FFAB9F;
    }
  }

  &.tv-0 { background: #FFAB9F; .item-info .info .status { color: #FFAB9F; } }
  &.tv-1 { background: #61EAFF; .item-info .info .status { color: #61EAFF; } }
  &.tv-2 { background: #FFDB9E; .item-info .info .status { color: #FFDB9E; } }
  &.tv-3 { background: #C2FF9E; .item-info .info .status { color: #C2FF9E; } }
  &.status-default { background: #E6E6E6; .item-info .info .status { color: #E6E6E6; } }

  .item-info {
    padding: 5px 0;
    width: 100%;

    word-break: keep-all;
    white-space: nowrap;
    text-overflow: ellipsis;
    .project-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: calc(100% - 10px);
      width: 100%;
      font-weight:600;
      font-size:13px;
      color:rgba(96,98,102,1);
      padding-bottom: 5px;
    }
    .info {
      font-size:11px;
      color:rgba(96,98,102,1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: calc(100% - 5px);
      width: 100%;
      .status {
        color: #457DEE;
      }
      .time {
        float: right;
        color: #B0B0B0;
      }
      .list-star {
        font-size: 14px;
      }
      .icon-ios-star {
        color: #f5a623;
      }
    }
  }
}

.status_number_0 {
  font-size: 18px;
  color: #FFAB9F;
}

.status_number_1 {
  font-size: 18px;
  color: #61EAFF;
}

.status_number_2 {
  font-size: 18px;
  color: #FFDB9E;
}

.status_number_3 {
  font-size: 18px;
  color: #C2FF9E;
}


.status_number_4 {
  font-size: 18px;
  color: #FF7661;
}

.status_number_5 {
  font-size: 18px;
  color: #A0E4E4;
}

.status_number_6 {
  font-size: 18px;
  color: #9EF2FF;
}

.status_number_7 {
  font-size: 18px;
  color: #FF9900;
}

.total_number {
  font-size: 12px;
  color: #909399FF;
}

.detail_text {
  width: calc(100vw - 24px);
  text-align: right;
  font-size: 10px;
  color: #909399FF;
  font-weight: bold;
  margin: 0px 12px 8px 12px;
  float: right;
}
