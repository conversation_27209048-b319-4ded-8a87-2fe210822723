import { Component, OnInit } from '@angular/core';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {SystemMessageService} from '@services/api/system-message.service';
import {StoreService} from '@services/store/store.service';
import {NoticesService} from '@services/api/notices.service';
import {isNumber} from 'util';
import {Events} from '@app/services/events/event.service';

@Component({
  selector: 'app-tab-student',
  templateUrl: './tab-student.page.html',
  styleUrls: ['./tab-student.page.scss'],
})
export class TabStudentPage implements OnInit {

  private userInfo: any = {}
  private intervalLocal: any = 0
  private intervalRemote: any = 0
  public hasMsg: any = false
  constructor(
    private nativeStorage: NativeStorage,
    private systemMessageService: SystemMessageService,
    private storeService: StoreService,
    private noticesService: NoticesService,
    private events: Events,
    ) { }

  ngOnInit() {
    this.init()
    this.events.subscribe('login', () => {
      this.init()
    })
  }

  async init() {
    this.userInfo = await this.nativeStorage.getItem('userInfo')
    // await this.storeService.getMsgStatus()
    this.intervalLocal = window.setInterval(() => {
      this.loadMsgStatus()
    }, 30 * 1000)
    // this.intervalRemote = window.setInterval(() => {
    //   this.storeService.getMsgStatus()
    // }, 30 * 1000)

    this.loadMsgStatus()
    this.events.subscribe('notice:changeBadge', () => {
      this.loadMsgStatus()
    })
  }


  async loadMsgStatus() {
    try {
      const user_id = this.userInfo.user_id
      const admin_permission = '0'
      const page = '0'
      const res: any = await this.noticesService.fetchNotices({ user_id, admin_permission, page, ignore_error: true })
      if (res && res.hasOwnProperty('unread_number')) {
        if (isNumber(res.unread_number)) {
          this.hasMsg = res.unread_number > 0
        } else {
          const n = Number(res.unread_number)
          this.hasMsg = n > 0
        }
        if (this.hasMsg) {
          console.log('有未讀訊息', res.unread_number)
        } else {
          console.log('沒有未讀訊息', res.unread_number)
        }
      }
      console.log(res)
    } catch (e) {
      console.error(e)
    }
  }

  onClickNotice() {
    this.events.publish('notice:reload');
  }
}
