import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TextViewPage } from './text-view.page';

describe('TextViewPage', () => {
  let component: TextViewPage;
  let fixture: ComponentFixture<TextViewPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TextViewPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TextViewPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
