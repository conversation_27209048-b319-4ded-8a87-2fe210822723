import { Component, OnInit } from '@angular/core';
import {ActivityService} from '@services/api/activity.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {UtilsService} from '@app/utils/utils.service';
import {SelectStudentService} from '@services/utils/select-student.service';
import {NavController, AlertController} from '@ionic/angular';
import {Router} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import {RollCallService} from '@services/sql/rollCall.service';
import {del} from 'selenium-webdriver/http';
import {Network} from '@ionic-native/network/ngx';

@Component({
  selector: 'app-offline-roll-call-list',
  templateUrl: './offline-roll-call-list.page.html',
  styleUrls: ['./offline-roll-call-list.page.scss'],
})
export class OfflineRollCallListPage implements OnInit {

  public list: any = []
  public user: any = {}
  constructor(
    public activityService: ActivityService,
    private nativeStorage: NativeStorage,
    public utils: UtilsService,
    public selectStudentService: SelectStudentService,
    private navCtrl: NavController,
    private router: Router,
    private loadingService: LoadingService,
    private rollCallService: RollCallService,
    public alertController: AlertController,
    private network: Network
    ) { }

  async ngOnInit() {
    this.user = await this.nativeStorage.getItem('userInfo')
    this.init()
  }

  async init() {

    let isLogin = false
    try {
      const user = await this.nativeStorage.getItem('userInfo')
      if (user && user.user_id) {
        isLogin = true
      }
    } catch (e) {
      console.error(e)
    }
    const arr = await this.rollCallService.all()
    this.list = arr

    if (isLogin) {
      this.checkReload()
    }
  }

  async checkReload() {
    try {
      const activity_id_array = this.list.map(i => {
        return {
          activity_id: i.activity_id + '',
          actName: i.actName,
        }
      })
      const res: any = await this.activityService.checkReload(activity_id_array.map(i => i.activity_id))
      const updateArr = []
      const deleteArr = []

      for (const key in res) {
        if (res.hasOwnProperty(key)) {
          const act = activity_id_array.find(i => i.activity_id === key)
          if (res[key] === 1) {
            updateArr.push(act)
          } else if (res[key] === 2) {
            deleteArr.push(act)
          }
        }
      }
      if (deleteArr.length > 0) {
        const msg = '下列點名紙已失效，是否刪除？<br>' + deleteArr.map(a => `【${a.actName}】`).join('<br>')
        if (await this.utils.confirm('點名紙已失效', msg)) {
          await this.deleteAct(deleteArr)
        }
      }
      if (updateArr.length > 0) {
        const msg = '下列點名紙已變更，是否需要更新？<br>' + updateArr.map(a => `【${a.actName}】`).join('<br>')
        if (await this.utils.confirm('更新點名紙', msg)) {
          await this.update(updateArr)
        }
      }
      console.log(res)
    } catch (e) {
      console.error(e)
    }
  }
  async deleteAct(arr) {
    try {
      for (const act of arr) {
        await this.rollCallService.deleteByActId(act.activity_id)
      }
      this.init()
    } catch (e) {
      console.error(e)
    }
  }
  async update(arr) {
    await this.loadingService.start()
    for (const act of arr) {
      try {
        await this.rollCallService.updateActivity(act.activity_id)
      } catch (e) {
        console.error(e)
      }
    }
    this.init()
    this.utils.showToast({ msg: '更新完成'})
    await this.loadingService.end()
  }
  // async updateActivity(id) {
  //   try {
  //     const actInfo: any = await this.activityService.getActivityInfo({id})
  //     const rollCallInfo: any = await this.activityService.getRollCall({ activity_id: id })
  //     const lottery: any = await this.activityService.getActivityLottery(id)
  //
  //     const local: any = await this.rollCallService.getByActId(id)
  //
  //     // @ts-ignore
  //     for (let i = 0; i < local.activity_date.length; i++) {
  //       const localItem = local.activity_date[i]
  //       if (localItem) {
  //         const cItem = rollCallInfo.activity_date.find( item => item.actKey === localItem.actKey)
  //         if (cItem) {
  //           const years = Object.keys(localItem.actDateArray)
  //           for (let j = 0; j < years.length; j++) {
  //             const year = years[j]
  //             // localItem.actDateArray[]
  //             if (cItem.actDateArray.hasOwnProperty(year)) {
  //               const yearList = localItem.actDateArray[year] // 本地選擇年
  //
  //               for (let k = 0; k < yearList.length; k++) {
  //                 const dayInfo = yearList[k] // 本地日
  //                 const cDay = cItem.actDateArray[year].find(day => day.date === dayInfo.date) // 新日
  //                 if (cDay) {
  //                   for (let l = 0; l < dayInfo.students.length; l++) {
  //                     const stu = dayInfo.students[l] // 本地學生
  //                     const cStu = cDay.students.find(s => s.student_id === s.student_id) // 新學生
  //                     if (cStu) {
  //                       cStu.status = stu.status
  //                     } else {
  //                       // 新有，舊無
  //                     }
  //                   }
  //
  //                 } else {
  //                   // cItem.actDateArray.push(dayInfo)
  //                 }
  //               }
  //
  //             } else {
  //               // 年
  //               // cItem.actDateArray[year] = localItem.actDateArray[year]
  //             }
  //           }
  //
  //         } else {
  //           // 活動地點
  //           // rollCallInfo.activity_date.push(cItem)
  //         }
  //       }
  //     }
  //
  //     this.rollCallService.update({
  //       id: local.id,
  //       activity_id: local.activity_id,
  //         actCode: local.actCode,
  //         actName: local.actName,
  //         actEname: local.actEname,
  //         actTeacher: local.actTeacher,
  //         cateCode: local.cateCode,
  //         cateName: local.cateName,
  //         cateEnName: local.cateEnName,
  //         activity_date: local.activity_date,
  //
  //         activityInfo: actInfo,
  //         lottery,
  //     })
  //
  //
  //   } catch (e) {
  //     console.error(e)
  //   }
  //
  // }

  getStatusClass(status, actType) {
    switch (status) {
      case '0':
        return 'warning'
      case '2':
        return 'error'
      case '3':
        return 'success'
      case '4':
        return 'warning'
      case '4_1':
        return 'warning'
      case '4_2':
        return 'warning'
      case '5':
        return 'warning'
      case '5_1':
        return 'warning'
      case '5_2':
        if (actType === '5' || actType === '3') {
          return 'success'
        } else {
          return 'warning'
        }
      case '5_3':
        return 'warning'
      default:
        return 'error'
    }
  }
  getStatus(status) {
    switch (status) {
      case '0':
        return '審核中'
      case '2':
        return '不通過'
      case '3':
        return '已發通告'
      case '4_1':
        return '篩選中'
      case '4_2':
        return '開始篩選'
      case '5_1':
        return '完成篩選'
      default:
        return '未知'
    }
  }

  toDetail(item) {
    this.navCtrl.setDirection('forward');
    this.router.navigate(['offline-roll-call', item.id])
  }

  async onDelete(item) {
    console.log('delete: ', item)

    const confirm = await this.deleteConfirm(item.actName)
    if (confirm) {
      await this.rollCallService.delete(item.id)
      this.init()
    }
  }



  async deleteConfirm(name) {
    let confirm = false
    const alert = await this.alertController.create({
      header: '刪除點名紙',
      message: `確認刪除 ${name} ？`,
      backdropDismiss: false,
      buttons: [
        {
          text: '取消',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
            console.log('Confirm Cancel: blah');
          }
        }, {
          text: '確認',
          handler: () => {
            confirm = true
            console.log('Confirm Okay');
          }
        }
      ]
    });

    await alert.present();
    await alert.onDidDismiss();
    return confirm
  }

}
