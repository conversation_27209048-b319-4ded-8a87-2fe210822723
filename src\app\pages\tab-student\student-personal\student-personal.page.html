<ion-header class="center">
  <ion-toolbar>
    <ion-title>我的活動</ion-title>
  </ion-toolbar>
</ion-header>

<ion-header class="filter">
  <ion-row class="row" style="margin-bottom: 3px;">
    <ion-label class="input-label">學期</ion-label>
    <ion-select
        [(ngModel)]="filter.actTerm"
        placeholder="學期"
        okText="確認"
        cancelText="取消"
        interface="popover"
        (ngModelChange)="loadData()">
      <ion-select-option value="">全部</ion-select-option>
      <ion-select-option value="0">全學期</ion-select-option>
      <ion-select-option value="1">上學期</ion-select-option>
      <ion-select-option value="2">下學期</ion-select-option>
      <ion-select-option value="3">暑假期</ion-select-option>
    </ion-select>
  </ion-row>
</ion-header>
<!-- 活動內容 -->
<ion-content #content>

  <ion-refresher slot="fixed" [pullMax]="500" (ionRefresh)="loadData($event, true, true)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-list #listActivity *ngIf="listData.length > 0; else emptyTips">
    <ion-item
        *ngFor="let item of listData"
        detail
        button
        lines="none"
        class="list-item"
        [classList]="'list-item ' + getStatusClass(item.actStatus)"
        (click)="toDetail(item)"
    >
      <div class="item-info">
        <div class="project-name">{{ item.actName }}</div>
        <div class="info">編號：{{ item.actCode }}</div>
        <div class="info">負責老師：{{ item.actTeacher }}</div>
        <div class="info">{{ item.actCategory }}</div>
        <div class="info">狀態：{{ item.actStatus }}</div>
      </div>
    </ion-item>
  </ion-list>

  <ng-template #emptyTips>
    <!--<div class="page-empty-tips"></div>-->
    <div class="empty-tips">
      暫無數據
    </div>
  </ng-template>
  <ion-infinite-scroll #infiniteScroll threshold="10px" (ionInfinite)="loadData($event, false)">
    <ion-infinite-scroll-content
        loadingSpinner="bubbles"
        loadingText="正在加載...">
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
