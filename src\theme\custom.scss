:root {
  --notch-inset-top: 0px;
  --notch-inset-right: 0px;
  --notch-inset-bottom: 0px;
  --notch-inset-left: 0px;
}

/* Use it with var(--notch-inset-top) now, for example: */
/* Android 頂部缺口邊距 */
html[mode="md"] {
  body {
    --ion-safe-area-top: var(--notch-inset-top);
  }
}
ion-header ion-toolbar:first-of-type {
  padding-top: 0;
}
@-webkit-keyframes rightIn {
  from {
    opacity: 0;
    height: 0;
    /* -webkit-transform: scale3d(.3, .3, .3); */
    /* transform: scale3d(.3, .3, .3); */
    transform: translateX(-90%) translateX(0)
  }
  50% {
    opacity: 1;
  }
  to {
    height: auto;
  }
}

@keyframes rightIn {
  from {
    opacity: 0;
    height: 0;
    /* -webkit-transform: scale3d(.3, .3, .3); */
    /* transform: scale3d(.3, .3, .3); */
    transform: translateX(90%) translateX(0)
  }
  50% {
    opacity: 1;
  }
  to {

    height: auto;
  }
}

@-webkit-keyframes rightOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  to {
    height: 0;
    opacity: 0;
    transform: translateX(90%) translateX(0)
  }
}

@keyframes rightOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  to {
    height: 0;
    opacity: 0;
    transform: translateX(90%) translateX(0)
  }
}

.item-enter {

  -webkit-animation-name: rightIn;
  animation-name: rightIn;
  -webkit-animation-duration: .3s;
  animation-duration: .3s;
  -webkit-animation: rightIn .3s;
  animation: rightIn .3s;
}

.item-leave {
  -webkit-animation-name: rightOut;
  animation-name: rightOut;
  -webkit-animation-duration: .3s;
  animation-duration: .3s;
  -webkit-animation: rightOut .3s;
  animation: rightOut .3s;
}


ion-app.cameraView, ion-app.cameraView ion-content, ion-app.cameraView .nav-decor {
  background: transparent none !important;
}


ion-app {

  .content-item {
    padding: 14px 27px;
    //--padding-start: 27px;
    //--padding-end: 27px;
    //--padding-top: 14px;
    //--padding-bottom: 14px;
  }

  ion-button.button-disabled {
    --background: #B3B3B3;
  }

  .utils-toast {
    //transform: translateY(-56px) !important;
    --ion-safe-area-bottom: 56px;
  }

  .iconfont {
    font-size: 1rem;
  }
}

html {
  font-size: 5.33333vw;
  //line-height: 0.7rem;
}

@media screen and (max-width: 320px) {
  html {
    font-size:42.667px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 321px) and (max-width:360px) {
  html {
    font-size:48px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 361px) and (max-width:375px) {
  html {
    font-size:50px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 376px) and (max-width:393px) {
  html {
    font-size:52.4px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 394px) and (max-width:412px) {
  html {
    font-size:54.93px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 413px) and (max-width:414px) {
  html {
    font-size:55.2px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 415px) and (max-width:480px) {
  html {
    font-size:64px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 481px) and (max-width:540px) {
  html {
    font-size:72px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 541px) and (max-width:640px) {
  html {
    font-size:85.33px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 641px) and (max-width:720px) {
  html {
    font-size:96px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 721px) and (max-width:768px) {
  html {
    font-size:102.4px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 769px) {
  html {
    font-size:102.4px;
    font-size: 5.33333vw
  }
}

body {
  font-family: "PingFangSC-Regular","Microsoft YaHei",Helvetica;
  background: #f5f7f9
}

ion-content {
  --background: #F9F9F9;
  > ion-list {
    background: #F9F9F9;
  }
  i, ion-icon {
    height: 20px;
    width: 20px;
    color: #c0c0c0;
  }
  ion-fab-button {
    i, ion-icon {
      color: #FFFFFF;
    }
  }
}

/* 標題居中 */
ion-header {
  ion-toolbar {
    ion-title {
      //text-align: center;
      //position: absolute;
      width: 100%;
      top: 0;
      left: 0;
      font-size: 18px;
    }
    ion-button {
      font-size: 13px;
    }
  }
  &.md {
    ion-toolbar {
      ion-title {
        min-height: 56px;
      }
    }
  }
  &.ios {
    ion-toolbar {
      ion-title {
        min-height: 44px;
      }
    }
  }
  &.center {

    ion-toolbar {
      ion-title {
        text-align: center;
        position: absolute;
      }
    }
  }
}
/* 內頁
ion-header.inside-page {
  background: #457DEE;
  --background: #457DEE;
  color: #ffffff;
  ion-toolbar {
    --background: inherit;
    --color: inherit;
    ion-buttons {
      ion-back-button:not(.ion-color) {
        //color: #FFFFFF;
      }
    }
  }
  &:not(.ion-color) {
    ion-toolbar:not(.ion-color) {
      ion-back-button, ion-buttons {
        color: #FFFFFF;
        ion-button {
          color: #FFFFFF;
        }
      }
    }
  }
  i, ion-icon {
    color: #FFFFFF;
  }
}

 */


ion-infinite-scroll {
  font-size: 14px;
}



.page-empty-tips {
  color: #e0e0e0;
  font-size: 20px;

  text-align:center;
  width:100%;
  height:100%;
  display:table;
  &:after {
    content: "- 空 -";
    display: table-cell;
    vertical-align: middle;
  }
}

.empty-tips {
  color: #e0e0e0;
  font-size: 0.8rem;
  text-align: center;
  width: 100%;
  height: 20vh;
  display: flex;
  justify-content: center;
  justify-items: center;
  flex-direction: column;
}






.picker-button.sc-ion-picker-md {
  vertical-align: text-top;
}


.header-md{
  margin-bottom: 1px;
  &:after {
    height: 1px;
    bottom: -1px;
    background-image: none;
    border-bottom: 1px solid #a7a7a78f;
  }
  &.none-border {
    margin-bottom: 0;
    &:after {
      height: 0;
      border: none;
    }
  }
}

/* 自定義顏色 */

.ion-color-search {
  --ion-color-base: #f5a623;
  --ion-color-base-rgb: 245,166,35;
  --ion-color-contrast: #ffffff;
  --ion-color-contrast-rgb: 255,255,255;
  --ion-color-shade: #c8891d;
  --ion-color-tint: #e29323;
  --padding-start: 2em;
  --padding-end: 2em;
}
.ion-color-white {
  --ion-color-base: #FFFFFF;
  --ion-color-base-rgb: 255,255,255;
  --ion-color-contrast: #6A6A6A;
  --ion-color-contrast-rgb: 106,106,106;
  --ion-color-shade: #F6F6F6;
  --ion-color-tint: #EBEBEB;
  --padding-start: 1em;
  --padding-end: 1em;
  height: 2rem;
  --box-shadow: 0 0 4px 0px #0000003d;
}


.ion-color-green {
  --ion-color-base: #80B777;
  --ion-color-base-rgb: 128,183,119;
  --ion-color-contrast: #ffffff;
  --ion-color-contrast-rgb: 255,255,255;
  --ion-color-shade: #6da464;
  --ion-color-tint: #78ad6f;
  --padding-start: 1em;
  --padding-end: 1em;
  height: 2rem;
}
.ion-color-yellow {
  --ion-color-base: #F5A623;
  --ion-color-base-rgb: 245,166,35;
  --ion-color-contrast: #ffffff;
  --ion-color-contrast-rgb: 255,255,255;
  --ion-color-shade: #dc9422;
  --ion-color-tint: #e09323;
  --padding-start: 1em;
  --padding-end: 1em;
  height: 2rem;
}


.button-mini {
  --border-radius: 6px;
  --padding-top: 0;
  --padding-start: 0.7em;
  --padding-end: 0.7em;
  --padding-bottom: 0;
  height: 2em;
  font-size: 12px;
}



ion-list {
  .sc-ion-select-popover-ios.ios.list-ios {
    margin: 0;
  }
}

/*     ios      */
ion-input.ios {
  input.native-input {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0;
    padding-bottom: 0;
  }
}
ion-textarea.ios {
  .native-textarea {
    padding: 0.5rem;
  }
}
ion-select-popover.ios {
  ion-list.sc-ion-select-popover-ios {
    margin-bottom: 0;
  }
}

ion-popover {
  .popover-wrapper {
    .popover-content {
      max-width: 70%;
      min-width: 50%;
      width: auto;
    }
  }
}
/* ios顯示 */
html[mode="ios"] {
  .ion-page {
    width: 100%;
    height: 100%;
  }
}
ion-popover [popover]:not(:popover-open):not(dialog[open]) {
  display: contents;
}

//app-root > ion-app {
//  margin-bottom: var(--ion-soft-button);
//}
