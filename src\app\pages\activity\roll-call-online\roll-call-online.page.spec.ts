import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RollCallOnlinePage } from './roll-call-online.page';

describe('RollCallOnlinePage', () => {
  let component: RollCallOnlinePage;
  let fixture: ComponentFixture<RollCallOnlinePage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RollCallOnlinePage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RollCallOnlinePage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
