import { Component, OnInit } from '@angular/core';
import {NavController, Platform, AlertController} from '@ionic/angular'
import {Router, Route, ActivatedRoute, NavigationExtras, UrlTree} from '@angular/router';
import { EventsService } from '@services/api/events.service';
import {EventUtilsService} from '@app/utils/event-utils.service';
// import {NavigationOptions} from '@ionic/angular/dist/providers/nav-controller';
import {LoadingService} from '@app/utils/loading.service';
import {TextViewService} from '@services/utils/text-view.service';
import {dateFormat} from '@app/utils';

@Component({
  selector: 'app-event-detail',
  templateUrl: './event-detail.page.html',
  styleUrls: ['./event-detail.page.scss'],
})
export class EventDetailPage implements OnInit {

  // data
  private id = ''
  private isInit = false
  public data: any = {}
  public showFreeReason = true
  constructor(
    private platform: Platform,
    private routeInfo: ActivatedRoute,
    private eventsService: EventsService,
    private eventUtilsService: EventUtilsService,
    public nav: NavController,
    private loadingService: LoadingService,
    private alertController: AlertController,
    private textViewService: TextViewService,
  ) { }

  ngOnInit() {
    this.platform.ready().then(() => {
      // this.statusBar.backgroundColorByHexString('#003658')
      // this.id = this.routeInfo.snapshot.queryParams['id']
      this.id = this.routeInfo.snapshot.params.id
      this.loadData()
    })
  }

  async loadData() {
    await this.loadingService.start()
    try {
      const res: any = await this.eventsService.getEvent(this.id)
      console.log(res)
      this.data = res
      this.isInit = true
      this.showFreeReason = res.free === 0
    } catch (e) {
      console.error(e)
    }
    await this.loadingService.end()
  }

  async onPass() {
    const alert = await this.alertController.create({
      // header: '通過',
      message: '是否通過並離開？',
      buttons: [
        {
          text: '否',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
            // console.log('Confirm Cancel: blah');
          }
        }, {
          text: '是',
          handler: () => {
            // console.log('Confirm Okay');
            this.handlePass()
          }
        }
      ]
    });

    await alert.present();
  }
  async handlePass() {
    await this.loadingService.start()
    try {
      const event_id = this.id
      const status = 'C'
      const arrange_user_id = ''
      const remark = ''
      const res = await this.eventsService.updateEventStatus({
        event_id,
        status,
        arrange_user_id,
        remark,
      })
      // console.log(res)
      this.nav.pop()
    } catch (e) {
      console.error(e)
    }
    await this.loadingService.end()
  }

  get canSubmit() {
    if (this.data) {
      const status = this.data.status
      if (status === 'P' || status === '') {
        return true
      }
    }
    return false
  }

  getUrgent(v) {
    const text = this.eventUtilsService.urgentList[v]
    return text ? text : '-'
  }
  getStatus(v) {
    const text = this.eventUtilsService.statusList[v]
    return text ? text : '-'
  }
  getDevProcess(v) {
    const text = this.eventUtilsService.statusList[v]
    return text ? text : '-'
  }


  /* ---------------------------------------------------------- */
  onNameView() {
    this.showText('修改標題', this.data.name)
  }
  onContentView() {
    this.showText('修改內容', this.data.content)
  }
  onRemarkView() {
    this.showText('備註', this.data.remark)
  }

  async showText(title, content) {
    await this.textViewService.showTextView(title, content)

    // const navigationExtras: NavigationExtras = {
    //   queryParams: {
    //     title,
    //     content,
    //   },
    //   skipLocationChange: true
    // }
    // // @ts-ignore
    // this.nav.navigateForward(['text-view'], navigationExtras);
  }
  showChild() {
    const navigationExtras: NavigationExtras = {
      queryParams: {
        data: JSON.stringify(this.data.tasks)
      },
      skipLocationChange: true
    }
    // @ts-ignore
    this.nav.navigateForward(['event/event-child'], navigationExtras);
  }
  showAttaches() {
    const navigationExtras: NavigationExtras = {
      queryParams: {
        data: JSON.stringify(this.data.attaches)
      },
      skipLocationChange: true
    }
    // @ts-ignore
    this.nav.navigateForward(['event/event-attaches'], navigationExtras);
  }

  formatDateTime(datetime, format = 'yyyy-MM-dd') {
    if (datetime) {
      const str = datetime.replace(new RegExp(/-/gm), '/')
      return dateFormat(new Date(str), format)
    }
    return '-'
  }

  get showDeadline() {
    return this.data.urgent === 'H'
  }

}
