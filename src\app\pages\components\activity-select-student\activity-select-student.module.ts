import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { ActivitySelectStudentPage } from './activity-select-student.page';

const routes: Routes = [
  {
    path: '',
    component: ActivitySelectStudentPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  // declarations: [ActivitySelectStudentPage]
})
export class ActivitySelectStudentPageModule {}
