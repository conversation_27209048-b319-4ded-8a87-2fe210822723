import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { NoticeDetailPage } from './notice-detail.page';
import { NoticeFilePage } from '@pages/notices/notice-file/notice-file.page';

import { NoticeStudentListPageModule} from '@pages/notices/notice-student-list/notice-student-list.module';
const routes: Routes = [
  {
    path: '',
    component: NoticeDetailPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    NoticeStudentListPageModule
  ],
  declarations: [NoticeDetailPage, NoticeFilePage],
  entryComponents: [NoticeFilePage],
})
export class NoticeDetailPageModule {}
