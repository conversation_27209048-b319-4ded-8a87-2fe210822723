import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { EventAttachesPage } from './event-attaches.page';

describe('EventAttachesPage', () => {
  let component: EventAttachesPage;
  let fixture: ComponentFixture<EventAttachesPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EventAttachesPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EventAttachesPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
