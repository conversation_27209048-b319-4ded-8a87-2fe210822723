import { Animation } from '@ionic/core';

export function rightEnterAnimation(AnimationC: Animation, baseEl: HTMLElement): Promise<Animation> {

    let baseAnimation: Animation;
    // @ts-ignore
    // tslint:disable-next-line:prefer-const
    let backdropAnimation: Animation;
    let wrapperAnimation: Animation;
    // @ts-ignore
    baseAnimation = new AnimationC();

    // @ts-ignore
    // tslint:disable-next-line:label-position
    backdropAnimation: Animation = new AnimationC();
    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop'));

    // @ts-ignore
    wrapperAnimation = new AnimationC();
    wrapperAnimation.addElement(baseEl.querySelector('.modal-wrapper'));

    wrapperAnimation.beforeStyles({
        opacity: 1
    })
        // .fromTo('opacity', 0, 1)
        .fromTo('translateX', '100%', '0%');

    backdropAnimation.fromTo('opacity', 0.01, 0.4);

    return Promise.resolve(baseAnimation
        .addElement(baseEl)
        .easing('cubic-bezier(0.36,0.66,0.04,1)')
        .duration(250)
        .beforeAddClass('show-modal')
        .addAnimation(backdropAnimation)
        .addAnimation(wrapperAnimation));

}

export function rightLeaveAnimation(AnimationC: Animation, baseEl: HTMLElement): Promise<Animation> {

    // @ts-ignore
    const baseAnimation = new AnimationC();

    // @ts-ignore
    const backdropAnimation = new AnimationC();
    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop'));

    // @ts-ignore
    const wrapperAnimation = new AnimationC();
    const wrapperEl = baseEl.querySelector('.modal-wrapper');
    wrapperAnimation.addElement(wrapperEl);
    // const wrapperElRect = wrapperEl.getBoundingClientRect();

    wrapperAnimation.beforeStyles({opacity: 1})
        // .fromTo('translateX', '0%', `${window.innerWidth - wrapperElRect.left}px`);
        .fromTo('translateX', '0%', `100%`);

    backdropAnimation.fromTo('opacity', 0.4, 0.0);

    return Promise.resolve(baseAnimation
        .addElement(baseEl)
        .easing('ease-out')
        .duration(400)
        .add(backdropAnimation)
        .add(wrapperAnimation));

}
