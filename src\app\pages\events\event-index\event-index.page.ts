import { Component, OnInit, ViewChild } from '@angular/core';
import { IonInfiniteScroll, IonList, IonContent } from '@ionic/angular';
import {SystemsService} from '@services/api/systems.service';
import {EventsService} from '@services/api/events.service';
import {EventUtilsService} from '@app/utils/event-utils.service';
import {NavigationEnd, Router} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import { NavController } from '@ionic/angular';

import { Subscription } from 'rxjs';
import {dateFormat} from '@app/utils';
@Component({
  selector: 'app-event-index',
  templateUrl: './event-index.page.html',
  styleUrls: ['./event-index.page.scss'],
})
export class EventIndexPage implements OnInit {
  @ViewChild('infiniteScroll', {}) infiniteScroll: IonInfiniteScroll;
  @ViewChild('list', {}) list: IonList;
  @ViewChild('content', {}) content: IonContent;

  public filter = {
    keyword: '',
    system_id: '',
    status: '',
    urgent: '',
    only_mine: false,
    page_index: 1,
    page_size: 10
  }
  public systems: any = []
  // public statusList = {
  //   X: '待提交',
  //   C: '待確認',
  //   A: '待安排',
  //   S: '待開始',
  //   H: '進行中',
  //   T: '待測試',
  //   D: '已完成',
  //   E: '已關閉',
  // }
  // public statusClassList = {
  //   X: 'normal',
  //   C: 'normal',
  //   A: 'normal',
  //   S: 'normal',
  //   H: 'warning',
  //   T: 'normal',
  //   D: 'success',
  //   E: 'error',
  // }
  // public urgentList = {
  //   H: '高',
  //   L: '普通',
  //   E: '優化',
  // }
  public listData: any = []
  public total_count = 0
  public loading = 0

  public sysSelectOptions = {
    header: '歸屬系統',
  }
  public statusSelectOptions = {
    header: '狀態',
  }
  public urgentSelectOptions = {
    header: '緊急程度',
  }

  private subscription: Subscription;
  constructor(
    private router: Router,
    private systemService: SystemsService,
    private eventsService: EventsService,
    private eventUtilsService: EventUtilsService,
    private loadingService: LoadingService,
    private navCtrl: NavController,
  ) { }

  async ngOnInit() {

    await this.onEnter(true);
    this.subscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd && event.url === '/tab-user/eventIndex') {
        this.onEnter();
      }
    });
    this.init()
  }
  async onEnter(init = false) {
    // do your on enter page stuff here
    console.log('onEnter', init)
    if (init) { return }
    try {
      await this.loadingService.start()
      await this.loadData()
    } catch (e) {
    }
    this.loadingService.end()
  }

  async init() {
    try {
      await this.loadingService.start()
      await this.initSystems()
      await this.loadData(null, true)
    } catch (e) {
    }
    this.loadingService.end()
  }

  async initSystems() {
    try {
      const res = await this.systemService.fetchSystems()

      console.log(res)
      this.systems = res
    } catch (e) {
      console.error(e)
    }
  }

  async loadData(event?, isNew = true, isRefresh = false) {
    try {
      const { keyword, system_id: systemId, status, urgent, only_mine: onlyMine, page_index: pageIndex, page_size } = this.filter
      const system_id = systemId
      const only_mine = onlyMine ? '1' : '0'
      const with_hide_data = '0'
      let page_index = pageIndex
      if (isNew) {
        page_index = 1
        this.filter.page_index = 1
        this.toggleInfiniteScroll(false)
      }
      const res: any = await this.eventsService.searchEvents({
        keyword, system_id, status, urgent, only_mine,
        page_index, page_size, with_hide_data
      })
      if (isNew) {
        this.listData = res.data
      } else {
        this.listData.push(...res.data)
      }

      this.total_count = res.total_count
    } catch (e) {
      console.error(e)
    }
    if (event && event.target) {
      event.target.complete();
      if (!isRefresh) { // !isRefresh 下拉刷新不需要禁用
        event.target.disabled = (this.filter.page_index * this.filter.page_size) >= this.total_count
      }
    } else {
      this.toggleInfiniteScroll((this.filter.page_index * this.filter.page_size) >= this.total_count)
    }
    if (isNew) {
      this.content.scrollToTop()
    }
    this.filter.page_index += 1
  }

  toggleInfiniteScroll(disabled) {
    console.log(disabled)
    this.infiniteScroll.disabled = disabled
  }


  get systemText() {
    if (this.filter.system_id) {
      const system = this.systems.find(i => i.system_id === this.filter.system_id)
      if (system) {
        return system.system_name
      }
    }
    return '歸屬系統'
  }
  get statusText() {
    if (this.filter.status) {
      return this.eventUtilsService.statusList[this.filter.status]
    } else {
      return '狀態'
    }
  }
  get urgentText() {
    if (this.filter.urgent) {
      return this.eventUtilsService.urgentList[this.filter.urgent]
    } else {
      return '緊急程度'
    }
  }
  getUrgent(v) {
    const text = this.eventUtilsService.urgentList[v]
    return text ? text : '-'
  }
  getStatus(v) {
    const text = this.eventUtilsService.statusList[v]
    return text ? text : '-'
  }
  getDevProcess(v) {
    const text = this.eventUtilsService.devProcessList[v]
    return text ? text : '-'
  }
  getStatusClass(v) {
    const text = this.eventUtilsService.statusClassList[v]
    return text ? text : '-'
  }


  toDetail(item) {
    this.navCtrl.setDirection('forward');
    this.router.navigate(['/event/event-detail', item.event_id])
  }

  formatDateTime(datetime, format = 'yyyy-MM-dd') {
    if (datetime) {
      const str = datetime.replace(new RegExp(/-/gm), '/')
      return dateFormat(new Date(str), format)
    }
    return '-'
  }
}
