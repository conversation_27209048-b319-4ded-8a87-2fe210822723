{"compileOnSave": false, "compilerOptions": {"baseUrl": "./src", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "esnext", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "paths": {"@app/*": ["app/*"], "@assets/*": ["assets/*"], "@pages/*": ["app/pages/*"], "@services/*": ["app/services/*"], "stream": ["./node_modules/stream-browserify"]}}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true}}