import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {UserPopoverComponent} from './user-popover.component'
import {IonicModule} from '@ionic/angular'
import {TranslateModule} from '@ngx-translate/core';

@NgModule({
  declarations: [UserPopoverComponent],
  entryComponents: [UserPopoverComponent],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
  ],
  exports: [UserPopoverComponent]
})
export class UserPopoverModule { }
