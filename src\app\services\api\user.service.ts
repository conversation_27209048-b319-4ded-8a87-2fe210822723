import {Injectable} from '@angular/core'

import {RequestService} from '../request/request.service'

@Injectable({
  providedIn: 'root'
})
export class UserService {

  constructor(public request: RequestService) {
  }


  login(username: any, password: any) {
    return this.request.request({
      url: '/v2/app/user/login',
      method: 'post',
      data: {
        username,
        password
      }
    })
  }

  getUsers({
             nickname,
             enable,
             level,
             order_by
           }: {
    nickname?: any, // 用戶名稱
    enable?: any,  // 1=激活,0=不激活
    level?: any, // A=管理,U=普通用戶,MA=保養管理員
    order_by?: any // 排序sql
  }) {
    return this.request.request({
      url: '/users',
      method: 'get',
      params: {
        nickname,
        enable,
        level,
        order_by
      }
    })
  }

  getUserInfoByToken() {
    return this.request.request({
      url: '/users/me',
      method: 'get'
    })
  }

  getUserInfo(user_id: string) {
    return this.request.request({
      url: '/users/actions/inquire',
      method: 'get',
      params: {
        user_id
      }
    })
  }

  resetPassword({
                  password,
                  new_password
                }: {
    password?: string, // 舊密碼
    new_password?: string,  // 新密碼
  }) {
    return this.request.request({
      url: '/users/actions/reset-password',
      method: 'post',
      data: {
        password,
        new_password
      }
    })
  }

  /*                ------------             */
  /**
   * 主頁校曆
   */
  getUserCalendar({user_id, begin_date, end_date, personal}: {
    user_id: string, // 用戶id
    begin_date: string, // [string] 開始日期
    end_date: string, // [string] 結束日期
    personal: string
  }) {
    return this.request.request({
      url: `/v2/app/user/${user_id}/calendar`,
      method: 'GET',
      params: {
        user_id,
        begin_date,
        end_date,
        personal
      }
    })
  }

  /**
   * 學生個人信息
   */
  getStudentPersonProfile(userNo: string) {
    return this.request.request({
      url: `/extra/student_person_profile`,
      method: 'GET',
      params: {
        userNo
      }
    })
  }

  /**
   * 修改學生個人信息
   */
  editStudentPersonProfile({
                             userNo,
                             username,
                             pass,
                             email
                           }: {
    userNo: string, // [string] 學生編號
    username: string, // [string] 用戶名
    pass: string, // [string] 密碼
    email: string, // [string] 電郵
  }) {
    return this.request.request({
      url: `/extra/student_person_profile_edit`,
      method: 'POST',
      data: {
        userNo,
        username,
        pass,
        email
      }
    })
  }

  /**
   * 登錄時修改學生資料
   */
  editStudentInfo({
                    userId,
                    stuPhone,
                    stuParent,
                    password,
                    email
                  }: {
    userId: string, // [integer] 用戶ID
    stuPhone: string, // [string] 電話
    stuParent: string, // [string] 家長姓名
    password: string, // [string] 密碼
    email: string, // [string] 電郵
  }) {
    return this.request.request({
      url: `/master/student_login_save`,
      method: 'POST',
      data: {
        userId,
        stuPhone,
        stuParent,
        password,
        email
      }
    })
  }
  /**
   * 根據ID獲取學生資料
   */
  getStudentInfo({ id, userId }: any) {
    return this.request.request({
      url: `/master/student_info`,
      method: 'GET',
      params: {
        id,
        userId
      }
    })
  }
  /**
   * 直接修改用戶密碼
   */
  changePassword({
        userId,
        password
      }: any) {
    return this.request.request({
      url: `/master/user_change_pwd`,
      method: 'POST',
      data: {
        userId,
        password
      }
    })
  }
}
