import { Component, OnInit, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-text-view',
  templateUrl: './text-view.page.html',
  styleUrls: ['./text-view.page.scss'],
})
export class TextViewPage implements OnInit {
  @Input() title: string;
  @Input() content: string;
  @Input() canEdit: boolean;
  public show = false
  // public title = ''
  // public content = ''
  constructor(
    private route: ActivatedRoute,
    private modalCtrl: ModalController
  ) {}

  ngOnInit() {
    // this.route.queryParams.subscribe(params => {
    //   this.title = params.title
    //   this.content = params.content
    // });
    setTimeout(() => this.show = true, 50)
  }

  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true
    });
  }
  onSave() {
    this.modalCtrl.dismiss({
      save: true,
      content: this.content
    });
  }


}
