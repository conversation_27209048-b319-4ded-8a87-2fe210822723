<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <!--<ion-back-button (click)="onClose()"></ion-back-button>-->

      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>查看附件</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid *ngIf="data.length > 0; else emptyTips">
    <ion-row>
      <ion-col *ngFor="let item of data" size="4" class="ion-activatable" (click)="openFile(item)">
        <i [classList]="getFileIconClass(item.type)"></i>
        <ion-text>{{ item.name }}</ion-text>
        <ion-ripple-effect></ion-ripple-effect>
      </ion-col>
    </ion-row>
  </ion-grid>
  <ng-template #emptyTips>
    <div class="page-empty-tips"></div>
  </ng-template>
</ion-content>
