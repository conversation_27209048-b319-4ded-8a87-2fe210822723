<div class="scanner-bar">
  <ion-toolbar
      class="scanner-bar-tool"
      [ngClass]="currentClass"
  >
    <ion-label slot="start" class="label">{{ label }}</ion-label>
    <form class="form">
      <ion-input
          [(ngModel)]="value"
          name="value"
          class="form-input"
          inputmode="search"
          type="search"
          [clearInput]="true"
          [autocorrect]="true"
          [placeholder]="placeholder"
          (change)="handleChange()"
          (keyup.enter)="handleSubmit()"
      ></ion-input>
    </form>
    <ion-buttons slot="end">
      <ion-button class="icon-btn" (click)="handleScan()">
<!--        <ion-icon class="icon" name="qr-scanner"></ion-icon>-->

        <i class="iconfont icon-scan"></i>
      </ion-button>
    </ion-buttons>
    <!--    <ion-icon class="icon" slot="end" name="qr-scanner"></ion-icon>-->
  </ion-toolbar>
</div>
