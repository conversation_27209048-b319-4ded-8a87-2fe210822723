import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StudentIndexPage } from './student-index.page';

describe('StudentIndexPage', () => {
  let component: StudentIndexPage;
  let fixture: ComponentFixture<StudentIndexPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ StudentIndexPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StudentIndexPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
