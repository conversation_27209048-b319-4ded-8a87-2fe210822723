import { Injectable } from '@angular/core';
import {SqlService} from '@services/sql/sql.service'
import { dateFormat} from '@app/utils';
import {Platform} from '@ionic/angular';
import {ActivityService} from '@services/api/activity.service';

@Injectable({
  providedIn: 'root'
})
export class UserListService {

  constructor(
    private sql: SqlService,
    private platform: Platform,
    private activityService: ActivityService,
    private userListService: UserListService,
    ) {
    this.platform.ready()
      .then(async () => {
        this.sql.init()
      })
  }
  get isInit() {
    return this.sql.isInit
  }

  getCount() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT count(id) as count FROM tbl_user_list', [],
          (x, rs) => {
            console.log(rs)
            const res = rs.rows.item(0)
            resolve(res.count)
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  all() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_user_list order by id desc', [],
          (x, rs) => {
            const arr = []
            for (let i = 0; i < rs.rows.length; i++) {
              const item = rs.rows.item(i)
              console.log('sql row:', item)

              arr.push({
                id: item.id, // 本地ID

                username: item.username,
                password: item.password,
              })
            }
            resolve(arr)
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  get(id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_user_list WHERE id=(?)', [id],
          (x, rs) => {
            const item = rs.rows.item(0)
            console.log('sql row:', item)

            const data = {
                id: item.id, // 本地ID

                username: item.username,
                password: item.password,
              }
            resolve(data)
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  getByUsername(username) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_user_list WHERE username=(?)', [username],
          (x, rs) => {
          if (rs.rows.length > 0) {
            const item = rs.rows.item(0)
            console.log('sql row:', item)

            const data = {
              id: item.id, // 本地ID

              username: item.username,
              password: item.password,
            }
            resolve(data)
          } else {
            resolve(null)
          }
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  add({ username, password }) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }

      this.getByUsername(username).then((res: any) => {
        // 已存在
        this.update({ id: res.id, username, password }).then(r => {
          this.all().then(arr => {
            resolve(arr)
          }).catch(err => {
            console.error(err)
            reject(err)
          })
        }).catch(err => {
          reject(err)
        })

      }).catch(() => {
        // 不存在
        this.sql.db.transaction(tx => {
          tx.executeSql(
            'INSERT INTO tbl_user_list (username, password) VALUES (?,?)',
            [username, password],
            (x, rs) => {
              this.all().then(arr => {
                resolve(arr)
              }).catch(err => {
                console.error(err)
                reject(err)
              })
            }, (x, error) => {
              console.log('SELECT error: ' + error.message);
              reject(error)
            });
        })
      })
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
    })
  }
  delete(id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM tbl_user_list WHERE id=(?)',

          [id],
          (x, rs) => {
            this.all().then(arr => {
              resolve(arr)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  deleteByUsername(username) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM tbl_user_list WHERE username=(?)',

          [username],
          (x, rs) => {
            this.all().then(arr => {
              resolve(arr)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  update({ id, username, password }) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'UPDATE tbl_user_list SET username=(?),password=(?) WHERE id=(?)',

          [username, password, id],
          (x, rs) => {
            this.all().then(arr => {
              resolve(arr)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
}
