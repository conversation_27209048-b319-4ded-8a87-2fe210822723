ion-content {
  //--background: #FFFFFF;
  --padding-bottom: 1rem;
  --padding-top: 1rem;
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;

  .form-item {
    font-size: 0.7rem;
    margin-bottom: 0.3rem;

    flex-direction: row;

    .label {

      display: inline-block;
      line-height: 1.25rem;
      margin-right: 0.3rem;
    }

    .value {
      display: inline-block;

        ion-input {
          height: 1.5rem;
        }
        ion-input,ion-textarea {

          border: 1px solid #EDEDED;
          border-radius: 0.3rem;
          line-height: 1.25rem;
          width: 5rem;
          background: #FFFFFF;

        }
    }

    .btn {
      display: inline-block;

      ion-button {

        vertical-align: middle;
        margin: 0 0.5rem;
      }
    }

    &.target {

    }
    &.title {
      .value {
        flex: 1;
        ion-input {
          width: 100%;
        }
      }
    }
    &.content {
      .value {
        flex: 1;
        ion-textarea {
          width: 100%;
        }
      }
    }
  }

  .select-file {
    width: 5rem;
    height: 5rem;
    border: 1px solid #D6D5D5;
    border-radius: 0.3rem;
    text-align: center;
    position: relative;

    ion-icon {
      height: 4.4rem;
      width: 4.4rem;
      margin: 0.3rem;
    }
  }

  .file-preview {
    position: relative;
    padding-bottom: 0.5rem;
    ion-img {
      width: 5rem;
      height: 5rem;
      border: 1px solid #D6D5D5;
      border-radius: 0.3rem;
      text-align: center;
      position: relative;
      margin-right: 0.5rem;
    }

    ion-icon {
      position: absolute;
      top: 0;
      margin: 0 auto;
      width: 1.3rem;
      height: 1.3rem;
      background: #7a7a7aab;
      color: #FFFFFF;
      border-radius: 0.3rem;
      right: 0.5rem;
    }
  }
}
