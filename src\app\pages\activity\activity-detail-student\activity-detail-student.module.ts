import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { ActivityDetailStudentPage } from './activity-detail-student.page';
import {TranslateModule} from '@ngx-translate/core';
import { InfoLabelModule } from '@app/components/info-label/info-label.module';

const routes: Routes = [
  {
    path: '',
    component: ActivityDetailStudentPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    TranslateModule,
    InfoLabelModule
  ],
  declarations: [ActivityDetailStudentPage]
})
export class ActivityDetailStudentPageModule {}
