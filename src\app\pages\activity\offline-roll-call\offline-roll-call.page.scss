ion-header {
  ion-toolbar {
    border-bottom: 1px solid #a7a7a78f;
  }
  .tabs {
    background: #FFFFFF;
  }
}

ion-content {
  font-size: 0.6rem;
  --background: #FFFFFF;
  --padding-top: 0.5rem;
  --padding-bottom: 1rem;
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  color: #3C3C3C;
  --color: #3C3C3C;
  .activity-title {
    color: #3C3C3C;
    text-align: center;
    padding: 0.5rem 0.5rem;
  }


  table.info-table {
    /* padding: 10px; */
    margin: 0.3rem 0.5rem;
    tr {
      height: 1.3rem;


      td {
        padding: 0 0.5rem;
        white-space: pre-wrap;
      }
    }
  }

  &.vn-content {
    .activity-title {
      color: #2469F2;
    }
  }
  .vn-box {
    .vn-title {
      text-align: center;
    }
    .divide {
      width: 80%;
      min-height: 1px;
      background: #DFDFDF;
    }

    .vn-year {
      background: #F9F9F9F9;
      margin: 1rem 0.5rem;
      border-radius: 0.2rem;
      padding: 0.5rem;


      .year-label {
        font-weight: bold;
        margin: 0 0.1rem;
      }


      .vn-month {
        display: flex;
        justify-content: flex-start;
        flex-direction: row;
        flex-wrap: wrap;
        margin: 0.3rem 0;

        .vn-date {
          background: rgba(255,255,255,1);
          border-radius: 0.2rem;
          border: 1px solid #dfdfdf;
          margin: 0.1rem 0.1rem;
          padding: 0.1rem 0.3rem;
          width: 4.7rem;
          position: relative;
          font-size: 0.6rem;
          .vn-week {
            float: right;
            font-size: 0.4rem;
            padding: 0.2rem 0rem;
          }
          .dot {
            background: RED;
            min-width: 0.2rem;
            min-height: 0.2rem;
            border-radius: 50%;
            position: absolute;
            right: 0.1rem;
            top: 0.1rem;
          }
        }
      }
    }


  }

  .type-select {
    ion-label, ion-button {
      vertical-align: middle;
      margin: 0 0.5rem;
    }
  }






  .class-box {
    background: #F9F9F9F9;
    margin: 0.5rem 0.5rem;
    border-radius: 0.2rem;
    padding: 0.5rem;
    .class-title {
      font-weight: bold;
      margin: 0 0.1rem;
    }
    .stu-box {
      display: flex;
      justify-content: flex-start;
      flex-direction: row;
      flex-wrap: wrap;
      margin: 0.3rem 0;
      .stu-item {
        background: #457DEE;
        border-radius: 0.2rem;
        border: 1px solid #dfdfdf;
        margin: 0.1rem 0.1rem;
        padding: 0.1rem 0.3rem;
        width: 3.7rem;
        position: relative;
        font-size: 0.6rem;
        color: #FFFFFF;
        text-align: center;

        &.agree {
            background: #5CB85C;
        }
        &.not-agree {
            background: #FFAB9F;
        }
      }
    }
  }
  .stu-empty-tips {
    background: #F9F9F9F9;
    margin: 1rem 0.5rem;
    border-radius: 0.2rem;
    padding: 0.5rem;
  }



}

.icon-PDF {
  font-size: 2rem;
  color: #C64A48;
}
