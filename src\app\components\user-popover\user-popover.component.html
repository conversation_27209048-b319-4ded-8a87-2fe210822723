
<ion-list>
  <ion-item button (click)="onUserInfo()">{{ userName }}</ion-item>
  <ion-item button (click)="onSync()">{{ 'USER.SYNC' | translate }}</ion-item>
  <ion-item *ngIf="!isStudent" button (click)="onRollCall()">{{ 'USER.OFFLINE_ROLL_CALL' | translate }}</ion-item>
  <ion-item *ngIf="isStudent" button (click)="onSwitchAccount()">{{ 'USER.SWITCH_ACCOUNT' | translate }}</ion-item>
  <ion-item lines="none" detail="false" button (click)="onLogout()">{{ 'USER.LOGOUT' | translate }}</ion-item>
</ion-list>
