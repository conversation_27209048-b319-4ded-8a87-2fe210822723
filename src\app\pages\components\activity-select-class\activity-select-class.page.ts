import { Component, Input, OnInit } from '@angular/core';
import {ModalController} from '@ionic/angular';
import {ActivitySelectStudentPage} from '@pages/components/activity-select-student/activity-select-student.page';

@Component({
  selector: 'app-activity-select-class',
  templateUrl: './activity-select-class.page.html',
  styleUrls: ['./activity-select-class.page.scss'],
})
export class ActivitySelectClassPage implements OnInit {

  @Input() list: any = []
  constructor(
    private modalCtrl: ModalController,
    ) { }

  ngOnInit() {
    console.log(this.list)
  }

  onClose() {
    this.modalCtrl.dismiss({
      dismissed: true
    });
  }

  onSelectAll() {

    const checked = this.list.every(item => item.students.every(i => i.checked))

    this.list.forEach(item => {
      item.students.forEach(i => i.checked = !checked)
    })
  }

  getIsSelectAll(item) {
    return item.students.length > 0 && item.students.every(i => i.checked)
  }

  getSelectAllClass(item) {
    return this.getIsSelectAll(item) ? '' : 'white'
  }

  getStuNum(item) {
    const num = item.students.filter(i => i.checked).length

    return `已選 ${num} 人`
  }

  itemSelectAll(item, event) {
    event.preventDefault()
    event.stopPropagation()

    const checked = item.students.every(i => i.checked)
    item.students.forEach(i => i.checked = !checked)
  }


  /* 選擇班級 */
  async selectStudent(item) {
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: ActivitySelectStudentPage,
      componentProps: {
        domId: 'activity-select-student-modal',
        list: item.students
      },
      id: 'activity-select-student-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    if (res.data.save) {
      return res.data.activity
    }
    return {}
  }
  
  
  get selectStr() {
    return this.list.every(item => item.students.every(i => i.checked)) ? '取消全選' : '全選'
  }
}
