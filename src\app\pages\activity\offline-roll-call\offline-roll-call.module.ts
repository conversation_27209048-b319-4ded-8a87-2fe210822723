import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { OfflineRollCallPage } from './offline-roll-call.page';
// import { InfoLabelComponent } from '@app/components/info-label/info-label.component';
// import { RollCallOnlinePage } from '@pages/activity/roll-call-online/roll-call-online.page';
import {TranslateModule} from '@ngx-translate/core';
import { InfoLabelModule } from '@app/components/info-label/info-label.module';
// import { RollCallOnlinePageModule } from '@pages/activity/roll-call-online/roll-call-online.module';
import { RollCallOfflineInputPage} from '@pages/activity/roll-call-offline-input/roll-call-offline-input.page';

const routes: Routes = [
  {
    path: '',
    component: OfflineRollCallPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    TranslateModule,
    InfoLabelModule,
    // RollCallOnlinePageModule,
  ],
  declarations: [OfflineRollCallPage, RollCallOfflineInputPage],
  entryComponents: [
    // RollCallOnlinePage
    RollCallOfflineInputPage
  ]
})
export class OfflineRollCallPageModule {}
