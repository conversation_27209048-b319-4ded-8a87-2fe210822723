import { Injectable } from '@angular/core';
import {ModalController} from '@ionic/angular';
import { SelectClassPage } from '@pages/components/select-class/select-class.page';
// import { SelectStudentPage } from '@pages/components/select-student/select-student.page';

@Injectable({
  providedIn: 'root'
})
export class SelectStudentService {

  constructor(
    private modalCtrl: ModalController
  ) { }

  async showClasses() {
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: SelectClassPage,
      componentProps: {
        domId: 'class-select-modal',
      },
      id: 'class-select-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    if (res.data.save) {
      return { save: true, class: res.data.class, student: res.data.student }
    }
    return {}
  }
  // async showStudent(classId) {
  //   const modal = await this.modalCtrl.create({
  //     // enterAnimation: rightEnterAnimation,
  //     // leaveAnimation: rightLeaveAnimation,
  //     component: SelectStudentPage,
  //     componentProps: {
  //       domId: 'student-select-modal',
  //       classId,
  //     },
  //     id: 'student-select-modal'
  //   })
  //   await modal.present()
  //   const res = await modal.onDidDismiss()
  //   if (res.data.save) {
  //     return { save: true, student: res.data.student }
  //   }
  //   return {}
  // }
}
