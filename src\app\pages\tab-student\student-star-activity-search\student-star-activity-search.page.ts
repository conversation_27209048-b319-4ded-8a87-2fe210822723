import {Component, OnInit, ChangeDetectorRef, Input, Output, EventEmitter} from '@angular/core';
import {ActionSheetController, ModalController, NavController, Platform} from '@ionic/angular';
import {ActivatedRoute} from '@angular/router';
import {LoadingService} from '@app/utils/loading.service';
import {StarService} from '@services/api/star.service';
import {StorageService} from '@services/storage/storage.service';
import {NativeStorage} from '@ionic-native/native-storage/ngx';
import {TranslateService} from '@ngx-translate/core';
import {Events} from '@app/services/events/event.service';

@Component({
    selector: 'app-student-star-activity-search',
    templateUrl: './student-star-activity-search.page.html',
    styleUrls: ['./student-star-activity-search.page.scss'],
})
export class StudentStarActivitySearchPage implements OnInit {
    @Input() domId = 'search-activity-modal'
    @Input() year_id: any = ''
    @Input() stu_id: any = ''
    @Input() semester: any = ''
    @Output() public reload = new EventEmitter();

    public data: any = {}
    public selectedData: any = []
    public teachers: any = []
    public activityFilter: any = {
        year_id: this.year_id,
        semester: this.semester,
        stu_id: this.stu_id,
        act_category: '',
        act_name: '',
        act_teacher: '',
        act_lottery: ''
    }

    constructor(
        private platform: Platform,
        private routeInfo: ActivatedRoute,
        public nav: NavController,
        private loadingService: LoadingService,
        public starService: StarService,
        public actionSheetController: ActionSheetController,
        private modalCtrl: ModalController,
        private storageService: StorageService,
        private nativeStorage: NativeStorage,
        public translate: TranslateService,
        protected cdr: ChangeDetectorRef,
        protected events: Events,
    ) {
    }

    ngOnInit() {
        this.platform.ready().then(async () => {
            await this.initTeacher()
            await this.loadData()
        })
    }

    async loadData() {
        await this.loadingService.start()
        try {
            this.activityFilter.year_id = this.year_id
            this.activityFilter.semester = this.semester
            this.activityFilter.stu_id = this.stu_id
            this.activityFilter.act_lottery = 1
            const res: any = await this.starService.fetchStuActivities(this.activityFilter)
            this.data = res
            this.selectedData = []
        } catch (e) {
            console.error(e)
        }

        await this.loadingService.end()
    }

    async initTeacher() {
        try {
            const res: any = await this.starService.fetchTeachers({})
            this.teachers = res
        } catch (e) {
            console.error(e)
        }
    }

    onSearchKeyUp(event) {
        if ('Enter' === event.key) {
            this.loadData()
        }
    }

    onClose(save = false) {
        this.selectedData = []
        this.modalCtrl.dismiss({
            dismissed: true,
            save
        });
    }

    onSave() {
        const returnData = []

        this.data.forEach(item => {
            if (this.selectedData[item.id]) {
                returnData.push({
                    active_id: item.id,
                    actName: item.actName
                })
            }
        })

        console.log(returnData, 'toReturnData')
        this.modalCtrl.dismiss({
            dismissed: true,
            save: true,
            data: returnData
        });
    }
}

