.search-toolbar {
  --background: var(--content-page-background);
  .search-input {
    font-size: 14px;
    text-align: center;
    border-radius: 4px;
    margin: 10px;
    background: #ffffff;
    width: auto;
    border:1px solid rgba(237,237,237,1);
  }
  .row {
    font-size: 13px;
    background: #ffffff;
    ion-select {
      font-size: 13px;
      width: max-content;
      max-width: 100%;
      .tips-option {
        color: #eeeeee;
      }
    }
    ion-checkbox {
      height: auto;
    }
    .checkbox-div {
      vertical-align: middle;
      display: inline-flex;
      padding-top: 10px;
      padding-right: 0;
      padding-bottom: 11px;
      padding-left: 16px;
      ion-label {
        padding-right: 5px;
      }
    }
    ion-col {
      //line-height: 25px;
    }
  }
}

ion-content {
  --background: #FFFFFF;
  ion-list {
    background: #FFFFFF;
  }
}

.list-item {
  margin: 12px;
  box-shadow:1px 1px 3px 0px rgba(0,0,0,0.18);
  border-radius:4px;
  padding-left: 10px;

  background: #457DEE;
  &.success {
    background: #4CC726;
    .item-info .info .status {
      color: #4CC726;
    }
  }
  &.warning {
    background: #FF9900;
    .item-info .info .status {
      color: #FF9900;
    }
  }
  &.error {
    background: #F56C6C;
    .item-info .info .status {
      color: #F56C6C;
    }
  }
  .item-info {
    padding: 5px 0;
    width: 100%;

    word-break: keep-all;
    white-space: nowrap;
    text-overflow: ellipsis;
    .project-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: calc(100% - 10px);
      width: 100%;
      font-weight:600;
      font-size:13px;
      color:rgba(96,98,102,1);
      padding-bottom: 5px;
    }
    .info {
      font-size:11px;
      color:rgba(96,98,102,1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: calc(100% - 5px);
      width: 100%;
      .status {
        color: #457DEE;
      }
      .time {
        float: right;
        color: #B0B0B0;
      }
    }
  }
}
